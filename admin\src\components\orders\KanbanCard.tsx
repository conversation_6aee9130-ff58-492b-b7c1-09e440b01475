import { Order, PAYMENT_METHOD_OPTIONS } from "../../types/order";
import { Card, Typography } from "antd";
import { formatCurrency } from "../../lib/utils";
import { BanknoteIcon, TruckIcon, CreditCard } from "lucide-react";

const { Text } = Typography;

const getStatusColor = (status: Order["status"]) => {
  switch (status) {
    case "pending":
      return "text-yellow-500";
    case "processing":
      return "text-blue-500";
    case "shipped":
      return "text-purple-500";
    case "delivered":
      return "text-green-500";
    case "cancelled":
      return "text-red-500";
    default:
      return "text-gray-500";
  }
};

const PaymentIcon = ({ method }: { method: Order["payment_method"] }) => {
  if (!method) return null;

  switch (method) {
    case "cash":
      return (
        <BanknoteIcon className="w-4 h-4 text-green-500 inline-block mr-1" />
      );
    case "cod":
      return (
        <TruckIcon className="w-4 h-4 text-orange-500 inline-block mr-1" />
      );
    case "bank_transfer":
      return <CreditCard className="w-4 h-4 text-blue-500 inline-block mr-1" />;
    default:
      return null;
  }
};

interface KanbanCardProps {
  order: Order;
}

export function KanbanCard({ order }: KanbanCardProps) {
  const handleClick = () => {
    window.location.href = `/orders/${order.id}`;
  };

  const payment = PAYMENT_METHOD_OPTIONS.find(
    (p) => p.value === order.payment_method
  );

  return (
    <Card
      size="small"
      className="w-full bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleClick}
    >
      {/* Header - Order ID & Total */}
      <div className="flex justify-between items-center mb-3">
        <Text strong>#{order.id}</Text>
        <Text strong className={getStatusColor(order.status)}>
          {formatCurrency(order.final_total)}
        </Text>
      </div>

      {/* Customer Info */}
      <div className="mb-3 space-y-1">
        <div className="flex gap-2">
          <Text type="secondary" className="w-16">
            Tên:
          </Text>
          <Text>{order.user.full_name}</Text>
        </div>
        <div className="flex gap-2">
          <Text type="secondary" className="w-16">
            SĐT:
          </Text>
          <Text>{order.phone_number}</Text>
        </div>
        <div className="flex gap-2">
          <Text type="secondary" className="w-16">
            Địa chỉ:
          </Text>
          <Text className="truncate max-w-32" title={order.shipping_address}>
            {order.shipping_address}, Phường {order.ward}, Quận {order.district}
            , Thành phố {order.city}
          </Text>
        </div>
      </div>

      {/* Products */}
      <div className="mb-3">
        {order.items.slice(0, 3).map((item, index) => (
          <Text key={index} className="block text-xs text-gray-500 truncate">
            {item.product_name} ({item.quantity}x)
          </Text>
        ))}
        {order.items.length > 3 && (
          <Text className="text-xs text-gray-500">...</Text>
        )}
      </div>

      {/* Footer */}
      <div className="text-xs text-gray-500">
        <div className="flex justify-between items-center">
          <Text strong className="text-xs flex items-center">
            <PaymentIcon method={order.payment_method} />
            {payment?.label || "Chưa chọn PT thanh toán"}
          </Text>
          <Text className="text-xs">
            {order.sales_admin?.first_name || "Chưa phân công"}
          </Text>
        </div>
        {order.notes && (
          <Text className="block mt-2 italic truncate" title={order.notes}>
            Ghi chú: {order.notes}
          </Text>
        )}
      </div>
    </Card>
  );
}
