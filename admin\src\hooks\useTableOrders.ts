import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiCall, endpoints } from "../lib/api";
import { Order } from "../types/order";
import { useOrdersBase } from "./useOrdersBase";

interface UseTableOrdersParams {
  userId?: number;
  userRole?: string;
  showroomOnly?: boolean;
}

export function useTableOrders({ userId, userRole, showroomOnly }: UseTableOrdersParams = {}) {
  const [page, setPage] = useState(1);

  const {
    statusFilter,
    paymentStatusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedDeliveryStaff,
    selectedDeliveryStaffs,
    selectedShippingUnit,
    selectedShippingUnits,
    statusCounts,
    staffList,
    handleSearch: baseHandleSearch,
    handleStatusChange: baseHandleStatusChange,
    handleSalesAdminChange,
    handleSalesAdminsChange,
    handleDeliveryStaffChange,
    handleDeliveryStaffsChange,
    handleShippingUnitChange,
    handleShippingUnitsChange,
    handlePaymentStatusFilterChange: baseHandlePaymentStatusFilterChange,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
    updatePaymentStatus,
    updateShowroomStatus,
  } = useOrdersBase({
    userId,
    userRole,
    queryKey: "table_orders",
    noPage: false,
    showroomOnly,
  });

  const { data: ordersData, isLoading } = useQuery<{ results: Order[]; count: number }>({
    queryKey: [
      "table_orders",
      page,
      statusFilter,
      searchParams,
      selectedSalesAdmin?.id,
      selectedSalesAdmins,
      selectedDeliveryStaff?.id,
      selectedDeliveryStaffs,
      selectedShippingUnit,
      selectedShippingUnits,
      paymentStatusFilter,
      showroomOnly
    ],
    queryFn: () => {
      let url = `${endpoints.orders.list}`;
      // Note: The original logic for sales_admin involved appending `/by_sales_admin/`
      // and then adding query parameters. This needs to be handled carefully.
      if (userRole === "sales_admin") {
        url = `${endpoints.orders.list}/by_sales_admin/?id=${userId}&page=${page}&page_size=10`;
      } else {
        url += `?page=${page}&page_size=10`;
      }

      if (statusFilter) {
        url += `&status=${statusFilter}`;
      }
      if (searchParams.queries && searchParams.queries.length > 0) {
        url += `&search=${searchParams.queries.join(',')}&search_by=${searchParams.searchBy}`;
      } else if (searchParams.query) {
        url += `&search=${searchParams.query}&search_by=${searchParams.searchBy}`;
      }
      if (searchParams.dateFrom) {
        url += `&date_from=${searchParams.dateFrom}`;
      }
      if (searchParams.dateTo) {
        url += `&date_to=${searchParams.dateTo}`;
      }
      if (selectedSalesAdmins.length > 0) {
        url += `&sales_admin=${selectedSalesAdmins.join(',')}`;
      } else if (selectedSalesAdmin) {
        url += `&sales_admin=${selectedSalesAdmin.id}`;
      }
      if (selectedDeliveryStaffs.length > 0) {
        url += `&delivery_staff=${selectedDeliveryStaffs.join(',')}`;
      } else if (selectedDeliveryStaff) {
        url += `&delivery_staff=${selectedDeliveryStaff.id}`;
      }
      if (selectedShippingUnits.length > 0) {
        url += `&shipping_unit=${selectedShippingUnits.join(',')}`;
      } else if (selectedShippingUnit) {
        url += `&shipping_unit=${selectedShippingUnit}`;
      }
      if (paymentStatusFilter && paymentStatusFilter.length > 0) {
        url += `&payment_status=${paymentStatusFilter.join(',')}`;
      }

      url += `&is_showroom=${showroomOnly ? 'true': 'false'}`;

      return apiCall("GET", url);
    },
  });

  // Handler functions that reset page
  const handleSearchWrapper = (params: typeof searchParams) => {
    baseHandleSearch(params);
    setPage(1);
  };

  const handleStatusChangeWrapper = (status: string) => { // Parameter type changed to string
    baseHandleStatusChange(status); // baseHandleStatusChange now expects string
    setPage(1);
  };

  const handlePaymentStatusFilterChangeWrapper = (statuses?: Order['payment_status'][]) => {
    baseHandlePaymentStatusFilterChange(statuses);
    setPage(1);
  };

  return {
    // States
    page,
    statusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedDeliveryStaff,
    selectedDeliveryStaffs,
    selectedShippingUnit,
    selectedShippingUnits,
    paymentStatusFilter,

    // Data
    orders: ordersData?.results || [],
    totalCount: ordersData?.count || 0,
    statusCounts,
    staffList,
    isLoading,

    // Actions
    setPage,
    handleSearch: handleSearchWrapper,
    handleStatusChange: handleStatusChangeWrapper,
    handleSalesAdminChange,
    handleSalesAdminsChange,
    handleDeliveryStaffChange,
    handleDeliveryStaffsChange,
    handleShippingUnitChange,
    handleShippingUnitsChange,
    handlePaymentStatusFilterChange: handlePaymentStatusFilterChangeWrapper,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
    updatePaymentStatus,
    updateShowroomStatus,
  };
}
