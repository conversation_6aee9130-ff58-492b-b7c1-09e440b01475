# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 19:21+0000\n"
"Last-Translator: Maredudd ap Gwyndaf <<EMAIL>>\n"
"Language-Team: Welsh (http://www.transifex.com/django/django/language/cy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cy\n"
"Plural-Forms: nplurals=4; plural=(n==1) ? 0 : (n==2) ? 1 : (n != 8 && n != "
"11) ? 2 : 3;\n"

msgid "Humanize"
msgstr "Dynoli"

msgid "th"
msgstr "edd"

msgid "st"
msgstr "af"

msgid "nd"
msgstr "ail"

msgid "rd"
msgstr "ydd"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f miliwn"
msgstr[1] "%(value).1f miliwn"
msgstr[2] "%(value).1f miliwn"
msgstr[3] "%(value).1f miliwn"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s miliwn"
msgstr[1] "%(value)s miliwn"
msgstr[2] "%(value)s miliwn"
msgstr[3] "%(value)s miliwn"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f biliwn"
msgstr[1] "%(value).1f biliwn"
msgstr[2] "%(value).1f biliwn"
msgstr[3] "%(value).1f biliwn"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s biliwn"
msgstr[1] "%(value)s biliwn"
msgstr[2] "%(value)s biliwn"
msgstr[3] "%(value)s biliwn"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f triliwn"
msgstr[1] "%(value).1f triliwn"
msgstr[2] "%(value).1f triliwn"
msgstr[3] "%(value).1f triliwn"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s triliwn"
msgstr[1] "%(value)s triliwn"
msgstr[2] "%(value)s triliwn"
msgstr[3] "%(value)s triliwn"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f cwadriliwn"
msgstr[1] "%(value).1f cwadriliwn"
msgstr[2] "%(value).1f cwadriliwn"
msgstr[3] "%(value).1f cwadriliwn"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s cwadriliwn"
msgstr[1] "%(value)s cwadriliwn"
msgstr[2] "%(value)s cwadriliwn"
msgstr[3] "%(value)s cwadriliwn"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f cwintiliwn"
msgstr[1] "%(value).1f cwintiliwn"
msgstr[2] "%(value).1f cwintiliwn"
msgstr[3] "%(value).1f cwintiliwn"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s cwintiliwn"
msgstr[1] "%(value)s cwintiliwn"
msgstr[2] "%(value)s cwintiliwn"
msgstr[3] "%(value)s cwintiliwn"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f secstiliwnfm"
msgstr[1] "%(value).1f secstiliwnfm"
msgstr[2] "%(value).1f secstiliwnfm"
msgstr[3] "%(value).1f secstiliwnfm"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s secstiliwn"
msgstr[1] "%(value)s secstiliwn"
msgstr[2] "%(value)s secstiliwn"
msgstr[3] "%(value)s secstiliwn"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f septiliwn"
msgstr[1] "%(value).1f septiliwn"
msgstr[2] "%(value).1f septiliwn"
msgstr[3] "%(value).1f septiliwn"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septiliwn"
msgstr[1] "%(value)s septiliwn"
msgstr[2] "%(value)s septiliwn"
msgstr[3] "%(value)s septiliwn"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f octiliwn"
msgstr[1] "%(value).1f octiliwn"
msgstr[2] "%(value).1f octiliwn"
msgstr[3] "%(value).1f octiliwn"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s octiliwn"
msgstr[1] "%(value)s octiliwn"
msgstr[2] "%(value)s octiliwn"
msgstr[3] "%(value)s octiliwn"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f noniliwn"
msgstr[1] "%(value).1f noniliwn"
msgstr[2] "%(value).1f noniliwn"
msgstr[3] "%(value).1f noniliwn"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s noniliwn"
msgstr[1] "%(value)s noniliwn"
msgstr[2] "%(value)s noniliwn"
msgstr[3] "%(value)s noniliwn"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f dengiliwn"
msgstr[1] "%(value).1f dengiliwn"
msgstr[2] "%(value).1f dengiliwn"
msgstr[3] "%(value).1f dengiliwn"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s dengiliwn"
msgstr[1] "%(value)s dengiliwn"
msgstr[2] "%(value)s dengiliwn"
msgstr[3] "%(value)s dengiliwn"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f gwgol"
msgstr[1] "%(value).1f gwgol"
msgstr[2] "%(value).1f gwgol"
msgstr[3] "%(value).1f gwgol"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s gwgol"
msgstr[1] "%(value)s gwgol"
msgstr[2] "%(value)s gwgol"
msgstr[3] "%(value)s gwgol"

msgid "one"
msgstr "un"

msgid "two"
msgstr "dau"

msgid "three"
msgstr "tri"

msgid "four"
msgstr "pedwar"

msgid "five"
msgstr "pump"

msgid "six"
msgstr "chwech"

msgid "seven"
msgstr "saith"

msgid "eight"
msgstr "wyth"

msgid "nine"
msgstr "naw"

msgid "today"
msgstr "heddiw"

msgid "tomorrow"
msgstr "fory"

msgid "yesterday"
msgstr "ddoe"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s ago"
msgstr "%(delta)s yn ôl"

msgid "now"
msgstr "nawr"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "un eiliad yn ôl"
msgstr[1] "%(count)s eiliad yn ôl"
msgstr[2] "%(count)s eiliad yn ôl"
msgstr[3] "%(count)s eiliad yn ôl"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "un munud yn ôl"
msgstr[1] "%(count)s munud yn ôl"
msgstr[2] "%(count)s munud yn ôl"
msgstr[3] "%(count)s munud yn ôl"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "un awr yn ôl"
msgstr[1] "%(count)s awr yn ôl"
msgstr[2] "%(count)s awr yn ôl"
msgstr[3] "%(count)s awr yn ôl"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s from now"
msgstr "%(delta)s o nawr"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "un eiliad o nawr"
msgstr[1] "%(count)s eiliad o nawr"
msgstr[2] "%(count)s eiliad o nawr"
msgstr[3] "%(count)s eiliad o nawr"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "un munud o nawr"
msgstr[1] "%(count)s funud o nawr"
msgstr[2] "%(count)s munud o nawr"
msgstr[3] "%(count)s munud o nawr"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "un awr o nawr"
msgstr[1] "%(count)s awr o nawr"
msgstr[2] "%(count)s awr o nawr"
msgstr[3] "%(count)s awr o nawr"
