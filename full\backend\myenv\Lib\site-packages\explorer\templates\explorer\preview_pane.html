{% load i18n %}

{% if headers %}
<div class="container mt-4">
    <nav>
        <div class="nav nav-tabs" role="tablist" id="nav-tab">
            <button class="nav-link active" id="nav-preview-tab" data-bs-toggle="tab" data-bs-target="#nav-preview" type="button" role="tab" area-controls="nav-preview" aria-selected="true">{% translate "Preview" %}</button>
            {% if query.id and query.snapshot %}
                <button class="nav-link" id="nav-snapshots-tab" data-bs-toggle="tab" data-bs-target="#nav-snapshots" type="button" role="tab" area-controls="nav-snapshots" aria-selected="false">{% translate "Snapshots" %}</button>
            {% endif %}
            {% if data %}
                <button class="nav-link" id="nav-pivot-tab" data-bs-toggle="tab" data-bs-target="#nav-pivot" type="button" role="tab" area-controls="nav-pivot" aria-selected="false">{% translate "Pivot" %}</button>
                {% if charts_enabled and line_chart_svg %}
                    <button class="nav-link" id="nav-linechart-tab" data-bs-toggle="tab" data-bs-target="#nav-linechart" type="button" role="tab" area-controls="nav-linechart" aria-selected="false">{% translate "Line Chart" %}</button>
                    <button class="nav-link" id="nav-barchart-tab" data-bs-toggle="tab" data-bs-target="#nav-barchart" type="button" role="tab" area-controls="nav-barchart" aria-selected="false">{% translate "Bar Chart" %}</button>
                {% endif %}
            {% endif %}
        </div>
    </nav>
    <div class="tab-content" id="nav-tabContent">
        <div class="tab-pane show active" id="nav-preview" role="tabpanel" area-labelledby="nav-preview-tab">
            <div role="tabpanel" class="tab-pane active" id="previewpane">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col">
                                {% if data %}
                                    <a title="Show row numbers" id="counter-toggle" href="#"><i class="bi-hash"></i></a>&nbsp
                                {% endif %}
                                {% blocktranslate trimmed with duration=duration|floatformat:2 %}
                                    Execution time: {{ duration }} ms
                                {% endblocktranslate %}
                            </div>
                            <div class="col text-end">
                                <span class="me-1">
                                    {% if rows > total_rows %}
                                        {% translate "Showing" %}&nbsp;
                                        <input class="rows-input" type="text" name="rows" id="rows"
                                               value="{{ total_rows }}"/>
                                    {% else %}
                                        {% translate "First" %}&nbsp;
                                        <input class="rows-input" type="text" name="rows" id="rows"
                                               value="{{ rows }}"/>
                                    {% endif %}
                                    {% blocktranslate %}of {{ total_rows }} total rows.{% endblocktranslate %}
                                </span>
                                <a id="fullscreen" href="./?{{ fullscreen_params }}" target="_blank"
                                   title="Fullscreen results">
                                    <i class="bi-arrows-angle-expand"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="overflow-wrapper">
                            <table class="table table-striped table-hover" id="preview">
                                <thead class="data-headers">
                                    <tr>
                                        <th class="preview-header counter" style="display: none;"></th>
                                        {% for h in headers %}
                                            <th class="preview-header"><span><i class="sort bi-chevron-expand pe-1" data-sort="{{ forloop.counter0 }}"
                                                         data-dir="asc"></i>{{ h }}{% if h.summary %}<i class="stats-expand bi-calculator text-info ps-1"></i>{% endif %}</span></th>
                                        {% endfor %}
                                    </tr>
                                    {% if has_stats %}
                                        <tr class="stats-th">
                                            <th class="counter" style="display: none;"></th>
                                            {% for h in headers %}
                                                <th>
                                                    {% if h.summary %}
                                                        <table class="stats-wrapper table table-sm fw-normal small" style="display: none;">
                                                            {% for label, value in h.summary.stats.items %}
                                                                <tr>
                                                                    <td class="text-info">{{ label }}</td>
                                                                    <td>{{ value }}</td>
                                                                </tr>
                                                            {% endfor %}
                                                        </table>
                                                    {% endif %}
                                                </th>
                                            {% endfor %}
                                        </tr>
                                    {% endif %}
                                </thead>
                                <tbody class="list">
                                    {% if data %}
                                        {% for row in data %}
                                        <tr class="data-row">
                                            <td class="counter" style="display: none;">{{ forloop.counter0 }}</td>
                                            {% for i in row %}
                                                {% if unsafe_rendering %}
                                                    <td class="{{ forloop.counter0 }}">
                                                        {% autoescape off %}{{ i }}{% endautoescape %}
                                                    </td>
                                                {% else %}
                                                    <td class="{{ forloop.counter0 }}">{{ i }}</td>
                                                {% endif %}
                                            {% endfor %}
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr class="text-center">
                                            <td colspan="{{ headers|length }}">
                                                {% translate "Empty Resultset" %}
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% if query.id and query.snapshot and query.snapshots %}
            <div class="tab-pane" id="nav-snapshots" role="tabpanel" area-labelledby="nav-snapshots-tab">
                <h3>{{ snapshots|length }} Snapshots <small>(oldest first)</small></h3>
                <div class="overflow-wrapper">
                    <p>
                    <ul>
                        {% for s in snapshots %}
                            <li><a href='{{ s.url }}'>{{ s.last_modified }}</a></li>
                        {% endfor %}
                    </ul>
                    </p>
                </div>
            </div>
        {% endif %}

        {% if data %}
            <div class="tab-pane" id="nav-pivot" role="tabpanel" area-labelledby="nav-pivot-tab">
                <div class="card p-3">
                    <ul class="nav justify-content-end">
                        <li class="nav-item">
                            <a id="pivot-bookmark" class="nav-link"
                           data-baseurl="{% url 'explorer_playground' %}?querylog_id={{ ql_id }}"
                           href="#">
                            <i class="bi-link"></i> Link to this
                        </a>
                        </li>
                        <li class="nav-item">
                            <a id="button-excel" class="nav-link" href="#"><i class="bi-download"></i> Download CSV</a>
                         </li>
                    </ul>
                    <div class="overflow-wrapper">
                        <div class="pivot-table"></div>
                    </div>
                </div>
            </div>
            {% if charts_enabled and line_chart_svg %}
                <div class="tab-pane" id="nav-linechart" role="tabpanel" area-labelledby="nav-linechart-tab">
                    <div class="overflow-wrapper">
                        <div style="margin: 2em;">
                            {{ line_chart_svg | safe }}
                        </div>
                    </div>
                </div>
                <div class="tab-pane" id="nav-barchart" role="tabpanel" area-labelledby="nav-barchart-tab">
                    <div class="overflow-wrapper">
                        <div style="margin: 2em;">
                            {{ bar_chart_svg | safe }}
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>
{% endif %}
