# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# Nicat <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-08-07 20:19+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Azerbaijani (http://app.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administrativ Dokumentasiya"

msgid "Home"
msgstr "Ev"

msgid "Documentation"
msgstr "Dokumentasiya"

msgid "Bookmarklets"
msgstr "Bukmarkletlər"

msgid "Documentation bookmarklets"
msgstr "Dokumentasiya bukmarkletləri"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Bukmarkletləri quraşdırmaq üçün linki əlfəcin alətləri panelinizə sürükləyin "
"və ya linki sağ klikləyin və onu əlfəcinlərinizə əlavə edin. İndi siz saytın "
"istənilən səhifəsindən bukmarklet seçə bilərsiniz."

msgid "Documentation for this page"
msgstr "Bu səhifənin dokumentasiyası"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Hər hansı səhifəni əmələ gətirən funksiyanın dokumentasiyasını göstərir."

msgid "Tags"
msgstr "Teqlər"

msgid "List of all the template tags and their functions."
msgstr "Bütün şablon teqləri və onların funksiyaları."

msgid "Filters"
msgstr "Filterlər"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filterlər şablondakı dəyişənlərə nəticəni dəyişmək üçün tətbiq oluna bilən "
"əməliyyatlarıdr."

msgid "Models"
msgstr "Modellər"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modellər sistemdəki bütün obyektlərin və onlarla əlaqəli xanaların "
"təsviridir. Hər bir modeldə şablon dəyişənləri kimi istifadə oluna bilən "
"xanaların siyahısı var"

msgid "Views"
msgstr "Görüntülər"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"İctimai saytdakı hər bir səhifə bir görüntü tərəfindən yaradılıb. Görüntü "
"səhifəni yaratmaq üçün hansı şablondan istifadə olunacağını və həmin şablon "
"üçün hansı obyektlərin mövcud olduğunu müəyyən edir."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Admin funksionallığına tez çatmağa kömək edən səyyah alətləri."

msgid "Please install docutils"
msgstr "Lütfən \"docutils\"i quraşdırın"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Admin dokumentasiyası üçün Python-un <a href=\"%(link)s\">docutils</a> "
"kitabxanası tələb olunur."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Lütfən administrasiyanızdan <a href=\"%(link)s\">docutils</a>in qurulmasını "
"istəyin."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Sahələr"

msgid "Field"
msgstr "Sahə"

msgid "Type"
msgstr "Növ"

msgid "Description"
msgstr "Təsvir"

msgid "Methods with arguments"
msgstr "Arqumentli metodlar"

msgid "Method"
msgstr "Metod"

msgid "Arguments"
msgstr "Arqumentlər"

msgid "Back to Model documentation"
msgstr "Model dokumentasiyaına qayıt"

msgid "Model documentation"
msgstr "Model dokumentasiyası"

msgid "Model groups"
msgstr "Model qrupları"

msgid "Templates"
msgstr "Şablonlar"

#, python-format
msgid "Template: %(name)s"
msgstr "Şablon: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Şablon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "<q>%(name)s</q> şablonu üçün axtarış yolu:"

msgid "(does not exist)"
msgstr "(mövcud deyil)"

msgid "Back to Documentation"
msgstr "Dokumentasiyaya qayıt"

msgid "Template filters"
msgstr "Şablon filterləri"

msgid "Template filter documentation"
msgstr "Şablon filter dokumentasiyası"

msgid "Built-in filters"
msgstr "Qurlu filterlər"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Bu filterləri istifadə etmək üçün, şablonun əvvəlinə bunu qoyun: "
"<code>%(code)s</code> "

msgid "Template tags"
msgstr "Şablon teqləri"

msgid "Template tag documentation"
msgstr "Şablon teq dokumentasiyası"

msgid "Built-in tags"
msgstr "Qurulu teqlər"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Bu teqləri istifadə etmək üçün, şablonun əvvəlinə bunu qoyun: "
"<code>%(code)s</code>"

#, python-format
msgid "View: %(name)s"
msgstr "Bax: %(name)s"

msgid "Context:"
msgstr "Məzmun:"

msgid "Templates:"
msgstr "Şablonlar:"

msgid "Back to View documentation"
msgstr "Görüntü dokumentasiyasına qayıdın"

msgid "View documentation"
msgstr "Görüntü dokumentasiyası"

msgid "Jump to namespace"
msgstr "Ad sahəsinə keç"

msgid "Empty namespace"
msgstr "Boş ad sahəsi"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Ad sahəsinə görə görünüşlər %(name)s"

msgid "Views by empty namespace"
msgstr "Boş ad sahələrinə görə görünüşlər"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Funksiyaya bax: <code>%(full_name)s</code>. Ad: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "teq:"

msgid "filter:"
msgstr "süzgəc:"

msgid "view:"
msgstr "baxış:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r tətbiqi tapılmadı"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(model_name)r modelini %(app_label)r tətbiqetməsində tapılmadı"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "bağlı \"%(app_label)s.%(data_type)s\" obyekti"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "bağlı \"%(app_label)s.%(object_name)s\" obyektləri"

#, python-format
msgid "all %s"
msgstr "bütün %s"

#, python-format
msgid "number of %s"
msgstr "%s sayı"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s urlpattern obyekti deyil"
