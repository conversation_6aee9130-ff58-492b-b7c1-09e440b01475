# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Georgian (http://www.transifex.com/django/django/language/"
"ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Sites"
msgstr "საიტები"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "დომენის სახელი არ შეიძლება შეიცავდეს ჰარებს ან ტაბულაციებს."

msgid "domain name"
msgstr "დომენის სახელი"

msgid "display name"
msgstr "საჩვენებელი სახელი"

msgid "site"
msgstr "საიტი"

msgid "sites"
msgstr "საიტები"
