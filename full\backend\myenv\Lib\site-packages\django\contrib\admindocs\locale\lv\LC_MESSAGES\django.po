# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-25 22:44+0000\n"
"Last-Translator: NullIsNot0 <<EMAIL>>\n"
"Language-Team: Latvian (http://www.transifex.com/django/django/language/"
"lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

msgid "Administrative Documentation"
msgstr "Administrācijas dokumentācija"

msgid "Home"
msgstr "Sākums"

msgid "Documentation"
msgstr "Dokumentācija"

msgid "Bookmarklets"
msgstr "Grāmatzīmes"

msgid "Documentation bookmarklets"
msgstr "Dokumentācijas grāmatzīmes"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Lai instalētu bukmarkletu, velciet saiti uz grāmatzīmju rīkjoslu, vai ar "
"peles labo pogu noklikšķiniet uz saites un pievienojiet to savām "
"grāmatzīmēm. Tagad jūs varat izvēlēties bukmarkletu no jebkuras vietnes "
"lapas."

msgid "Documentation for this page"
msgstr "Dokumentācija šai lapai"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Pārvieto no jebkuras lapas uz tā skata dokumentāciju, kas ģenerē šo lapu."

msgid "Tags"
msgstr "Tagi"

msgid "List of all the template tags and their functions."
msgstr "Visu šablonu tagu un to funkciju saraksts."

msgid "Filters"
msgstr "Filtri"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtri ir darbības, kuras var tikt pielietotas šablona mainīgajiem, lai "
"mainītu izvades rezultātu."

msgid "Models"
msgstr "Modeļi"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeļi ir visu sistēmas objektu un to saistīto lauku apraksti. Katram "
"modelim ir saraksts ar laukiem, kuriem var piekļūt kā šablonu mainīgajiem"

msgid "Views"
msgstr "Skati"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Katru lapu publiskajā vietnē ģenerē skats. Skats nosaka, kuru šablonu "
"izmanto lapas ģenerēšanai un kādi objekti ir pieejami šim šablonam."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Rīki Jūsu pārlūkprogrammai, lai ātri piekļūtu administratora "
"funkcionalitātei."

msgid "Please install docutils"
msgstr "Lūdzu instalējiet 'docutils'"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Administratora dokumentācijas sistēmai ir nepieciešama Python <a href="
"\"%(link)s\">docutils</a> bibliotēka."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Palūdziet administratoram instalēt <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelis: %(name)s"

msgid "Fields"
msgstr "Lauki"

msgid "Field"
msgstr "Lauks"

msgid "Type"
msgstr "Tips"

msgid "Description"
msgstr "Apraksts"

msgid "Methods with arguments"
msgstr "Metodes ar parametriem"

msgid "Method"
msgstr "Metode"

msgid "Arguments"
msgstr "Parametrs"

msgid "Back to Model documentation"
msgstr "Atpakaļ uz Modeļu dokumentāciju"

msgid "Model documentation"
msgstr "Modeļu dokumentācija"

msgid "Model groups"
msgstr "Modeļu grupas"

msgid "Templates"
msgstr "Šabloni"

#, python-format
msgid "Template: %(name)s"
msgstr "Šablons: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Šablons: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Šablona <q>%(name)s</q> ceļš:"

msgid "(does not exist)"
msgstr "(neeksistē)"

msgid "Back to Documentation"
msgstr "Atpakaļ pie Dokumentācijas"

msgid "Template filters"
msgstr "Šablona filtri"

msgid "Template filter documentation"
msgstr "Šablona filtru dokumentācija"

msgid "Built-in filters"
msgstr "Iebūvētie filtri"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Lai izmantotu šos filtrus, ievietojiet <code>%(code)s</code> šablonā pirms "
"filtra izmantošanas."

msgid "Template tags"
msgstr "Šablonu tagi"

msgid "Template tag documentation"
msgstr "Šablonu tagu dokumentācija"

msgid "Built-in tags"
msgstr "Iebūvētie tagi"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Lai izmantotu šos tagus, ievietojiet <code>%(code)s</code> šablonā pirms "
"taga izmantošanas."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Konteksts:"

msgid "Templates:"
msgstr "Šabloni:"

msgid "Back to View documentation"
msgstr "Atpakaļ pie Skatu dokumentācijas"

msgid "View documentation"
msgstr "Skatu dokumentācija"

msgid "Jump to namespace"
msgstr "Pāriet uz vārdtelpu"

msgid "Empty namespace"
msgstr "Tukša vārdtelpa"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Skati pēc vārdtelpas %(name)s"

msgid "Views by empty namespace"
msgstr "Skati pēc tukšas vārdtelpas"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Skata funkcija: <code>%(full_name)s</code>. Nosaukums: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tags:"

msgid "filter:"
msgstr "filtrs:"

msgid "view:"
msgstr "skats:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Lietotne %(app_label)r netika atrasta"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modelis  %(model_name)r lietotnē %(app_label)r nav atrasts"

msgid "model:"
msgstr "modelis:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "saistītais `%(app_label)s.%(data_type)s` objekts"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "saistītie `%(app_label)s.%(object_name)s` objekti"

#, python-format
msgid "all %s"
msgstr "visi %s"

#, python-format
msgid "number of %s"
msgstr "%s skaits"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nav urlpattern objekts"
