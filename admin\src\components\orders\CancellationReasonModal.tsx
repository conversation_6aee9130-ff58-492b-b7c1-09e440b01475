import React, { useState } from "react";
import { Modal, Input, Button, Typography, Space } from "antd";

const { TextArea } = Input;
const { Text } = Typography;

interface CancellationReasonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  title?: string;
}

export function CancellationReasonModal({
  isOpen,
  onClose,
  onConfirm,
  title = "Lý do huỷ đơn hàng",
}: CancellationReasonModalProps) {
  const [reason, setReason] = useState<string>("");

  const handleConfirm = () => {
    if (reason.trim()) {
      onConfirm(reason.trim());
      resetForm();
    }
  };

  const handleCancel = () => {
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setReason("");
  };

  return (
    <Modal
      title={title}
      open={isOpen}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
          disabled={!reason.trim()}
          danger
        >
          Xác nhận huỷ đơn
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: "100%" }} size="large">
        <div>
          <Text strong>Vui lòng nhập lý do huỷ đơn hàng</Text>
          <TextArea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Nhập lý do huỷ đơn hàng..."
            rows={4}
            style={{ marginTop: 8 }}
          />
          {!reason.trim() && (
            <Text type="danger" style={{ display: "block", marginTop: 4 }}>
              Bắt buộc nhập lý do huỷ đơn hàng
            </Text>
          )}
        </div>
      </Space>
    </Modal>
  );
}
