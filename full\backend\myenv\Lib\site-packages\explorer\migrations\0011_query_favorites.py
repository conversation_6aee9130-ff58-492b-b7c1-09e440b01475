# Generated by Django 3.2.16 on 2023-01-12 18:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('explorer', '0010_sql_required'),
    ]

    operations = [
        migrations.CreateModel(
            name='QueryFavorite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='explorer.query')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('query', 'user')},
            },
        ),
    ]
