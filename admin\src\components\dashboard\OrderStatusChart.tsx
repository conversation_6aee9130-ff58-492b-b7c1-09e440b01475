import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { STATUS_OPTIONS } from "@/types/order";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Axis,
} from "recharts";

interface OrderStatusData {
  status: string;
  count: number;
}

interface OrderStatusChartProps {
  data: OrderStatusData[];
}

const statusColors: { [key: string]: string } = {
  pending: "#f59e0b",
  processing: "#3b82f6",
  shipped: "#10b981",
  delivered: "#22c55e",
  cancelled: "#ef4444",
  returned: "#6b7280",
  refunded: "#9333ea",
};

export function OrderStatusChart({ data }: OrderStatusChartProps) {
  const formattedData = data.map((item) => ({
    ...item,
    label: STATUS_OPTIONS.find((opt) => opt.value === item.status)?.label || item.status,
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Trạng thái đơn hàng</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={formattedData}>
              <XAxis
                dataKey="label"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length) {
                    const data = payload[0];
                    const status = (data.payload as OrderStatusData & { label: string });
                    return (
                      <div className="rounded-lg border bg-white p-2 shadow-sm">
                        <div className="flex flex-col">
                          <span className="text-[0.70rem] uppercase text-muted-foreground">
                            {status.label}
                          </span>
                          <span className="font-bold text-xl">
                            {status.count} đơn
                          </span>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Bar
                dataKey="count"
                fill="currentColor"
                className="fill-primary"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
