import{L as c}from"./index.5.3.js";import"./_commonjsHelpers.5.3.js";function l(){window.parent.document.getElementById("schema_frame").classList.contains("no-autofocus")||document.querySelector(".search").focus()}function s(){let n={valueNames:["app-name"],handlers:{updated:[l]}};new c("schema-contents",n),document.getElementById("collapse_all").addEventListener("click",function(){document.querySelectorAll(".schema-table").forEach(function(e){e.style.display="none"})}),document.getElementById("expand_all").addEventListener("click",function(){document.querySelectorAll(".schema-table").forEach(function(e){e.style.display=""})}),document.querySelectorAll(".schema-header").forEach(function(e){e.addEventListener("click",function(){let t=this.parentElement.querySelector(".schema-table");t.style.display==="none"||t.style.display===""?t.style.display="block":t.style.display="none"})}),document.querySelectorAll(".copyable").forEach(function(e){e.addEventListener("click",()=>{navigator.clipboard.writeText(e.innerHTML);let t=e.innerHTML;e.innerHTML="Copied",setTimeout(()=>e.innerHTML=t,1e3)})})}export{s as setupSchema};
