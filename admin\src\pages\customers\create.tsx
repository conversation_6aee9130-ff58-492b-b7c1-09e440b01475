import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { apiCall, endpoints } from "../../lib/api";
import { slugify } from "../../lib/utils";
import type {
  CreateCustomerData,
  CreatedCustomerResponse,
} from "../../types/customer";
import { useToast } from "../../context/toast-hooks";
import { CustomerForm } from "../../components/customers/CustomerForm";
import { CustomerCreatedSuccess } from "../../components/customers/CustomerCreatedSuccess";
import { useAuth } from "@/context/auth-hooks";

export default function CreateCustomerPage() {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [loading, setLoading] = useState(false);
  const [createdCustomer, setCreatedCustomer] =
    useState<CreatedCustomerResponse | null>(null);

  const { user } = useAuth();

  const [formData, setFormData] = useState<CreateCustomerData>({
    email: "",
    first_name: "",
    last_name: "",
    phone_number: "",
    shipping_address: "",
    ward: "",
    district: "",
    city: "",
    creator_id: user?.id || 0,
  });

  const generateRandomEmail = (firstName: string, lastName: string) => {
    const timestamp = Date.now().toString().slice(-6);
    const base = slugify(`${firstName}-${lastName}`);
    return `${base}-${timestamp}@placeholder.com`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      const dataToSubmit = { ...formData };

      if (!dataToSubmit.email) {
        dataToSubmit.email = generateRandomEmail(
          dataToSubmit.first_name,
          dataToSubmit.last_name
        );
      }

      const response = await apiCall<CreatedCustomerResponse>(
        "POST",
        endpoints.customers.createCustomer,
        dataToSubmit
      );
      setCreatedCustomer(response);
      showToast("Tạo khách hàng thành công", "success");
    } catch (error) {
      console.error("Không thể tạo khách hàng:", error);
      showToast("Không thể tạo khách hàng", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  if (createdCustomer) {
    return (
      <CustomerCreatedSuccess
        customer={createdCustomer}
        onViewCustomer={() => navigate(`/customers/${createdCustomer.user.id}`)}
        onCreateAnother={() => {
          setCreatedCustomer(null);
          setFormData({
            email: "",
            first_name: "",
            last_name: "",
            phone_number: "",
            shipping_address: "",
            ward: "",
            district: "",
            city: "",
            creator_id: user?.id || 0,
          });
        }}
      />
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Tạo khách hàng</h1>
        <button
          onClick={() => navigate("/customers")}
          className="text-gray-600 hover:text-gray-800"
        >
          ← Trở về danh sách khách hàng
        </button>
      </div>

      <CustomerForm
        formData={formData}
        onChange={handleInputChange}
        onSubmit={handleSubmit}
        loading={loading}
      />
    </div>
  );
}
