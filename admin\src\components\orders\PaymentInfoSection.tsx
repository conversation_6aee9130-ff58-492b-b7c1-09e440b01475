import { useState, useEffect } from "react";
import {
  Order,
  PAYMENT_METHOD_OPTIONS,
  PAYMENT_STATUS_OPTIONS,
} from "../../types/order";
import { Select, Checkbox } from "antd";

interface PaymentInfoSectionProps {
  order: Order;
  onUpdateOrder: (data: Partial<Order>) => void;
  disabled?: boolean;
}

export function PaymentInfoSection({
  order,
  onUpdateOrder,
  disabled = false,
}: PaymentInfoSectionProps) {
  // Local state for debouncing
  const [paymentMethod, setPaymentMethod] = useState(order.payment_method);
  const [paymentStatus, setPaymentStatus] = useState(order.payment_status);
  const [companyPaymentReceived, setCompanyPaymentReceived] = useState(
    order.company_payment_received
  );

  // Sync local state if order prop changes externally
  useEffect(() => {
    setPaymentMethod(order.payment_method);
    setPaymentStatus(order.payment_status);
    setCompanyPaymentReceived(order.company_payment_received);
  }, [
    order.payment_method,
    order.payment_status,
    order.company_payment_received,
  ]);

  // Debounce effect for payment method
  useEffect(() => {
    if (paymentMethod === order.payment_method) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ payment_method: paymentMethod });
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [paymentMethod, order.payment_method, onUpdateOrder]);

  // Debounce effect for payment status
  useEffect(() => {
    if (paymentStatus === order.payment_status) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ payment_status: paymentStatus });
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [paymentStatus, order.payment_status, onUpdateOrder]);

  // Debounce effect for company payment received
  useEffect(() => {
    if (companyPaymentReceived === order.company_payment_received) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ company_payment_received: companyPaymentReceived });
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [companyPaymentReceived, order.company_payment_received, onUpdateOrder]);
  return (
    <div className="bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-3">Thông tin thanh toán</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Phương thức thanh toán
          </label>
          <Select
            value={paymentMethod || undefined}
            onChange={(value) =>
              setPaymentMethod(value as Order["payment_method"])
            }
            className="w-full"
            disabled={disabled}
            placeholder="Chọn phương thức thanh toán"
            options={PAYMENT_METHOD_OPTIONS as any}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Trạng thái thanh toán
          </label>
          <Select
            value={paymentStatus}
            onChange={(value) =>
              setPaymentStatus(value as Order["payment_status"])
            }
            className="w-full"
            disabled={disabled}
            options={PAYMENT_STATUS_OPTIONS as any}
          />
        </div>

        <div>
          <Checkbox
            checked={companyPaymentReceived}
            onChange={(e) => setCompanyPaymentReceived(e.target.checked)}
            disabled={disabled}
          >
            <span className="text-sm font-medium">
              Công ty đã nhận được thanh toán
            </span>
          </Checkbox>
        </div>
      </div>
    </div>
  );
}
