import{g as p}from"./csrf.5.3.js";function h(){var o=document.getElementById("drop-area"),c=document.getElementById("fileElem"),l=document.getElementById("progress-bar"),i=document.getElementById("upload-status");o&&(o.onclick=function(){c.click()},o.addEventListener("dragover",function(e){e.preventDefault(),o.classList.add("bg-info")}),o.addEventListener("dragleave",function(e){o.classList.remove("bg-info")}),o.addEventListener("drop",function(e){e.preventDefault(),o.classList.remove("bg-info");let r=e.dataTransfer.files;r.length&&u(r[0])}),c.onchange=function(){this.files.length&&u(this.files[0])});function u(e){m(e)}function m(e){let r=new FormData;r.append("file",e);let t=document.getElementById("append"),d=t.value;d&&r.append("append",d);let n=new XMLHttpRequest;n.open("POST",`${window.baseUrlPath}connections/upload/`,!0),n.setRequestHeader("X-CSRFToken",p()),n.upload.onprogress=function(a){if(a.lengthComputable){let s=a.loaded/a.total*100;l.style.width=s+"%",l.setAttribute("aria-valuenow",s),l.innerHTML=s.toFixed(0)+"%",s>99&&(i.innerHTML="Upload complete. Parsing and saving to S3...")}},n.onload=function(){if(n.status===200){let a=d?t.options[t.selectedIndex].text:e.name.substring(0,e.name.lastIndexOf("."))||e.name;window.location.href=`../?highlight=${encodeURIComponent(a)}`}else console.error("Error:",n.response),i.innerHTML=n.response},n.onerror=function(){console.error("Error:",n.statusText),i.innerHTML=n.response},n.send(r)}let f=document.getElementById("test-connection-btn");f&&f.addEventListener("click",function(){let e=document.getElementById("db-connection-form"),r=new FormData(e);fetch(`${window.baseUrlPath}connections/validate/`,{method:"POST",body:r,headers:{"X-CSRFToken":p()}}).then(t=>t.json()).then(t=>{t.success?alert("Connection successful!"):alert("Connection failed: "+t.error)}).catch(t=>console.error("Error:",t))})}export{h as setupUploads};
