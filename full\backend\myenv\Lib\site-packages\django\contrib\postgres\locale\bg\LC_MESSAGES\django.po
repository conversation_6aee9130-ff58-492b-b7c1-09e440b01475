# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2022-2023
# <PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <v<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-12-04 09:22+0000\n"
"Last-Translator: arneatec <<EMAIL>>, 2022-2023\n"
"Language-Team: Bulgarian (http://app.transifex.com/django/django/language/"
"bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL разширения"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Елемент %(nth)s в масива не се валидира:"

msgid "Nested arrays must have the same length."
msgstr "Вложените масиви трябва да имат еднаква дължина."

msgid "Map of strings to strings/nulls"
msgstr "Мап от стрингове към стрингове/null-ове"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Стойността на “%(key)s” не е стринг или null."

msgid "Could not load JSON data."
msgstr "Не можа да зареди JSON данни."

msgid "Input must be a JSON dictionary."
msgstr "Входните данни трябва да са JSON речник."

msgid "Enter two valid values."
msgstr "Въведете две валидни стойности."

msgid "The start of the range must not exceed the end of the range."
msgstr "Началото на обхвата не трябва да превишава края му."

msgid "Enter two whole numbers."
msgstr "Въведете две цели числа."

msgid "Enter two numbers."
msgstr "Въведете две числа."

msgid "Enter two valid date/times."
msgstr "Въведете две валидни дати/времена."

msgid "Enter two valid dates."
msgstr "Въведете две коректни дати."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Списъкът съдържа %(show_value)d елемент, а трябва да има не повече от "
"%(limit_value)d."
msgstr[1] ""
"Списъкът съдържа %(show_value)d елемента, а трябва да има не повече от "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Списъкът съдържа %(show_value)d елемент, а трябва да има поне "
"%(limit_value)d."
msgstr[1] ""
"Списъкът съдържа %(show_value)d елемента, а трябва да има поне "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Някои ключове липсват: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Бяха предоставени някои неизвестни ключове: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Уверете се, че горната граница на диапазона не е по-голяма от "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Уверете се, че горната граница на диапазона не е по-малка от %(limit_value)s."
