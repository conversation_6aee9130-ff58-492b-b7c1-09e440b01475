import { useState, useEffect, useRef } from "react";
import { Order } from "../../types/order";

interface NotesSectionProps {
  order: Order;
  onUpdateOrder: (data: Partial<Order>) => void;
  disabled?: boolean;
}

export function NotesSection({
  order,
  onUpdateOrder,
  disabled = false,
}: NotesSectionProps) {
  // Local state for notes
  const [localNotes, setLocalNotes] = useState(order.notes || "");

  // Use a ref to track the initial render
  const initialRenderRef = useRef(true);

  // Use a ref for the debounce timer
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Sync with order.notes when it changes from external sources
  useEffect(() => {
    // Skip the initial render since we already set the initial value
    if (initialRenderRef.current) {
      initialRenderRef.current = false;
      return;
    }

    // Only update local state if it's different from the current order notes
    if (order.notes !== localNotes) {
      setLocalNotes(order.notes || "");
    }
  }, [order.notes]);

  // Handle notes change with debouncing
  const handleNotesChange = (value: string) => {
    // Update local state immediately for UI responsiveness
    setLocalNotes(value);

    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // Set a new timer for debouncing
    timerRef.current = setTimeout(() => {
      // Only update if the value is different from the current order notes
      if (value !== order.notes) {
        onUpdateOrder({ notes: value });
      }
    }, 500);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return (
    <div className="bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-3">Ghi chú</h3>
      <textarea
        value={localNotes}
        onChange={(e) => handleNotesChange(e.target.value)}
        rows={4}
        className="w-full border rounded p-2"
        placeholder="Nhập ghi chú cho đơn hàng"
        disabled={disabled}
      />
    </div>
  );
}
