import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { apiCall, endpoints } from "@/lib/api";
import { Customer, CustomerStats, CustomerOrderSummary } from "@/types/customer";
import { CustomerEditDialog } from "@/components/customers/CustomerEditDialog";
import { CustomerNotes } from "@/components/customers/CustomerNotes";
import { CustomerRankSection } from "@/components/customers/CustomerRankSection";
import { CustomerRankBadge } from "@/components/customers/CustomerRankBadge";
import { formatCurrency } from "@/lib/utils";

type TabType = "info" | "rank" | "orders" | "notes" | "analytics";

export default function CustomerDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabType>("info");
  const [showEditDialog, setShowEditDialog] = useState(false);

  const { data: customer, isLoading: isLoadingCustomer } = useQuery<Customer>({
    queryKey: ["customer", id],
    queryFn: () => apiCall("GET", endpoints.customers.detail(Number(id))),
  });

  const { data: stats } = useQuery<CustomerStats>({
    queryKey: ["customer-stats", id],
    queryFn: () => apiCall("GET", endpoints.customers.stats(Number(id))),
  });

  const { data: orders } = useQuery<CustomerOrderSummary[]>({
    queryKey: ["customer-orders", id],
    queryFn: () => apiCall("GET", endpoints.customers.orders(Number(id))),
  });

  if (isLoadingCustomer) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Không tìm thấy khách hàng
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold">{customer.first_name} {customer.last_name}</h1>
              <CustomerRankBadge
                rank={stats?.rank_info?.current_rank || customer.profile.rank || 'normal'}
                size="default"
              />
            </div>
            <p className="text-muted-foreground">{customer.email}</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setShowEditDialog(true)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Sửa thông tin
            </button>
            <button
              onClick={() => navigate("/customers")}
              className="px-4 py-2 border rounded hover:bg-muted"
            >
              Trở về danh sách
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="text-sm text-muted-foreground">Tổng đơn hàng</div>
            <div className="text-2xl font-bold">{stats?.total_orders || 0}</div>
          </div>
          <div className="p-4 border rounded-lg">
            <div className="text-sm text-muted-foreground">Đơn hàng hoàn thành</div>
            <div className="text-2xl font-bold text-green-600">
              {stats?.rank_info?.delivered_orders || 0}
            </div>
          </div>
          <div className="p-4 border rounded-lg">
            <div className="text-sm text-muted-foreground">Tổng chi tiêu</div>
            <div className="text-2xl font-bold">
              {formatCurrency(stats?.rank_info?.total_spent || stats?.total_spent || 0)}
            </div>
          </div>
          <div className="p-4 border rounded-lg">
            <div className="text-sm text-muted-foreground">Hạng khách hàng</div>
            <div className="text-2xl font-bold">
              {stats?.rank_info?.rank_display || 'Thường'}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex gap-4">
            <button
              onClick={() => setActiveTab("info")}
              className={`px-4 py-2 font-medium border-b-2 -mb-px transition-colors ${
                activeTab === "info"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              }`}
            >
              Thông tin
            </button>
            <button
              onClick={() => setActiveTab("rank")}
              className={`px-4 py-2 font-medium border-b-2 -mb-px transition-colors ${
                activeTab === "rank"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              }`}
            >
              Hạng khách hàng
            </button>
            <button
              onClick={() => setActiveTab("orders")}
              className={`px-4 py-2 font-medium border-b-2 -mb-px transition-colors ${
                activeTab === "orders"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              }`}
            >
              Đơn hàng
            </button>
            <button
              onClick={() => setActiveTab("notes")}
              className={`px-4 py-2 font-medium border-b-2 -mb-px transition-colors ${
                activeTab === "notes"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              }`}
            >
              Ghi chú
            </button>
            <button
              onClick={() => setActiveTab("analytics")}
              className={`px-4 py-2 font-medium border-b-2 -mb-px transition-colors ${
                activeTab === "analytics"
                  ? "border-primary text-primary"
                  : "border-transparent text-muted-foreground hover:text-foreground"
              }`}
            >
              Phân tích
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "info" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Thông tin cơ bản</h3>
                  <dl className="grid grid-cols-2 gap-2">
                    <dt className="text-sm text-muted-foreground">Họ tên</dt>
                    <dd>{customer.first_name} {customer.last_name}</dd>
                    <dt className="text-sm text-muted-foreground">Email</dt>
                    <dd>{customer.email}</dd>
                    <dt className="text-sm text-muted-foreground">Tên đăng nhập</dt>
                    <dd>{customer.username}</dd>
                    <dt className="text-sm text-muted-foreground">Số điện thoại</dt>
                    <dd>{customer.profile.phone_number || "Chưa cung cấp"}</dd>
                  </dl>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Địa chỉ giao hàng</h3>
                  <div className="p-4 border rounded">
                    <div className="line-clamp-3">
                      {customer.profile.shipping_address ?
                        [
                          customer.profile.shipping_address,
                          customer.profile.ward ? `P.${customer.profile.ward}` : null,
                          customer.profile.district ? `Q.${customer.profile.district}` : null,
                          customer.profile.city ? customer.profile.city : null,
                        ]
                          .filter(Boolean)
                          .join(", ")
                        : "Chưa cung cấp địa chỉ"
                      }
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Chi tiết tài khoản</h3>
                  <dl className="grid grid-cols-2 gap-2">
                    <dt className="text-sm text-muted-foreground">Ngày tham gia</dt>
                    <dd>{customer.date_joined ? new Date(customer.date_joined).toLocaleDateString("vi-VN") : "Không có"}</dd>
                    <dt className="text-sm text-muted-foreground">Lần đăng nhập cuối</dt>
                    <dd>
                      {customer.last_login
                        ? new Date(customer.last_login).toLocaleDateString("vi-VN")
                        : "Chưa đăng nhập"}
                    </dd>

                  </dl>
                </div>
              </div>
            </div>
          )}

          {activeTab === "orders" && (
            <div>
              <div className="border rounded-lg overflow-hidden">
                <table className="min-w-full divide-y">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase">
                        Mã đơn hàng
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase">
                        Ngày tạo
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase">
                        Số lượng
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase">
                        Tổng tiền
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase">
                        Trạng thái
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-background divide-y">
                    {orders?.map((order) => (
                      <tr key={order.id} className="hover:bg-muted/50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          #{order.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {new Date(order.created_at).toLocaleDateString("vi-VN")}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {order.items_count} sản phẩm
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {formatCurrency(order.final_total)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex rounded-full px-2 text-xs font-semibold ${
                              order.status === "delivered"
                                ? "bg-green-100 text-green-800"
                                : order.status === "cancelled"
                                ? "bg-red-100 text-red-800"
                                : "bg-blue-100 text-blue-800"
                            }`}
                          >
                            {order.status === "delivered"
                              ? "Đã giao"
                              : order.status === "cancelled"
                              ? "Đã hủy"
                              : order.status === "pending"
                              ? "Chờ xử lý"
                              : order.status === "processing"
                              ? "Đang xử lý"
                              : order.status === "shipping"
                              ? "Đang giao"
                              : order.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === "rank" && (
            <div className="max-w-2xl">
              <CustomerRankSection customer={customer} stats={stats} />
            </div>
          )}

          {activeTab === "notes" && <CustomerNotes customerId={Number(id)} />}

          {activeTab === "analytics" && (
            <div className="text-center py-8 text-muted-foreground">
              Tính năng phân tích sắp ra mắt...
            </div>
          )}
        </div>
      </div>

      {showEditDialog && customer && (
        <CustomerEditDialog
          customer={customer}
          onClose={() => setShowEditDialog(false)}
        />
      )}
    </>
  );
}
