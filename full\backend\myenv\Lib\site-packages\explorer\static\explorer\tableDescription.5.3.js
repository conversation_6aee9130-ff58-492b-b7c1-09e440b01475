import{g as n,S as d,C as s}from"./choices.5.3.js";import"./_commonjsHelpers.5.3.js";function a(){window.tableChoices&&(window.tableChoices.destroy(),document.getElementById("id_table_name").innerHTML=""),d.get().then(l=>{const i=Object.keys(l),e=document.getElementById("id_table_name");e.toggleAttribute("data-trigger"),e.appendChild(document.createElement("option")),i.forEach(o=>{const t=document.createElement("option");t.value=o,t.textContent=o,e.appendChild(t)}),window.tableChoices=new s("#id_table_name",{searchEnabled:!0,shouldSort:!0,placeholder:!0,placeholderValue:"Select table",position:"bottom"})})}function c(){document.getElementById("schema_frame").src=`${window.baseUrlPath}schema/${n().value}`}function h(){n().addEventListener("change",a),a(),n().addEventListener("change",c),c()}export{h as setupTableDescription};
