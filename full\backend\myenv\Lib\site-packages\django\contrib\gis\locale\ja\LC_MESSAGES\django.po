# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012,2014-2015
# Ta<PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2024-08-07 18:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Japanese (http://app.transifex.com/django/django/language/"
"ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "GIS"
msgstr "地理情報システム"

msgid "The base GIS field."
msgstr "基底GISフィールド"

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr "基底GISフィールド — OpenGIS で決められた地形タイプに対応します。"

msgid "Point"
msgstr "点"

msgid "Line string"
msgstr "線"

msgid "Polygon"
msgstr "ポリゴン"

msgid "Multi-point"
msgstr "複数の点"

msgid "Multi-line string"
msgstr "複数の線"

msgid "Multi polygon"
msgstr "複数のポリゴン"

msgid "Geometry collection"
msgstr "ジオメトリのコレクション"

msgid "Extent Aggregate Field"
msgstr "広さ集計フィールド"

msgid "Raster Field"
msgstr "ラスターフィールド"

msgid "No geometry value provided."
msgstr "geometry値がありません。"

msgid "Invalid geometry value."
msgstr "geometry値が不正です"

msgid "Invalid geometry type."
msgstr "geometryタイプが不正です。"

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""
"geometry を geometry フォームフィールドの SRID に変換しようとしてエラーが起き"
"ました。"

msgid "Delete all Features"
msgstr "すべての機能を削除"

msgid "Debugging window (serialized value)"
msgstr "デバッグウィンドウ(シリアライズされた値)"

msgid "No feeds are registered."
msgstr "フィードが登録されていません。"

#, python-format
msgid "Slug %r isn’t registered."
msgstr "スラグ %r は登録されていません。"
