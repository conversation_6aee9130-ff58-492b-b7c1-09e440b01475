# Generated by Django 3.1.2 on 2020-10-09 05:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('explorer', '0008_auto_20190308_1642'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='query',
            options={'ordering': ['title'], 'verbose_name': 'Query', 'verbose_name_plural': 'Queries'},
        ),
        migrations.AlterField(
            model_name='query',
            name='connection',
            field=models.CharField(blank=True, default='', help_text='Name of DB connection (as specified in settings) to use for this query.Will use EXPLORER_DEFAULT_CONNECTION if left blank', max_length=128),
        ),
        migrations.AlterField(
            model_name='querylog',
            name='connection',
            field=models.CharField(blank=True, default='', max_length=128),
        ),
    ]
