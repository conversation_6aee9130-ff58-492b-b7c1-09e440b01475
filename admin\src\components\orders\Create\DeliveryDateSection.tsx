import { DatePicker } from "antd";
import dayjs from "dayjs";

interface DeliveryDateSectionProps {
  deliveryDate?: string;
  onDeliveryDateChange: (date: string | undefined) => void;
}

export default function DeliveryDateSection({
  deliveryDate,
  onDeliveryDateChange,
}: DeliveryDateSectionProps) {
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      onDeliveryDateChange(date.format("YYYY-MM-DD"));
    } else {
      onDeliveryDateChange(undefined);
    }
  };

  return (
    <div className="bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-4">Ng<PERSON>y giao hàng</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            <PERSON><PERSON><PERSON> giao hàng (tùy chọn)
          </label>
          <DatePicker
            value={deliveryDate ? dayjs(deliveryDate) : null}
            onChange={handleDateChange}
            className="w-full"
            placeholder="Chọn ngày giao hàng"
            format="DD/MM/YYYY"
            disabledDate={current => current && current < dayjs().startOf('day')}
          />
          <p className="text-sm text-gray-500 mt-1">
            Nếu được chọn, thông tin này sẽ được gửi kèm trong thông báo Discord
          </p>
        </div>
      </div>
    </div>
  );
}
