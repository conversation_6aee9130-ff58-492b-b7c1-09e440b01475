import"./explorer.5.3.js";import"./main.5.3.js";import"./_commonjsHelpers.5.3.js";import"./choices.5.3.js";import"./csrf.5.3.js";import"./index.5.3.js";import"./favorites.5.3.js";/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function bn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Mt(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?bn(Object(r),!0).forEach(function(n){Hn(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bn(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Ke(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ke=function(e){return typeof e}:Ke=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ke(t)}function Hn(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Yt(){return Yt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Yt.apply(this,arguments)}function Xn(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,o;for(o=0;o<n.length;o++)i=n[o],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}function jn(t,e){if(t==null)return{};var r=Xn(t,e),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(i=0;i<o.length;i++)n=o[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var qn="1.15.2";function Ut(t){if(typeof window!="undefined"&&window.navigator)return!!navigator.userAgent.match(t)}var Ht=Ut(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Oe=Ut(/Edge/i),yn=Ut(/firefox/i),_e=Ut(/safari/i)&&!Ut(/chrome/i)&&!Ut(/android/i),An=Ut(/iP(ad|od|hone)/i),xn=Ut(/chrome/i)&&Ut(/android/i),On={capture:!1,passive:!1};function H(t,e,r){t.addEventListener(e,r,!Ht&&On)}function U(t,e,r){t.removeEventListener(e,r,!Ht&&On)}function He(t,e){if(e){if(e[0]===">"&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(r){return!1}return!1}}function Vn(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function It(t,e,r,n){if(t){r=r||document;do{if(e!=null&&(e[0]===">"?t.parentNode===r&&He(t,e):He(t,e))||n&&t===r)return t;if(t===r)break}while(t=Vn(t))}return null}var wn=/\s+/g;function St(t,e,r){if(t&&e)if(t.classList)t.classList[r?"add":"remove"](e);else{var n=(" "+t.className+" ").replace(wn," ").replace(" "+e+" "," ");t.className=(n+(r?" "+e:"")).replace(wn," ")}}function x(t,e,r){var n=t&&t.style;if(n){if(r===void 0)return document.defaultView&&document.defaultView.getComputedStyle?r=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(r=t.currentStyle),e===void 0?r:r[e];!(e in n)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),n[e]=r+(typeof r=="string"?"":"px")}}function ve(t,e){var r="";if(typeof t=="string")r=t;else do{var n=x(t,"transform");n&&n!=="none"&&(r=n+" "+r)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(r)}function Nn(t,e,r){if(t){var n=t.getElementsByTagName(e),i=0,o=n.length;if(r)for(;i<o;i++)r(n[i],i);return n}return[]}function Pt(){var t=document.scrollingElement;return t||document.documentElement}function it(t,e,r,n,i){if(!(!t.getBoundingClientRect&&t!==window)){var o,c,d,h,g,E,w;if(t!==window&&t.parentNode&&t!==Pt()?(o=t.getBoundingClientRect(),c=o.top,d=o.left,h=o.bottom,g=o.right,E=o.height,w=o.width):(c=0,d=0,h=window.innerHeight,g=window.innerWidth,E=window.innerHeight,w=window.innerWidth),(e||r)&&t!==window&&(i=i||t.parentNode,!Ht))do if(i&&i.getBoundingClientRect&&(x(i,"transform")!=="none"||r&&x(i,"position")!=="static")){var k=i.getBoundingClientRect();c-=k.top+parseInt(x(i,"border-top-width")),d-=k.left+parseInt(x(i,"border-left-width")),h=c+o.height,g=d+o.width;break}while(i=i.parentNode);if(n&&t!==window){var B=ve(i||t),R=B&&B.a,z=B&&B.d;B&&(c/=z,d/=R,w/=R,E/=z,h=c+E,g=d+w)}return{top:c,left:d,bottom:h,right:g,width:w,height:E}}}function Sn(t,e,r){for(var n=te(t,!0),i=it(t)[e];n;){var o=it(n)[r],c=void 0;if(c=i>=o,!c)return n;if(n===Pt())break;n=te(n,!1)}return!1}function be(t,e,r,n){for(var i=0,o=0,c=t.children;o<c.length;){if(c[o].style.display!=="none"&&c[o]!==O.ghost&&(n||c[o]!==O.dragged)&&It(c[o],r.draggable,t,!1)){if(i===e)return c[o];i++}o++}return null}function pn(t,e){for(var r=t.lastElementChild;r&&(r===O.ghost||x(r,"display")==="none"||e&&!He(r,e));)r=r.previousElementSibling;return r||null}function Dt(t,e){var r=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)t.nodeName.toUpperCase()!=="TEMPLATE"&&t!==O.clone&&(!e||He(t,e))&&r++;return r}function En(t){var e=0,r=0,n=Pt();if(t)do{var i=ve(t),o=i.a,c=i.d;e+=t.scrollLeft*o,r+=t.scrollTop*c}while(t!==n&&(t=t.parentNode));return[e,r]}function Gn(t,e){for(var r in t)if(t.hasOwnProperty(r)){for(var n in e)if(e.hasOwnProperty(n)&&e[n]===t[r][n])return Number(r)}return-1}function te(t,e){if(!t||!t.getBoundingClientRect)return Pt();var r=t,n=!1;do if(r.clientWidth<r.scrollWidth||r.clientHeight<r.scrollHeight){var i=x(r);if(r.clientWidth<r.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||r.clientHeight<r.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!r.getBoundingClientRect||r===document.body)return Pt();if(n||e)return r;n=!0}}while(r=r.parentNode);return Pt()}function Wn(t,e){if(t&&e)for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t}function tn(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}var Te;function In(t,e){return function(){if(!Te){var r=arguments,n=this;r.length===1?t.call(n,r[0]):t.apply(n,r),Te=setTimeout(function(){Te=void 0},e)}}}function Jn(){clearTimeout(Te),Te=void 0}function kn(t,e,r){t.scrollLeft+=e,t.scrollTop+=r}function Fn(t){var e=window.Polymer,r=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):r?r(t).clone(!0)[0]:t.cloneNode(!0)}function Pn(t,e,r){var n={};return Array.from(t.children).forEach(function(i){var o,c,d,h;if(!(!It(i,e.draggable,t,!1)||i.animated||i===r)){var g=it(i);n.left=Math.min((o=n.left)!==null&&o!==void 0?o:1/0,g.left),n.top=Math.min((c=n.top)!==null&&c!==void 0?c:1/0,g.top),n.right=Math.max((d=n.right)!==null&&d!==void 0?d:-1/0,g.right),n.bottom=Math.max((h=n.bottom)!==null&&h!==void 0?h:-1/0,g.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var Ct="Sortable"+new Date().getTime();function Zn(){var t=[],e;return{captureAnimationState:function(){if(t=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(i){if(!(x(i,"display")==="none"||i===O.ghost)){t.push({target:i,rect:it(i)});var o=Mt({},t[t.length-1].rect);if(i.thisAnimationDuration){var c=ve(i,!0);c&&(o.top-=c.f,o.left-=c.e)}i.fromRect=o}})}},addAnimationState:function(n){t.push(n)},removeAnimationState:function(n){t.splice(Gn(t,{target:n}),1)},animateAll:function(n){var i=this;if(!this.options.animation){clearTimeout(e),typeof n=="function"&&n();return}var o=!1,c=0;t.forEach(function(d){var h=0,g=d.target,E=g.fromRect,w=it(g),k=g.prevFromRect,B=g.prevToRect,R=d.rect,z=ve(g,!0);z&&(w.top-=z.f,w.left-=z.e),g.toRect=w,g.thisAnimationDuration&&tn(k,w)&&!tn(E,w)&&(R.top-w.top)/(R.left-w.left)===(E.top-w.top)/(E.left-w.left)&&(h=$n(R,k,B,i.options)),tn(w,E)||(g.prevFromRect=E,g.prevToRect=w,h||(h=i.options.animation),i.animate(g,R,w,h)),h&&(o=!0,c=Math.max(c,h),clearTimeout(g.animationResetTimer),g.animationResetTimer=setTimeout(function(){g.animationTime=0,g.prevFromRect=null,g.fromRect=null,g.prevToRect=null,g.thisAnimationDuration=null},h),g.thisAnimationDuration=h)}),clearTimeout(e),o?e=setTimeout(function(){typeof n=="function"&&n()},c):typeof n=="function"&&n(),t=[]},animate:function(n,i,o,c){if(c){x(n,"transition",""),x(n,"transform","");var d=ve(this.el),h=d&&d.a,g=d&&d.d,E=(i.left-o.left)/(h||1),w=(i.top-o.top)/(g||1);n.animatingX=!!E,n.animatingY=!!w,x(n,"transform","translate3d("+E+"px,"+w+"px,0)"),this.forRepaintDummy=Qn(n),x(n,"transition","transform "+c+"ms"+(this.options.easing?" "+this.options.easing:"")),x(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){x(n,"transition",""),x(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},c)}}}}function Qn(t){return t.offsetWidth}function $n(t,e,r,n){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-r.top,2)+Math.pow(e.left-r.left,2))*n.animation}var he=[],en={initializeByDefault:!0},Ne={mount:function(e){for(var r in en)en.hasOwnProperty(r)&&!(r in e)&&(e[r]=en[r]);he.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),he.push(e)},pluginEvent:function(e,r,n){var i=this;this.eventCanceled=!1,n.cancel=function(){i.eventCanceled=!0};var o=e+"Global";he.forEach(function(c){r[c.pluginName]&&(r[c.pluginName][o]&&r[c.pluginName][o](Mt({sortable:r},n)),r.options[c.pluginName]&&r[c.pluginName][e]&&r[c.pluginName][e](Mt({sortable:r},n)))})},initializePlugins:function(e,r,n,i){he.forEach(function(d){var h=d.pluginName;if(!(!e.options[h]&&!d.initializeByDefault)){var g=new d(e,r,e.options);g.sortable=e,g.options=e.options,e[h]=g,Yt(n,g.defaults)}});for(var o in e.options)if(e.options.hasOwnProperty(o)){var c=this.modifyOption(e,o,e.options[o]);typeof c!="undefined"&&(e.options[o]=c)}},getEventProperties:function(e,r){var n={};return he.forEach(function(i){typeof i.eventProperties=="function"&&Yt(n,i.eventProperties.call(r[i.pluginName],e))}),n},modifyOption:function(e,r,n){var i;return he.forEach(function(o){e[o.pluginName]&&o.optionListeners&&typeof o.optionListeners[r]=="function"&&(i=o.optionListeners[r].call(e[o.pluginName],n))}),i}};function tr(t){var e=t.sortable,r=t.rootEl,n=t.name,i=t.targetEl,o=t.cloneEl,c=t.toEl,d=t.fromEl,h=t.oldIndex,g=t.newIndex,E=t.oldDraggableIndex,w=t.newDraggableIndex,k=t.originalEvent,B=t.putSortable,R=t.extraEventProperties;if(e=e||r&&r[Ct],!!e){var z,ct=e.options,_t="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!Ht&&!Oe?z=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(z=document.createEvent("Event"),z.initEvent(n,!0,!0)),z.to=c||r,z.from=d||r,z.item=i||r,z.clone=o,z.oldIndex=h,z.newIndex=g,z.oldDraggableIndex=E,z.newDraggableIndex=w,z.originalEvent=k,z.pullMode=B?B.lastPutMode:void 0;var K=Mt(Mt({},R),Ne.getEventProperties(n,e));for(var ot in K)z[ot]=K[ot];r&&r.dispatchEvent(z),ct[_t]&&ct[_t].call(e,z)}}var er=["evt"],yt=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=n.evt,o=jn(n,er);Ne.pluginEvent.bind(O)(e,r,Mt({dragEl:b,parentEl:Q,ghostEl:I,rootEl:V,nextEl:ae,lastDownEl:Be,cloneEl:G,cloneHidden:$t,dragStarted:Se,putSortable:lt,activeSortable:O.active,originalEvent:i,oldIndex:me,oldDraggableIndex:De,newIndex:Et,newDraggableIndex:Qt,hideGhostForTarget:Kn,unhideGhostForTarget:Bn,cloneNowHidden:function(){$t=!0},cloneNowShown:function(){$t=!1},dispatchSortableEvent:function(d){ht({sortable:r,name:d,originalEvent:i})}},o))};function ht(t){tr(Mt({putSortable:lt,cloneEl:G,targetEl:b,rootEl:V,oldIndex:me,oldDraggableIndex:De,newIndex:Et,newDraggableIndex:Qt},t))}var b,Q,I,V,ae,Be,G,$t,me,Et,De,Qt,Pe,lt,ge=!1,Xe=!1,je=[],ie,Nt,nn,rn,Cn,_n,Se,pe,Ae,xe=!1,Me=!1,ze,st,on=[],cn=!1,qe=[],Ge=typeof document!="undefined",Re=An,Tn=Oe||Ht?"cssFloat":"float",nr=Ge&&!xn&&!An&&"draggable"in document.createElement("div"),Mn=function(){if(Ge){if(Ht)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto",t.style.pointerEvents==="auto"}}(),Rn=function(e,r){var n=x(e),i=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),o=be(e,0,r),c=be(e,1,r),d=o&&x(o),h=c&&x(c),g=d&&parseInt(d.marginLeft)+parseInt(d.marginRight)+it(o).width,E=h&&parseInt(h.marginLeft)+parseInt(h.marginRight)+it(c).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&d.float&&d.float!=="none"){var w=d.float==="left"?"left":"right";return c&&(h.clear==="both"||h.clear===w)?"vertical":"horizontal"}return o&&(d.display==="block"||d.display==="flex"||d.display==="table"||d.display==="grid"||g>=i&&n[Tn]==="none"||c&&n[Tn]==="none"&&g+E>i)?"vertical":"horizontal"},rr=function(e,r,n){var i=n?e.left:e.top,o=n?e.right:e.bottom,c=n?e.width:e.height,d=n?r.left:r.top,h=n?r.right:r.bottom,g=n?r.width:r.height;return i===d||o===h||i+c/2===d+g/2},ir=function(e,r){var n;return je.some(function(i){var o=i[Ct].options.emptyInsertThreshold;if(!(!o||pn(i))){var c=it(i),d=e>=c.left-o&&e<=c.right+o,h=r>=c.top-o&&r<=c.bottom+o;if(d&&h)return n=i}}),n},Ln=function(e){function r(o,c){return function(d,h,g,E){var w=d.options.group.name&&h.options.group.name&&d.options.group.name===h.options.group.name;if(o==null&&(c||w))return!0;if(o==null||o===!1)return!1;if(c&&o==="clone")return o;if(typeof o=="function")return r(o(d,h,g,E),c)(d,h,g,E);var k=(c?d:h).options.group.name;return o===!0||typeof o=="string"&&o===k||o.join&&o.indexOf(k)>-1}}var n={},i=e.group;(!i||Ke(i)!="object")&&(i={name:i}),n.name=i.name,n.checkPull=r(i.pull,!0),n.checkPut=r(i.put),n.revertClone=i.revertClone,e.group=n},Kn=function(){!Mn&&I&&x(I,"display","none")},Bn=function(){!Mn&&I&&x(I,"display","")};Ge&&!xn&&document.addEventListener("click",function(t){if(Xe)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Xe=!1,!1},!0);var oe=function(e){if(b){e=e.touches?e.touches[0]:e;var r=ir(e.clientX,e.clientY);if(r){var n={};for(var i in e)e.hasOwnProperty(i)&&(n[i]=e[i]);n.target=n.rootEl=r,n.preventDefault=void 0,n.stopPropagation=void 0,r[Ct]._onDragOver(n)}}},or=function(e){b&&b.parentNode[Ct]._isOutsideThisEl(e.target)};function O(t,e){if(!(t&&t.nodeType&&t.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=Yt({},e),t[Ct]=this;var r={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Rn(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(c,d){c.setData("Text",d.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:O.supportPointer!==!1&&"PointerEvent"in window&&!_e,emptyInsertThreshold:5};Ne.initializePlugins(this,t,r);for(var n in r)!(n in e)&&(e[n]=r[n]);Ln(e);for(var i in this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=e.forceFallback?!1:nr,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?H(t,"pointerdown",this._onTapStart):(H(t,"mousedown",this._onTapStart),H(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(H(t,"dragover",this),H(t,"dragenter",this)),je.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),Yt(this,Zn())}O.prototype={constructor:O,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(pe=null)},_getDirection:function(e,r){return typeof this.options.direction=="function"?this.options.direction.call(this,e,r,b):this.options.direction},_onTapStart:function(e){if(e.cancelable){var r=this,n=this.el,i=this.options,o=i.preventOnFilter,c=e.type,d=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,h=(d||e).target,g=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||h,E=i.filter;if(hr(n),!b&&!(/mousedown|pointerdown/.test(c)&&e.button!==0||i.disabled)&&!g.isContentEditable&&!(!this.nativeDraggable&&_e&&h&&h.tagName.toUpperCase()==="SELECT")&&(h=It(h,i.draggable,n,!1),!(h&&h.animated)&&Be!==h)){if(me=Dt(h),De=Dt(h,i.draggable),typeof E=="function"){if(E.call(this,e,h,this)){ht({sortable:r,rootEl:g,name:"filter",targetEl:h,toEl:n,fromEl:n}),yt("filter",r,{evt:e}),o&&e.cancelable&&e.preventDefault();return}}else if(E&&(E=E.split(",").some(function(w){if(w=It(g,w.trim(),n,!1),w)return ht({sortable:r,rootEl:w,name:"filter",targetEl:h,fromEl:n,toEl:n}),yt("filter",r,{evt:e}),!0}),E)){o&&e.cancelable&&e.preventDefault();return}i.handle&&!It(g,i.handle,n,!1)||this._prepareDragStart(e,d,h)}}},_prepareDragStart:function(e,r,n){var i=this,o=i.el,c=i.options,d=o.ownerDocument,h;if(n&&!b&&n.parentNode===o){var g=it(n);if(V=o,b=n,Q=b.parentNode,ae=b.nextSibling,Be=n,Pe=c.group,O.dragged=b,ie={target:b,clientX:(r||e).clientX,clientY:(r||e).clientY},Cn=ie.clientX-g.left,_n=ie.clientY-g.top,this._lastX=(r||e).clientX,this._lastY=(r||e).clientY,b.style["will-change"]="all",h=function(){if(yt("delayEnded",i,{evt:e}),O.eventCanceled){i._onDrop();return}i._disableDelayedDragEvents(),!yn&&i.nativeDraggable&&(b.draggable=!0),i._triggerDragStart(e,r),ht({sortable:i,name:"choose",originalEvent:e}),St(b,c.chosenClass,!0)},c.ignore.split(",").forEach(function(E){Nn(b,E.trim(),an)}),H(d,"dragover",oe),H(d,"mousemove",oe),H(d,"touchmove",oe),H(d,"mouseup",i._onDrop),H(d,"touchend",i._onDrop),H(d,"touchcancel",i._onDrop),yn&&this.nativeDraggable&&(this.options.touchStartThreshold=4,b.draggable=!0),yt("delayStart",this,{evt:e}),c.delay&&(!c.delayOnTouchOnly||r)&&(!this.nativeDraggable||!(Oe||Ht))){if(O.eventCanceled){this._onDrop();return}H(d,"mouseup",i._disableDelayedDrag),H(d,"touchend",i._disableDelayedDrag),H(d,"touchcancel",i._disableDelayedDrag),H(d,"mousemove",i._delayedDragTouchMoveHandler),H(d,"touchmove",i._delayedDragTouchMoveHandler),c.supportPointer&&H(d,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(h,c.delay)}else h()}},_delayedDragTouchMoveHandler:function(e){var r=e.touches?e.touches[0]:e;Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){b&&an(b),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;U(e,"mouseup",this._disableDelayedDrag),U(e,"touchend",this._disableDelayedDrag),U(e,"touchcancel",this._disableDelayedDrag),U(e,"mousemove",this._delayedDragTouchMoveHandler),U(e,"touchmove",this._delayedDragTouchMoveHandler),U(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,r){r=r||e.pointerType=="touch"&&e,!this.nativeDraggable||r?this.options.supportPointer?H(document,"pointermove",this._onTouchMove):r?H(document,"touchmove",this._onTouchMove):H(document,"mousemove",this._onTouchMove):(H(b,"dragend",this),H(V,"dragstart",this._onDragStart));try{document.selection?Ue(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(n){}},_dragStarted:function(e,r){if(ge=!1,V&&b){yt("dragStarted",this,{evt:r}),this.nativeDraggable&&H(document,"dragover",or);var n=this.options;!e&&St(b,n.dragClass,!1),St(b,n.ghostClass,!0),O.active=this,e&&this._appendGhost(),ht({sortable:this,name:"start",originalEvent:r})}else this._nulling()},_emulateDragOver:function(){if(Nt){this._lastX=Nt.clientX,this._lastY=Nt.clientY,Kn();for(var e=document.elementFromPoint(Nt.clientX,Nt.clientY),r=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Nt.clientX,Nt.clientY),e!==r);)r=e;if(b.parentNode[Ct]._isOutsideThisEl(e),r)do{if(r[Ct]){var n=void 0;if(n=r[Ct]._onDragOver({clientX:Nt.clientX,clientY:Nt.clientY,target:e,rootEl:r}),n&&!this.options.dragoverBubble)break}e=r}while(r=r.parentNode);Bn()}},_onTouchMove:function(e){if(ie){var r=this.options,n=r.fallbackTolerance,i=r.fallbackOffset,o=e.touches?e.touches[0]:e,c=I&&ve(I,!0),d=I&&c&&c.a,h=I&&c&&c.d,g=Re&&st&&En(st),E=(o.clientX-ie.clientX+i.x)/(d||1)+(g?g[0]-on[0]:0)/(d||1),w=(o.clientY-ie.clientY+i.y)/(h||1)+(g?g[1]-on[1]:0)/(h||1);if(!O.active&&!ge){if(n&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(I){c?(c.e+=E-(nn||0),c.f+=w-(rn||0)):c={a:1,b:0,c:0,d:1,e:E,f:w};var k="matrix(".concat(c.a,",").concat(c.b,",").concat(c.c,",").concat(c.d,",").concat(c.e,",").concat(c.f,")");x(I,"webkitTransform",k),x(I,"mozTransform",k),x(I,"msTransform",k),x(I,"transform",k),nn=E,rn=w,Nt=o}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!I){var e=this.options.fallbackOnBody?document.body:V,r=it(b,!0,Re,!0,e),n=this.options;if(Re){for(st=e;x(st,"position")==="static"&&x(st,"transform")==="none"&&st!==document;)st=st.parentNode;st!==document.body&&st!==document.documentElement?(st===document&&(st=Pt()),r.top+=st.scrollTop,r.left+=st.scrollLeft):st=Pt(),on=En(st)}I=b.cloneNode(!0),St(I,n.ghostClass,!1),St(I,n.fallbackClass,!0),St(I,n.dragClass,!0),x(I,"transition",""),x(I,"transform",""),x(I,"box-sizing","border-box"),x(I,"margin",0),x(I,"top",r.top),x(I,"left",r.left),x(I,"width",r.width),x(I,"height",r.height),x(I,"opacity","0.8"),x(I,"position",Re?"absolute":"fixed"),x(I,"zIndex","100000"),x(I,"pointerEvents","none"),O.ghost=I,e.appendChild(I),x(I,"transform-origin",Cn/parseInt(I.style.width)*100+"% "+_n/parseInt(I.style.height)*100+"%")}},_onDragStart:function(e,r){var n=this,i=e.dataTransfer,o=n.options;if(yt("dragStart",this,{evt:e}),O.eventCanceled){this._onDrop();return}yt("setupClone",this),O.eventCanceled||(G=Fn(b),G.removeAttribute("id"),G.draggable=!1,G.style["will-change"]="",this._hideClone(),St(G,this.options.chosenClass,!1),O.clone=G),n.cloneId=Ue(function(){yt("clone",n),!O.eventCanceled&&(n.options.removeCloneOnHide||V.insertBefore(G,b),n._hideClone(),ht({sortable:n,name:"clone"}))}),!r&&St(b,o.dragClass,!0),r?(Xe=!0,n._loopId=setInterval(n._emulateDragOver,50)):(U(document,"mouseup",n._onDrop),U(document,"touchend",n._onDrop),U(document,"touchcancel",n._onDrop),i&&(i.effectAllowed="move",o.setData&&o.setData.call(n,i,b)),H(document,"drop",n),x(b,"transform","translateZ(0)")),ge=!0,n._dragStartId=Ue(n._dragStarted.bind(n,r,e)),H(document,"selectstart",n),Se=!0,_e&&x(document.body,"user-select","none")},_onDragOver:function(e){var r=this.el,n=e.target,i,o,c,d=this.options,h=d.group,g=O.active,E=Pe===h,w=d.sort,k=lt||g,B,R=this,z=!1;if(cn)return;function ct(M,A){yt(M,R,Mt({evt:e,isOwner:E,axis:B?"vertical":"horizontal",revert:c,dragRect:i,targetRect:o,canSort:w,fromSortable:k,target:n,completed:K,onMove:function(L,j){return Le(V,r,b,i,L,it(L),e,j)},changed:ot},A))}function _t(){ct("dragOverAnimationCapture"),R.captureAnimationState(),R!==k&&k.captureAnimationState()}function K(M){return ct("dragOverCompleted",{insertion:M}),M&&(E?g._hideClone():g._showClone(R),R!==k&&(St(b,lt?lt.options.ghostClass:g.options.ghostClass,!1),St(b,d.ghostClass,!0)),lt!==R&&R!==O.active?lt=R:R===O.active&&lt&&(lt=null),k===R&&(R._ignoreWhileAnimating=n),R.animateAll(function(){ct("dragOverAnimationComplete"),R._ignoreWhileAnimating=null}),R!==k&&(k.animateAll(),k._ignoreWhileAnimating=null)),(n===b&&!b.animated||n===r&&!n.animated)&&(pe=null),!d.dragoverBubble&&!e.rootEl&&n!==document&&(b.parentNode[Ct]._isOutsideThisEl(e.target),!M&&oe(e)),!d.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),z=!0}function ot(){Et=Dt(b),Qt=Dt(b,d.draggable),ht({sortable:R,name:"change",toEl:r,newIndex:Et,newDraggableIndex:Qt,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),n=It(n,d.draggable,r,!0),ct("dragOver"),O.eventCanceled)return z;if(b.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||R._ignoreWhileAnimating===n)return K(!1);if(Xe=!1,g&&!d.disabled&&(E?w||(c=Q!==V):lt===this||(this.lastPutMode=Pe.checkPull(this,g,b,e))&&h.checkPut(this,g,b,e))){if(B=this._getDirection(e,n)==="vertical",i=it(b),ct("dragOverValid"),O.eventCanceled)return z;if(c)return Q=V,_t(),this._hideClone(),ct("revert"),O.eventCanceled||(ae?V.insertBefore(b,ae):V.appendChild(b)),K(!0);var W=pn(r,d.draggable);if(!W||ur(e,B,this)&&!W.animated){if(W===b)return K(!1);if(W&&r===e.target&&(n=W),n&&(o=it(n)),Le(V,r,b,i,n,o,e,!!n)!==!1)return _t(),W&&W.nextSibling?r.insertBefore(b,W.nextSibling):r.appendChild(b),Q=r,ot(),K(!0)}else if(W&&sr(e,B,this)){var pt=be(r,0,d,!0);if(pt===b)return K(!1);if(n=pt,o=it(n),Le(V,r,b,i,n,o,e,!1)!==!1)return _t(),r.insertBefore(b,pt),Q=r,ot(),K(!0)}else if(n.parentNode===r){o=it(n);var a=0,l,u=b.parentNode!==r,s=!rr(b.animated&&b.toRect||i,n.animated&&n.toRect||o,B),f=B?"top":"left",p=Sn(n,"top","top")||Sn(b,"top","top"),v=p?p.scrollTop:void 0;pe!==n&&(l=o[f],xe=!1,Me=!s&&d.invertSwap||u),a=cr(e,n,o,B,s?1:d.swapThreshold,d.invertedSwapThreshold==null?d.swapThreshold:d.invertedSwapThreshold,Me,pe===n);var m;if(a!==0){var y=Dt(b);do y-=a,m=Q.children[y];while(m&&(x(m,"display")==="none"||m===I))}if(a===0||m===n)return K(!1);pe=n,Ae=a;var C=n.nextElementSibling,S=!1;S=a===1;var D=Le(V,r,b,i,n,o,e,S);if(D!==!1)return(D===1||D===-1)&&(S=D===1),cn=!0,setTimeout(lr,30),_t(),S&&!C?r.appendChild(b):n.parentNode.insertBefore(b,S?C:n),p&&kn(p,0,v-p.scrollTop),Q=b.parentNode,l!==void 0&&!Me&&(ze=Math.abs(l-it(n)[f])),ot(),K(!0)}if(r.contains(b))return K(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){U(document,"mousemove",this._onTouchMove),U(document,"touchmove",this._onTouchMove),U(document,"pointermove",this._onTouchMove),U(document,"dragover",oe),U(document,"mousemove",oe),U(document,"touchmove",oe)},_offUpEvents:function(){var e=this.el.ownerDocument;U(e,"mouseup",this._onDrop),U(e,"touchend",this._onDrop),U(e,"pointerup",this._onDrop),U(e,"touchcancel",this._onDrop),U(document,"selectstart",this)},_onDrop:function(e){var r=this.el,n=this.options;if(Et=Dt(b),Qt=Dt(b,n.draggable),yt("drop",this,{evt:e}),Q=b&&b.parentNode,Et=Dt(b),Qt=Dt(b,n.draggable),O.eventCanceled){this._nulling();return}ge=!1,Me=!1,xe=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),fn(this.cloneId),fn(this._dragStartId),this.nativeDraggable&&(U(document,"drop",this),U(r,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),_e&&x(document.body,"user-select",""),x(b,"transform",""),e&&(Se&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),I&&I.parentNode&&I.parentNode.removeChild(I),(V===Q||lt&&lt.lastPutMode!=="clone")&&G&&G.parentNode&&G.parentNode.removeChild(G),b&&(this.nativeDraggable&&U(b,"dragend",this),an(b),b.style["will-change"]="",Se&&!ge&&St(b,lt?lt.options.ghostClass:this.options.ghostClass,!1),St(b,this.options.chosenClass,!1),ht({sortable:this,name:"unchoose",toEl:Q,newIndex:null,newDraggableIndex:null,originalEvent:e}),V!==Q?(Et>=0&&(ht({rootEl:Q,name:"add",toEl:Q,fromEl:V,originalEvent:e}),ht({sortable:this,name:"remove",toEl:Q,originalEvent:e}),ht({rootEl:Q,name:"sort",toEl:Q,fromEl:V,originalEvent:e}),ht({sortable:this,name:"sort",toEl:Q,originalEvent:e})),lt&&lt.save()):Et!==me&&Et>=0&&(ht({sortable:this,name:"update",toEl:Q,originalEvent:e}),ht({sortable:this,name:"sort",toEl:Q,originalEvent:e})),O.active&&((Et==null||Et===-1)&&(Et=me,Qt=De),ht({sortable:this,name:"end",toEl:Q,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){yt("nulling",this),V=b=Q=I=ae=G=Be=$t=ie=Nt=Se=Et=Qt=me=De=pe=Ae=lt=Pe=O.dragged=O.ghost=O.clone=O.active=null,qe.forEach(function(e){e.checked=!0}),qe.length=nn=rn=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":b&&(this._onDragOver(e),ar(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],r,n=this.el.children,i=0,o=n.length,c=this.options;i<o;i++)r=n[i],It(r,c.draggable,this.el,!1)&&e.push(r.getAttribute(c.dataIdAttr)||dr(r));return e},sort:function(e,r){var n={},i=this.el;this.toArray().forEach(function(o,c){var d=i.children[c];It(d,this.options.draggable,i,!1)&&(n[o]=d)},this),r&&this.captureAnimationState(),e.forEach(function(o){n[o]&&(i.removeChild(n[o]),i.appendChild(n[o]))}),r&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,r){return It(e,r||this.options.draggable,this.el,!1)},option:function(e,r){var n=this.options;if(r===void 0)return n[e];var i=Ne.modifyOption(this,e,r);typeof i!="undefined"?n[e]=i:n[e]=r,e==="group"&&Ln(n)},destroy:function(){yt("destroy",this);var e=this.el;e[Ct]=null,U(e,"mousedown",this._onTapStart),U(e,"touchstart",this._onTapStart),U(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(U(e,"dragover",this),U(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(r){r.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),je.splice(je.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!$t){if(yt("hideClone",this),O.eventCanceled)return;x(G,"display","none"),this.options.removeCloneOnHide&&G.parentNode&&G.parentNode.removeChild(G),$t=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if($t){if(yt("showClone",this),O.eventCanceled)return;b.parentNode==V&&!this.options.group.revertClone?V.insertBefore(G,b):ae?V.insertBefore(G,ae):V.appendChild(G),this.options.group.revertClone&&this.animate(b,G),x(G,"display",""),$t=!1}}};function ar(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function Le(t,e,r,n,i,o,c,d){var h,g=t[Ct],E=g.options.onMove,w;return window.CustomEvent&&!Ht&&!Oe?h=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(h=document.createEvent("Event"),h.initEvent("move",!0,!0)),h.to=e,h.from=t,h.dragged=r,h.draggedRect=n,h.related=i||e,h.relatedRect=o||it(e),h.willInsertAfter=d,h.originalEvent=c,t.dispatchEvent(h),E&&(w=E.call(g,h,c)),w}function an(t){t.draggable=!1}function lr(){cn=!1}function sr(t,e,r){var n=it(be(r.el,0,r.options,!0)),i=Pn(r.el,r.options,I),o=10;return e?t.clientX<i.left-o||t.clientY<n.top&&t.clientX<n.right:t.clientY<i.top-o||t.clientY<n.bottom&&t.clientX<n.left}function ur(t,e,r){var n=it(pn(r.el,r.options.draggable)),i=Pn(r.el,r.options,I),o=10;return e?t.clientX>i.right+o||t.clientY>n.bottom&&t.clientX>n.left:t.clientY>i.bottom+o||t.clientX>n.right&&t.clientY>n.top}function cr(t,e,r,n,i,o,c,d){var h=n?t.clientY:t.clientX,g=n?r.height:r.width,E=n?r.top:r.left,w=n?r.bottom:r.right,k=!1;if(!c){if(d&&ze<g*i){if(!xe&&(Ae===1?h>E+g*o/2:h<w-g*o/2)&&(xe=!0),xe)k=!0;else if(Ae===1?h<E+ze:h>w-ze)return-Ae}else if(h>E+g*(1-i)/2&&h<w-g*(1-i)/2)return fr(e)}return k=k||c,k&&(h<E+g*o/2||h>w-g*o/2)?h>E+g/2?1:-1:0}function fr(t){return Dt(b)<Dt(t)?1:-1}function dr(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,r=e.length,n=0;r--;)n+=e.charCodeAt(r);return n.toString(36)}function hr(t){qe.length=0;for(var e=t.getElementsByTagName("input"),r=e.length;r--;){var n=e[r];n.checked&&qe.push(n)}}function Ue(t){return setTimeout(t,0)}function fn(t){return clearTimeout(t)}Ge&&H(document,"touchmove",function(t){(O.active||ge)&&t.cancelable&&t.preventDefault()});O.utils={on:H,off:U,css:x,find:Nn,is:function(e,r){return!!It(e,r,e,!1)},extend:Wn,throttle:In,closest:It,toggleClass:St,clone:Fn,index:Dt,nextTick:Ue,cancelNextTick:fn,detectDirection:Rn,getChild:be};O.get=function(t){return t[Ct]};O.mount=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];e[0].constructor===Array&&(e=e[0]),e.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(O.utils=Mt(Mt({},O.utils),n.utils)),Ne.mount(n)})};O.create=function(t,e){return new O(t,e)};O.version=qn;var rt=[],Ee,dn,hn=!1,ln,sn,Ve,Ce;function pr(){function t(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return t.prototype={dragStarted:function(r){var n=r.originalEvent;this.sortable.nativeDraggable?H(document,"dragover",this._handleAutoScroll):this.options.supportPointer?H(document,"pointermove",this._handleFallbackAutoScroll):n.touches?H(document,"touchmove",this._handleFallbackAutoScroll):H(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(r){var n=r.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?U(document,"dragover",this._handleAutoScroll):(U(document,"pointermove",this._handleFallbackAutoScroll),U(document,"touchmove",this._handleFallbackAutoScroll),U(document,"mousemove",this._handleFallbackAutoScroll)),Dn(),Ye(),Jn()},nulling:function(){Ve=dn=Ee=hn=Ce=ln=sn=null,rt.length=0},_handleFallbackAutoScroll:function(r){this._handleAutoScroll(r,!0)},_handleAutoScroll:function(r,n){var i=this,o=(r.touches?r.touches[0]:r).clientX,c=(r.touches?r.touches[0]:r).clientY,d=document.elementFromPoint(o,c);if(Ve=r,n||this.options.forceAutoScrollFallback||Oe||Ht||_e){un(r,this.options,d,n);var h=te(d,!0);hn&&(!Ce||o!==ln||c!==sn)&&(Ce&&Dn(),Ce=setInterval(function(){var g=te(document.elementFromPoint(o,c),!0);g!==h&&(h=g,Ye()),un(r,i.options,g,n)},10),ln=o,sn=c)}else{if(!this.options.bubbleScroll||te(d,!0)===Pt()){Ye();return}un(r,this.options,te(d,!1),!1)}}},Yt(t,{pluginName:"scroll",initializeByDefault:!0})}function Ye(){rt.forEach(function(t){clearInterval(t.pid)}),rt=[]}function Dn(){clearInterval(Ce)}var un=In(function(t,e,r,n){if(e.scroll){var i=(t.touches?t.touches[0]:t).clientX,o=(t.touches?t.touches[0]:t).clientY,c=e.scrollSensitivity,d=e.scrollSpeed,h=Pt(),g=!1,E;dn!==r&&(dn=r,Ye(),Ee=e.scroll,E=e.scrollFn,Ee===!0&&(Ee=te(r,!0)));var w=0,k=Ee;do{var B=k,R=it(B),z=R.top,ct=R.bottom,_t=R.left,K=R.right,ot=R.width,W=R.height,pt=void 0,a=void 0,l=B.scrollWidth,u=B.scrollHeight,s=x(B),f=B.scrollLeft,p=B.scrollTop;B===h?(pt=ot<l&&(s.overflowX==="auto"||s.overflowX==="scroll"||s.overflowX==="visible"),a=W<u&&(s.overflowY==="auto"||s.overflowY==="scroll"||s.overflowY==="visible")):(pt=ot<l&&(s.overflowX==="auto"||s.overflowX==="scroll"),a=W<u&&(s.overflowY==="auto"||s.overflowY==="scroll"));var v=pt&&(Math.abs(K-i)<=c&&f+ot<l)-(Math.abs(_t-i)<=c&&!!f),m=a&&(Math.abs(ct-o)<=c&&p+W<u)-(Math.abs(z-o)<=c&&!!p);if(!rt[w])for(var y=0;y<=w;y++)rt[y]||(rt[y]={});(rt[w].vx!=v||rt[w].vy!=m||rt[w].el!==B)&&(rt[w].el=B,rt[w].vx=v,rt[w].vy=m,clearInterval(rt[w].pid),(v!=0||m!=0)&&(g=!0,rt[w].pid=setInterval(function(){n&&this.layer===0&&O.active._onTouchMove(Ve);var C=rt[this.layer].vy?rt[this.layer].vy*d:0,S=rt[this.layer].vx?rt[this.layer].vx*d:0;typeof E=="function"&&E.call(O.dragged.parentNode[Ct],S,C,t,Ve,rt[this.layer].el)!=="continue"||kn(rt[this.layer].el,S,C)}.bind({layer:w}),24))),w++}while(e.bubbleScroll&&k!==h&&(k=te(k,!1)));hn=g}},30),zn=function(e){var r=e.originalEvent,n=e.putSortable,i=e.dragEl,o=e.activeSortable,c=e.dispatchSortableEvent,d=e.hideGhostForTarget,h=e.unhideGhostForTarget;if(r){var g=n||o;d();var E=r.changedTouches&&r.changedTouches.length?r.changedTouches[0]:r,w=document.elementFromPoint(E.clientX,E.clientY);h(),g&&!g.el.contains(w)&&(c("spill"),this.onSpill({dragEl:i,putSortable:n}))}};function gn(){}gn.prototype={startIndex:null,dragStart:function(e){var r=e.oldDraggableIndex;this.startIndex=r},onSpill:function(e){var r=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var i=be(this.sortable.el,this.startIndex,this.options);i?this.sortable.el.insertBefore(r,i):this.sortable.el.appendChild(r),this.sortable.animateAll(),n&&n.animateAll()},drop:zn};Yt(gn,{pluginName:"revertOnSpill"});function mn(){}mn.prototype={onSpill:function(e){var r=e.dragEl,n=e.putSortable,i=n||this.sortable;i.captureAnimationState(),r.parentNode&&r.parentNode.removeChild(r),i.animateAll()},drop:zn};Yt(mn,{pluginName:"removeOnSpill"});O.mount(new pr);O.mount(mn,gn);let Zt=[].indexOf||function(t){for(var e=0,r=this.length;e<r;e++)if(e in this&&this[e]===t)return e;return-1},gr=[].slice,we=function(t,e){return function(){return t.apply(e,arguments)}},ut={}.hasOwnProperty;function mr(t){var e,r,n,i,o,c,d,h,g,E,w,k,B,R,z,ct,_t,K,ot,W,pt;return r=function(a,l,u){var s,f,p,v;for(a+="",f=a.split("."),p=f[0],v=f.length>1?u+f[1]:"",s=/(\d+)(\d{3})/;s.test(p);)p=p.replace(s,"$1"+l+"$2");return p+v},w=function(a){var l;return l={digitsAfterDecimal:2,scaler:1,thousandsSep:",",decimalSep:".",prefix:"",suffix:""},a=t.extend({},l,a),function(u){var s;return isNaN(u)||!isFinite(u)?"":(s=r((a.scaler*u).toFixed(a.digitsAfterDecimal),a.thousandsSep,a.decimalSep),""+a.prefix+s+a.suffix)}},K=w(),ot=w({digitsAfterDecimal:0}),W=w({digitsAfterDecimal:1,scaler:100,suffix:"%"}),n={count:function(a){return a==null&&(a=ot),function(){return function(l,u,s){return{count:0,push:function(){return this.count++},value:function(){return this.count},format:a}}}},uniques:function(a,l){return l==null&&(l=ot),function(u){var s;return s=u[0],function(f,p,v){return{uniq:[],push:function(m){var y;if(y=m[s],Zt.call(this.uniq,y)<0)return this.uniq.push(m[s])},value:function(){return a(this.uniq)},format:l,numInputs:s!=null?0:1}}}},sum:function(a){return a==null&&(a=K),function(l){var u;return u=l[0],function(s,f,p){return{sum:0,push:function(v){if(!isNaN(parseFloat(v[u])))return this.sum+=parseFloat(v[u])},value:function(){return this.sum},format:a,numInputs:u!=null?0:1}}}},extremes:function(a,l){return l==null&&(l=K),function(u){var s;return s=u[0],function(f,p,v){return{val:null,sorter:d(f!=null?f.sorters:void 0,s),push:function(m){var y,C,S,D;if(D=m[s],(a==="min"||a==="max")&&(D=parseFloat(D),isNaN(D)||(this.val=Math[a](D,(y=this.val)!=null?y:D))),a==="first"&&this.sorter(D,(C=this.val)!=null?C:D)<=0&&(this.val=D),a==="last"&&this.sorter(D,(S=this.val)!=null?S:D)>=0)return this.val=D},value:function(){return this.val},format:function(m){return isNaN(m)?m:l(m)},numInputs:s!=null?0:1}}}},quantile:function(a,l){return l==null&&(l=K),function(u){var s;return s=u[0],function(f,p,v){return{vals:[],push:function(m){var y;if(y=parseFloat(m[s]),!isNaN(y))return this.vals.push(y)},value:function(){var m;return this.vals.length===0?null:(this.vals.sort(function(y,C){return y-C}),m=(this.vals.length-1)*a,(this.vals[Math.floor(m)]+this.vals[Math.ceil(m)])/2)},format:l,numInputs:s!=null?0:1}}}},runningStat:function(a,l,u){return a==null&&(a="mean"),l==null&&(l=1),u==null&&(u=K),function(s){var f;return f=s[0],function(p,v,m){return{n:0,m:0,s:0,push:function(y){var C,S;if(S=parseFloat(y[f]),!isNaN(S))return this.n+=1,this.n===1?this.m=S:(C=this.m+(S-this.m)/this.n,this.s=this.s+(S-this.m)*(S-C),this.m=C)},value:function(){if(a==="mean")return this.n===0?NaN:this.m;if(this.n<=l)return 0;switch(a){case"var":return this.s/(this.n-l);case"stdev":return Math.sqrt(this.s/(this.n-l))}},format:u,numInputs:f!=null?0:1}}}},sumOverSum:function(a){return a==null&&(a=K),function(l){var u,s;return s=l[0],u=l[1],function(f,p,v){return{sumNum:0,sumDenom:0,push:function(m){if(isNaN(parseFloat(m[s]))||(this.sumNum+=parseFloat(m[s])),!isNaN(parseFloat(m[u])))return this.sumDenom+=parseFloat(m[u])},value:function(){return this.sumNum/this.sumDenom},format:a,numInputs:s!=null&&u!=null?0:2}}}},sumOverSumBound80:function(a,l){return a==null&&(a=!0),l==null&&(l=K),function(u){var s,f;return f=u[0],s=u[1],function(p,v,m){return{sumNum:0,sumDenom:0,push:function(y){if(isNaN(parseFloat(y[f]))||(this.sumNum+=parseFloat(y[f])),!isNaN(parseFloat(y[s])))return this.sumDenom+=parseFloat(y[s])},value:function(){var y;return y=a?1:-1,(.821187207574908/this.sumDenom+this.sumNum/this.sumDenom+1.2815515655446004*y*Math.sqrt(.410593603787454/(this.sumDenom*this.sumDenom)+this.sumNum*(1-this.sumNum/this.sumDenom)/(this.sumDenom*this.sumDenom)))/(1+1.642374415149816/this.sumDenom)},format:l,numInputs:f!=null&&s!=null?0:2}}}},fractionOf:function(a,l,u){return l==null&&(l="total"),u==null&&(u=W),function(){var s;return s=1<=arguments.length?gr.call(arguments,0):[],function(f,p,v){return{selector:{total:[[],[]],row:[p,[]],col:[[],v]}[l],inner:a.apply(null,s)(f,p,v),push:function(m){return this.inner.push(m)},format:u,value:function(){return this.inner.value()/f.getAggregator.apply(f,this.selector).inner.value()},numInputs:a.apply(null,s)().numInputs}}}}},n.countUnique=function(a){return n.uniques(function(l){return l.length},a)},n.listUnique=function(a){return n.uniques(function(l){return l.sort(E).join(a)},function(l){return l})},n.max=function(a){return n.extremes("max",a)},n.min=function(a){return n.extremes("min",a)},n.first=function(a){return n.extremes("first",a)},n.last=function(a){return n.extremes("last",a)},n.median=function(a){return n.quantile(.5,a)},n.average=function(a){return n.runningStat("mean",1,a)},n.var=function(a,l){return n.runningStat("var",a,l)},n.stdev=function(a,l){return n.runningStat("stdev",a,l)},i=function(a){return{Count:a.count(ot),"Count Unique Values":a.countUnique(ot),"List Unique Values":a.listUnique(", "),Sum:a.sum(K),"Integer Sum":a.sum(ot),Average:a.average(K),Median:a.median(K),"Sample Variance":a.var(1,K),"Sample Standard Deviation":a.stdev(1,K),Minimum:a.min(K),Maximum:a.max(K),First:a.first(K),Last:a.last(K),"Sum over Sum":a.sumOverSum(K),"80% Upper Bound":a.sumOverSumBound80(!0,K),"80% Lower Bound":a.sumOverSumBound80(!1,K),"Sum as Fraction of Total":a.fractionOf(a.sum(),"total",W),"Sum as Fraction of Rows":a.fractionOf(a.sum(),"row",W),"Sum as Fraction of Columns":a.fractionOf(a.sum(),"col",W),"Count as Fraction of Total":a.fractionOf(a.count(),"total",W),"Count as Fraction of Rows":a.fractionOf(a.count(),"row",W),"Count as Fraction of Columns":a.fractionOf(a.count(),"col",W)}}(n),R={Table:function(a,l){return k(a,l)},"Table Barchart":function(a,l){return t(k(a,l)).barchart()},Heatmap:function(a,l){return t(k(a,l)).heatmap("heatmap",l)},"Row Heatmap":function(a,l){return t(k(a,l)).heatmap("rowheatmap",l)},"Col Heatmap":function(a,l){return t(k(a,l)).heatmap("colheatmap",l)}},h={en:{aggregators:i,renderers:R,localeStrings:{renderError:"An error occurred rendering the PivotTable results.",computeError:"An error occurred computing the PivotTable results.",uiRenderError:"An error occurred rendering the PivotTable UI.",selectAll:"Select All",selectNone:"Select None",tooMany:"(too many to list)",filterResults:"Filter values",apply:"Apply",cancel:"Cancel",totals:"Totals",vs:"vs",by:"by"}}},g=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],o=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],pt=function(a){return("0"+a).substr(-2,2)},c={bin:function(a,l){return function(u){return u[a]-u[a]%l}},dateFormat:function(a,l,u,s,f){var p;return u==null&&(u=!1),s==null&&(s=g),f==null&&(f=o),p=u?"UTC":"",function(v){var m;return m=new Date(Date.parse(v[a])),isNaN(m)?"":l.replace(/%(.)/g,function(y,C){switch(C){case"y":return m["get"+p+"FullYear"]();case"m":return pt(m["get"+p+"Month"]()+1);case"n":return s[m["get"+p+"Month"]()];case"d":return pt(m["get"+p+"Date"]());case"w":return f[m["get"+p+"Day"]()];case"x":return m["get"+p+"Day"]();case"H":return pt(m["get"+p+"Hours"]());case"M":return pt(m["get"+p+"Minutes"]());case"S":return pt(m["get"+p+"Seconds"]());default:return"%"+C}})}}},z=/(\d+)|(\D+)/g,B=/\d/,ct=/^0/,E=function(a){return function(l,u){var s,f,p,v,m,y;if(u!=null&&l==null)return-1;if(l!=null&&u==null)return 1;if(typeof l=="number"&&isNaN(l))return-1;if(typeof u=="number"&&isNaN(u))return 1;if(m=+l,y=+u,m<y)return-1;if(m>y)return 1;if(typeof l=="number"&&typeof u!="number")return-1;if(typeof u=="number"&&typeof l!="number")return 1;if(typeof l=="number"&&typeof u=="number")return 0;if(isNaN(y)&&!isNaN(m))return-1;if(isNaN(m)&&!isNaN(y))return 1;if(s=String(l),p=String(u),s===p)return 0;if(!(B.test(s)&&B.test(p)))return s>p?1:-1;for(s=s.match(z),p=p.match(z);s.length&&p.length;)if(f=s.shift(),v=p.shift(),f!==v)return B.test(f)&&B.test(v)?f.replace(ct,".0")-v.replace(ct,".0"):f>v?1:-1;return s.length-p.length}}(),_t=function(a){var l,u,s,f;s={},u={};for(l in a)f=a[l],s[f]=l,typeof f=="string"&&(u[f.toLowerCase()]=l);return function(p,v){return s[p]!=null&&s[v]!=null?s[p]-s[v]:s[p]!=null?-1:s[v]!=null?1:u[p]!=null&&u[v]!=null?u[p]-u[v]:u[p]!=null?-1:u[v]!=null?1:E(p,v)}},d=function(a,l){var u;if(a!=null){if(t.isFunction(a)){if(u=a(l),t.isFunction(u))return u}else if(a[l]!=null)return a[l]}return E},e=function(){function a(l,u){var s,f,p,v,m,y,C,S,D,M;u==null&&(u={}),this.getAggregator=we(this.getAggregator,this),this.getRowKeys=we(this.getRowKeys,this),this.getColKeys=we(this.getColKeys,this),this.sortKeys=we(this.sortKeys,this),this.arrSort=we(this.arrSort,this),this.input=l,this.aggregator=(s=u.aggregator)!=null?s:n.count()(),this.aggregatorName=(f=u.aggregatorName)!=null?f:"Count",this.colAttrs=(p=u.cols)!=null?p:[],this.rowAttrs=(v=u.rows)!=null?v:[],this.valAttrs=(m=u.vals)!=null?m:[],this.sorters=(y=u.sorters)!=null?y:{},this.rowOrder=(C=u.rowOrder)!=null?C:"key_a_to_z",this.colOrder=(S=u.colOrder)!=null?S:"key_a_to_z",this.derivedAttributes=(D=u.derivedAttributes)!=null?D:{},this.filter=(M=u.filter)!=null?M:function(){return!0},this.tree={},this.rowKeys=[],this.colKeys=[],this.rowTotals={},this.colTotals={},this.allTotal=this.aggregator(this,[],[]),this.sorted=!1,a.forEachRecord(this.input,this.derivedAttributes,function(A){return function(F){if(A.filter(F))return A.processRecord(F)}}(this))}return a.forEachRecord=function(l,u,s){var f,p,v,m,y,C,S,D,M,A,F,L;if(t.isEmptyObject(u)?f=s:f=function(j){var X,P,T;for(X in u)T=u[X],j[X]=(P=T(j))!=null?P:j[X];return s(j)},t.isFunction(l))return l(f);if(t.isArray(l))if(t.isArray(l[0])){A=[];for(v in l)if(ut.call(l,v)&&(p=l[v],v>0)){D={},M=l[0];for(m in M)ut.call(M,m)&&(y=M[m],D[y]=p[m]);A.push(f(D))}return A}else{for(F=[],C=0,S=l.length;C<S;C++)D=l[C],F.push(f(D));return F}else{if(l instanceof t)return L=[],t("thead > tr > th",l).each(function(j){return L.push(t(this).text())}),t("tbody > tr",l).each(function(j){return D={},t("td",this).each(function(X){return D[L[X]]=t(this).text()}),f(D)});throw new Error("unknown input format")}},a.prototype.forEachMatchingRecord=function(l,u){return a.forEachRecord(this.input,this.derivedAttributes,function(s){return function(f){var p,v,m;if(s.filter(f)){for(p in l)if(m=l[p],m!==((v=f[p])!=null?v:"null"))return;return u(f)}}}(this))},a.prototype.arrSort=function(l){var u,s;return s=function(){var f,p,v;for(v=[],f=0,p=l.length;f<p;f++)u=l[f],v.push(d(this.sorters,u));return v}.call(this),function(f,p){var v,m,y;for(m in s)if(ut.call(s,m)&&(y=s[m],v=y(f[m],p[m]),v!==0))return v;return 0}},a.prototype.sortKeys=function(){var l;if(!this.sorted){switch(this.sorted=!0,l=function(u){return function(s,f){return u.getAggregator(s,f).value()}}(this),this.rowOrder){case"value_a_to_z":this.rowKeys.sort(function(u){return function(s,f){return E(l(s,[]),l(f,[]))}}());break;case"value_z_to_a":this.rowKeys.sort(function(u){return function(s,f){return-E(l(s,[]),l(f,[]))}}());break;default:this.rowKeys.sort(this.arrSort(this.rowAttrs))}switch(this.colOrder){case"value_a_to_z":return this.colKeys.sort(function(u){return function(s,f){return E(l([],s),l([],f))}}());case"value_z_to_a":return this.colKeys.sort(function(u){return function(s,f){return-E(l([],s),l([],f))}}());default:return this.colKeys.sort(this.arrSort(this.colAttrs))}}},a.prototype.getColKeys=function(){return this.sortKeys(),this.colKeys},a.prototype.getRowKeys=function(){return this.sortKeys(),this.rowKeys},a.prototype.processRecord=function(l){var u,s,f,p,v,m,y,C,S,D,M,A,F;for(u=[],A=[],C=this.colAttrs,p=0,v=C.length;p<v;p++)F=C[p],u.push((S=l[F])!=null?S:"null");for(D=this.rowAttrs,y=0,m=D.length;y<m;y++)F=D[y],A.push((M=l[F])!=null?M:"null");if(f=A.join("\0"),s=u.join("\0"),this.allTotal.push(l),A.length!==0&&(this.rowTotals[f]||(this.rowKeys.push(A),this.rowTotals[f]=this.aggregator(this,A,[])),this.rowTotals[f].push(l)),u.length!==0&&(this.colTotals[s]||(this.colKeys.push(u),this.colTotals[s]=this.aggregator(this,[],u)),this.colTotals[s].push(l)),u.length!==0&&A.length!==0)return this.tree[f]||(this.tree[f]={}),this.tree[f][s]||(this.tree[f][s]=this.aggregator(this,A,u)),this.tree[f][s].push(l)},a.prototype.getAggregator=function(l,u){var s,f,p;return p=l.join("\0"),f=u.join("\0"),l.length===0&&u.length===0?s=this.allTotal:l.length===0?s=this.colTotals[f]:u.length===0?s=this.rowTotals[p]:s=this.tree[p][f],s!=null?s:{value:function(){return null},format:function(){return""}}},a}(),t.pivotUtilities={aggregatorTemplates:n,aggregators:i,renderers:R,derivers:c,locales:h,naturalSort:E,numberFormat:w,sortAs:_t,PivotData:e},k=function(a,l){var u,s,f,p,v,m,y,C,S,D,M,A,F,L,j,X,P,T,ee,Tt,q,le,$,At;m={table:{clickCallback:null,rowTotals:!0,colTotals:!0},localeStrings:{totals:"Totals"}},l=t.extend(!0,{},m,l),f=a.colAttrs,A=a.rowAttrs,L=a.getRowKeys(),v=a.getColKeys(),l.table.clickCallback&&(y=function(_,at,kt){var ft,gt,tt;gt={};for(tt in f)ut.call(f,tt)&&(ft=f[tt],kt[tt]!=null&&(gt[ft]=kt[tt]));for(tt in A)ut.call(A,tt)&&(ft=A[tt],at[tt]!=null&&(gt[ft]=at[tt]));return function(Rt){return l.table.clickCallback(Rt,_,gt,a)}}),M=document.createElement("table"),M.className="pvtTable",j=function(_,at,kt){var ft,gt,tt,Rt,Lt,dt,se,wt;if(at!==0){for(Rt=!0,wt=ft=0,Lt=kt;0<=Lt?ft<=Lt:ft>=Lt;wt=0<=Lt?++ft:--ft)_[at-1][wt]!==_[at][wt]&&(Rt=!1);if(Rt)return-1}for(gt=0;at+gt<_.length;){for(se=!1,wt=tt=0,dt=kt;0<=dt?tt<=dt:tt>=dt;wt=0<=dt?++tt:--tt)_[at][wt]!==_[at+gt][wt]&&(se=!0);if(se)break;gt++}return gt},ee=document.createElement("thead");for(S in f)if(ut.call(f,S)){s=f[S],q=document.createElement("tr"),parseInt(S)===0&&A.length!==0&&(T=document.createElement("th"),T.setAttribute("colspan",A.length),T.setAttribute("rowspan",f.length),q.appendChild(T)),T=document.createElement("th"),T.className="pvtAxisLabel",T.textContent=s,q.appendChild(T);for(C in v)ut.call(v,C)&&(p=v[C],At=j(v,parseInt(C),parseInt(S)),At!==-1&&(T=document.createElement("th"),T.className="pvtColLabel",T.textContent=p[S],T.setAttribute("colspan",At),parseInt(S)===f.length-1&&A.length!==0&&T.setAttribute("rowspan",2),q.appendChild(T)));parseInt(S)===0&&l.table.rowTotals&&(T=document.createElement("th"),T.className="pvtTotalLabel pvtRowTotalLabel",T.innerHTML=l.localeStrings.totals,T.setAttribute("rowspan",f.length+(A.length===0?0:1)),q.appendChild(T)),ee.appendChild(q)}if(A.length!==0){q=document.createElement("tr");for(C in A)ut.call(A,C)&&(D=A[C],T=document.createElement("th"),T.className="pvtAxisLabel",T.textContent=D,q.appendChild(T));T=document.createElement("th"),f.length===0&&(T.className="pvtTotalLabel pvtRowTotalLabel",T.innerHTML=l.localeStrings.totals),q.appendChild(T),ee.appendChild(q)}M.appendChild(ee),X=document.createElement("tbody");for(C in L)if(ut.call(L,C)){F=L[C],q=document.createElement("tr");for(S in F)ut.call(F,S)&&(le=F[S],At=j(L,parseInt(C),parseInt(S)),At!==-1&&(T=document.createElement("th"),T.className="pvtRowLabel",T.textContent=le,T.setAttribute("rowspan",At),parseInt(S)===A.length-1&&f.length!==0&&T.setAttribute("colspan",2),q.appendChild(T)));for(S in v)ut.call(v,S)&&(p=v[S],u=a.getAggregator(F,p),$=u.value(),P=document.createElement("td"),P.className="pvtVal row"+C+" col"+S,P.textContent=u.format($),P.setAttribute("data-value",$),y!=null&&(P.onclick=y($,F,p)),q.appendChild(P));(l.table.rowTotals||f.length===0)&&(Tt=a.getAggregator(F,[]),$=Tt.value(),P=document.createElement("td"),P.className="pvtTotal rowTotal",P.textContent=Tt.format($),P.setAttribute("data-value",$),y!=null&&(P.onclick=y($,F,[])),P.setAttribute("data-for","row"+C),q.appendChild(P)),X.appendChild(q)}if(l.table.colTotals||A.length===0){q=document.createElement("tr"),(l.table.colTotals||A.length===0)&&(T=document.createElement("th"),T.className="pvtTotalLabel pvtColTotalLabel",T.innerHTML=l.localeStrings.totals,T.setAttribute("colspan",A.length+(f.length===0?0:1)),q.appendChild(T));for(S in v)ut.call(v,S)&&(p=v[S],Tt=a.getAggregator([],p),$=Tt.value(),P=document.createElement("td"),P.className="pvtTotal colTotal",P.textContent=Tt.format($),P.setAttribute("data-value",$),y!=null&&(P.onclick=y($,[],p)),P.setAttribute("data-for","col"+S),q.appendChild(P));(l.table.rowTotals||f.length===0)&&(Tt=a.getAggregator([],[]),$=Tt.value(),P=document.createElement("td"),P.className="pvtGrandTotal",P.textContent=Tt.format($),P.setAttribute("data-value",$),y!=null&&(P.onclick=y($,[],[])),q.appendChild(P)),X.appendChild(q)}return M.appendChild(X),M.setAttribute("data-numrows",L.length),M.setAttribute("data-numcols",v.length),M},t.fn.pivot=function(a,l,u){var s,f,p,v,m,y,C,S;u==null&&(u="en"),h[u]==null&&(u="en"),s={cols:[],rows:[],vals:[],rowOrder:"key_a_to_z",colOrder:"key_a_to_z",dataClass:e,filter:function(){return!0},aggregator:n.count()(),aggregatorName:"Count",sorters:{},derivedAttributes:{},renderer:k},v=t.extend(!0,{},h.en.localeStrings,h[u].localeStrings),p={rendererOptions:{localeStrings:v},localeStrings:v},m=t.extend(!0,{},p,t.extend({},s,l)),C=null;try{y=new m.dataClass(a,m);try{C=m.renderer(y,m.rendererOptions)}catch(D){f=D,typeof console!="undefined"&&console!==null&&console.error(f.stack),C=t("<span>").html(m.localeStrings.renderError)}}catch(D){f=D,typeof console!="undefined"&&console!==null&&console.error(f.stack),C=t("<span>").html(m.localeStrings.computeError)}for(S=this[0];S.hasChildNodes();)S.removeChild(S.lastChild);return this.append(C)},t.fn.pivotUI=function(a,l,u,s){var f,p,v,m,y,C,S,D,M,A,F,L,j,X,P,T,ee,Tt,q,le,$,At,_,at,kt,ft,gt,tt,Rt,Lt,dt,se,wt,Ie,We,ye,Je,Xt,Ze,Qe,ne,ue,ke,Fe,mt;u==null&&(u=!1),s==null&&(s="en"),h[s]==null&&(s="en"),D={derivedAttributes:{},aggregators:h[s].aggregators,renderers:h[s].renderers,hiddenAttributes:[],hiddenFromAggregators:[],hiddenFromDragDrop:[],menuLimit:500,cols:[],rows:[],vals:[],rowOrder:"key_a_to_z",colOrder:"key_a_to_z",dataClass:e,exclusions:{},inclusions:{},unusedAttrsVertical:85,autoSortUnusedAttrs:!1,onRefresh:null,showUI:!0,filter:function(){return!0},sorters:{}},q=t.extend(!0,{},h.en.localeStrings,h[s].localeStrings),Tt={rendererOptions:{localeStrings:q},localeStrings:q},A=this.data("pivotUIOptions"),A==null||u?_=t.extend(!0,{},Tt,t.extend({},D,l)):_=A;try{y={},le=[],ft=0,e.forEachRecord(a,_.derivedAttributes,function(N){var Y,et,Kt,vt;if(_.filter(N)){le.push(N);for(Y in N)ut.call(N,Y)&&y[Y]==null&&(y[Y]={},ft>0&&(y[Y].null=ft));for(Y in y)vt=(Kt=N[Y])!=null?Kt:"null",(et=y[Y])[vt]==null&&(et[vt]=0),y[Y][vt]++;return ft++}}),ne=t("<table>",{class:"pvtUi"}).attr("cellpadding",5),Ie=t("<td>").addClass("pvtUiCell"),wt=t("<select>").addClass("pvtRenderer").appendTo(Ie).bind("change",function(){return dt()}),gt=_.renderers;for(mt in gt)ut.call(gt,mt)&&t("<option>").val(mt).html(mt).appendTo(wt);if(ue=t("<td>").addClass("pvtAxisContainer pvtUnused pvtUiCell"),ye=function(){var N;N=[];for(f in y)Zt.call(_.hiddenAttributes,f)<0&&N.push(f);return N}(),Je=function(){var N,Y,et;for(et=[],N=0,Y=ye.length;N<Y;N++)C=ye[N],Zt.call(_.hiddenFromAggregators,C)<0&&et.push(C);return et}(),Xt=function(){var N,Y,et;for(et=[],N=0,Y=ye.length;N<Y;N++)C=ye[N],Zt.call(_.hiddenFromDragDrop,C)<0&&et.push(C);return et}(),Fe=!1,_.unusedAttrsVertical==="auto"?ke=120:ke=parseInt(_.unusedAttrsVertical),!isNaN(ke)){for(m=0,X=0,P=Xt.length;X<P;X++)f=Xt[X],m+=f.length;Fe=m>ke}_.unusedAttrsVertical===!0||Fe?ue.addClass("pvtVertList"):ue.addClass("pvtHorizList"),F=function(N){var Y,et,Kt,vt,xt,Bt,ce,re,jt,nt,qt,Vt,fe,Ft,J,Ot,de,Z,zt;if(zt=function(){var Gt;Gt=[];for(J in y[N])Gt.push(J);return Gt}(),re=!1,Z=t("<div>").addClass("pvtFilterBox").hide(),Z.append(t("<h4>").append(t("<span>").text(N),t("<span>").addClass("count").text("("+zt.length+")"))),zt.length>_.menuLimit)Z.append(t("<p>").html(_.localeStrings.tooMany));else for(zt.length>5&&(vt=t("<p>").appendTo(Z),fe=d(_.sorters,N),qt=_.localeStrings.filterResults,t("<input>",{type:"text"}).appendTo(vt).attr({placeholder:qt,class:"pvtSearch"}).bind("keyup",function(){var Gt,Wt,bt;return bt=t(this).val().toLowerCase().trim(),Wt=function(Jt,Un){return function(Yn){var $e,vn;return $e=bt.substring(Jt.length).trim(),$e.length===0?!0:(vn=Math.sign(fe(Yn.toLowerCase(),$e)),Zt.call(Un,vn)>=0)}},Gt=bt.indexOf(">=")===0?Wt(">=",[1,0]):bt.indexOf("<=")===0?Wt("<=",[-1,0]):bt.indexOf(">")===0?Wt(">",[1]):bt.indexOf("<")===0?Wt("<",[-1]):bt.indexOf("~")===0?function(Jt){return bt.substring(1).trim().length===0?!0:Jt.toLowerCase().match(bt.substring(1))}:function(Jt){return Jt.toLowerCase().indexOf(bt)!==-1},Z.find(".pvtCheckContainer p label span.value").each(function(){return Gt(t(this).text())?t(this).parent().parent().show():t(this).parent().parent().hide()})}),vt.append(t("<br>")),t("<button>",{type:"button"}).appendTo(vt).html(_.localeStrings.selectAll).bind("click",function(){return Z.find("input:visible:not(:checked)").prop("checked",!0).toggleClass("changed"),!1}),t("<button>",{type:"button"}).appendTo(vt).html(_.localeStrings.selectNone).bind("click",function(){return Z.find("input:visible:checked").prop("checked",!1).toggleClass("changed"),!1})),et=t("<div>").addClass("pvtCheckContainer").appendTo(Z),Vt=zt.sort(d(_.sorters,N)),nt=0,jt=Vt.length;nt<jt;nt++)Ot=Vt[nt],de=y[N][Ot],xt=t("<label>"),Bt=!1,_.inclusions[N]?Bt=Zt.call(_.inclusions[N],Ot)<0:_.exclusions[N]&&(Bt=Zt.call(_.exclusions[N],Ot)>=0),re||(re=Bt),t("<input>").attr("type","checkbox").addClass("pvtFilter").attr("checked",!Bt).data("filter",[N,Ot]).appendTo(xt).bind("change",function(){return t(this).toggleClass("changed")}),xt.append(t("<span>").addClass("value").text(Ot)),xt.append(t("<span>").addClass("count").text("("+de+")")),et.append(t("<p>").append(xt));return Kt=function(){return Z.find("[type='checkbox']").length>Z.find("[type='checkbox']:checked").length?Y.addClass("pvtFilteredAttribute"):Y.removeClass("pvtFilteredAttribute"),Z.find(".pvtSearch").val(""),Z.find(".pvtCheckContainer p").show(),Z.hide()},ce=t("<p>").appendTo(Z),zt.length<=_.menuLimit&&t("<button>",{type:"button"}).text(_.localeStrings.apply).appendTo(ce).bind("click",function(){return Z.find(".changed").removeClass("changed").length&&dt(),Kt()}),t("<button>",{type:"button"}).text(_.localeStrings.cancel).appendTo(ce).bind("click",function(){return Z.find(".changed:checked").removeClass("changed").prop("checked",!1),Z.find(".changed:not(:checked)").removeClass("changed").prop("checked",!0),Kt()}),Ft=t("<span>").addClass("pvtTriangle").html(" &#x25BE;").bind("click",function(Gt){var Wt,bt,Jt;return bt=t(Gt.currentTarget).position(),Wt=bt.left,Jt=bt.top,Z.css({left:Wt+10,top:Jt+10}).show()}),Y=t("<li>").addClass("axis_"+L).append(t("<span>").addClass("pvtAttr").text(N).data("attrName",N).append(Ft)),re&&Y.addClass("pvtFilteredAttribute"),ue.append(Y).append(Z)};for(L in Xt)ut.call(Xt,L)&&(v=Xt[L],F(v));Ze=t("<tr>").appendTo(ne),p=t("<select>").addClass("pvtAggregator").bind("change",function(){return dt()}),tt=_.aggregators;for(mt in tt)ut.call(tt,mt)&&p.append(t("<option>").val(mt).html(mt));for(at={key_a_to_z:{rowSymbol:"&varr;",colSymbol:"&harr;",next:"value_a_to_z"},value_a_to_z:{rowSymbol:"&darr;",colSymbol:"&rarr;",next:"value_z_to_a"},value_z_to_a:{rowSymbol:"&uarr;",colSymbol:"&larr;",next:"key_a_to_z"}},We=t("<a>",{role:"button"}).addClass("pvtRowOrder").data("order",_.rowOrder).html(at[_.rowOrder].rowSymbol).bind("click",function(){return t(this).data("order",at[t(this).data("order")].next),t(this).html(at[t(this).data("order")].rowSymbol),dt()}),S=t("<a>",{role:"button"}).addClass("pvtColOrder").data("order",_.colOrder).html(at[_.colOrder].colSymbol).bind("click",function(){return t(this).data("order",at[t(this).data("order")].next),t(this).html(at[t(this).data("order")].colSymbol),dt()}),t("<td>").addClass("pvtVals pvtUiCell").appendTo(Ze).append(p).append(We).append(S).append(t("<br>")),t("<td>").addClass("pvtAxisContainer pvtHorizList pvtCols pvtUiCell").appendTo(Ze),Qe=t("<tr>").appendTo(ne),Qe.append(t("<td>").addClass("pvtAxisContainer pvtRows pvtUiCell").attr("valign","top")),kt=t("<td>").attr("valign","top").addClass("pvtRendererArea").appendTo(Qe),_.unusedAttrsVertical===!0||Fe?(ne.find("tr:nth-child(1)").prepend(Ie),ne.find("tr:nth-child(2)").prepend(ue)):ne.prepend(t("<tr>").append(Ie).append(ue)),this.html(ne),Rt=_.cols,$=0,T=Rt.length;$<T;$++)mt=Rt[$],this.find(".pvtCols").append(this.find(".axis_"+t.inArray(mt,Xt)));for(Lt=_.rows,At=0,ee=Lt.length;At<ee;At++)mt=Lt[At],this.find(".pvtRows").append(this.find(".axis_"+t.inArray(mt,Xt)));_.aggregatorName!=null&&this.find(".pvtAggregator").val(_.aggregatorName),_.rendererName!=null&&this.find(".pvtRenderer").val(_.rendererName),_.showUI||this.find(".pvtUiCell").hide(),j=!0,se=function(N){return function(){var Y,et,Kt,vt,xt,Bt,ce,re,jt,nt,qt,Vt,fe,Ft;if(nt={derivedAttributes:_.derivedAttributes,localeStrings:_.localeStrings,rendererOptions:_.rendererOptions,sorters:_.sorters,cols:[],rows:[],dataClass:_.dataClass},xt=(re=_.aggregators[p.val()]([])().numInputs)!=null?re:0,Ft=[],N.find(".pvtRows li span.pvtAttr").each(function(){return nt.rows.push(t(this).data("attrName"))}),N.find(".pvtCols li span.pvtAttr").each(function(){return nt.cols.push(t(this).data("attrName"))}),N.find(".pvtVals select.pvtAttrDropdown").each(function(){if(xt===0)return t(this).remove();if(xt--,t(this).val()!=="")return Ft.push(t(this).val())}),xt!==0)for(ce=N.find(".pvtVals"),mt=qt=0,jt=xt;0<=jt?qt<jt:qt>jt;mt=0<=jt?++qt:--qt){for(vt=t("<select>").addClass("pvtAttrDropdown").append(t("<option>")).bind("change",function(){return dt()}),Vt=0,Kt=Je.length;Vt<Kt;Vt++)v=Je[Vt],vt.append(t("<option>").val(v).text(v));ce.append(vt)}if(j&&(Ft=_.vals,L=0,N.find(".pvtVals select.pvtAttrDropdown").each(function(){return t(this).val(Ft[L]),L++}),j=!1),nt.aggregatorName=p.val(),nt.vals=Ft,nt.aggregator=_.aggregators[p.val()](Ft),nt.renderer=_.renderers[wt.val()],nt.rowOrder=We.data("order"),nt.colOrder=S.data("order"),Y={},N.find("input.pvtFilter").not(":checked").each(function(){var J;return J=t(this).data("filter"),Y[J[0]]!=null?Y[J[0]].push(J[1]):Y[J[0]]=[J[1]]}),et={},N.find("input.pvtFilter:checked").each(function(){var J;if(J=t(this).data("filter"),Y[J[0]]!=null)return et[J[0]]!=null?et[J[0]].push(J[1]):et[J[0]]=[J[1]]}),nt.filter=function(J){var Ot,de,Z,zt;if(!_.filter(J))return!1;for(de in Y)if(Ot=Y[de],Z=""+((zt=J[de])!=null?zt:"null"),Zt.call(Ot,Z)>=0)return!1;return!0},kt.pivot(le,nt),Bt=t.extend({},_,{cols:nt.cols,rows:nt.rows,colOrder:nt.colOrder,rowOrder:nt.rowOrder,vals:Ft,exclusions:Y,inclusions:et,inclusionsInfo:et,aggregatorName:p.val(),rendererName:wt.val()}),N.data("pivotUIOptions",Bt),_.autoSortUnusedAttrs&&(fe=N.find("td.pvtUnused.pvtAxisContainer"),t(fe).children("li").sort(function(J,Ot){return E(t(J).text(),t(Ot).text())}).appendTo(fe)),kt.css("opacity",1),_.onRefresh!=null)return _.onRefresh(Bt)}}(this),dt=function(N){return function(){return kt.css("opacity",.5),setTimeout(se,10)}}(this),dt(),document.querySelectorAll(".pvtAxisContainer").forEach(function(N){O.create(N,{group:"pvtAxisContainer",draggable:"li",animation:150,ghostClass:"pvtPlaceholder",onEnd:function(Y){dt()}})})}catch(N){M=N,typeof console!="undefined"&&console!==null&&console.error(M.stack),this.html(_.localeStrings.uiRenderError)}return this},t.fn.heatmap=function(a,l){var u,s,f,p,v,m,y,C,S,D,M;switch(a==null&&(a="heatmap"),C=this.data("numrows"),y=this.data("numcols"),u=l!=null&&(S=l.heatmap)!=null?S.colorScaleGenerator:void 0,u==null&&(u=function(A){var F,L;return L=Math.min.apply(Math,A),F=Math.max.apply(Math,A),function(j){var X;return X=255-Math.round(255*(j-L)/(F-L)),"rgb(255,"+X+","+X+")"}}),s=function(A){return function(F){var L,j,X;return j=function(P){return A.find(F).each(function(){var T;if(T=t(this).data("value"),T!=null&&isFinite(T))return P(T,t(this))})},X=[],j(function(P){return X.push(P)}),L=u(X),j(function(P,T){return T.css("background-color",L(P))})}}(this),a){case"heatmap":s(".pvtVal");break;case"rowheatmap":for(f=v=0,D=C;0<=D?v<D:v>D;f=0<=D?++v:--v)s(".pvtVal.row"+f);break;case"colheatmap":for(p=m=0,M=y;0<=M?m<M:m>M;p=0<=M?++m:--m)s(".pvtVal.col"+p)}return s(".pvtTotal.rowTotal"),s(".pvtTotal.colTotal"),this},t.fn.barchart=function(a){var l,u,s,f,p;for(f=this.data("numrows"),this.data("numcols"),l=function(v){return function(m){var y,C,S,D,M,A;return y=function(F){return v.find(m).each(function(){var L;if(L=t(this).data("value"),L!=null&&isFinite(L))return F(L,t(this))})},A=[],y(function(F){return A.push(F)}),C=Math.max.apply(Math,A),C<0&&(C=0),D=C,S=Math.min.apply(Math,A),S<0&&(D=C-S),M=function(F){return 100*F/(1.4*D)},y(function(F,L){var j,X,P,T;return P=L.text(),T=t("<div>").css({position:"relative",height:"55px"}),X="gray",j=0,S<0&&(j=M(-S)),F<0&&(j+=M(F),X="darkred",F=-F),T.append(t("<div>").css({position:"absolute",bottom:j+"%",left:0,right:0,height:M(F)+"%","background-color":X})),T.append(t("<div>").text(P).css({position:"relative","padding-left":"5px","padding-right":"5px"})),L.css({padding:0,"padding-top":"5px","text-align":"center"}).html(T)})}}(this),u=s=0,p=f;0<=p?s<p:s>p;u=0<=p?++s:--s)l(".pvtVal.row"+u);return l(".pvtTotal.colTotal"),this}}function vr(t){let e=[],r=t.getElementsByTagName("tr");for(let n=0;n<r.length;n++){let i=r[n].querySelectorAll("td,th"),o=[];for(let c=0;c<i.length;c++)o.push(i[c].innerText);e.push(o.join(","))}return e=e.join(`
`),e}function br(t){let e=vr(t),r=new Blob([e],{type:"text/csv"}),n=document.createElement("a");n.download="pivot.csv",n.href=window.URL.createObjectURL(r),n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n)}function Ar(t){let e={};if(window.location.hash)try{e=JSON.parse(atob(window.location.hash.slice(1)))}catch(i){console.log(i)}e.onRefresh=yr,mr(t);let r=t("#preview").clone();r.find("tr.stats-th").remove(),t(".pivot-table").pivotUI(r,e);let n=document.querySelector("#button-excel");n&&n.addEventListener("click",i=>{i.preventDefault();let o=document.querySelector(".pvtTable");typeof o!="undefined"&&o!=null&&br(o)})}function yr(t){const e=(({aggregatorName:o,rows:c,cols:d,rendererName:h,vals:g})=>({aggregatorName:o,rows:c,cols:d,rendererName:h,vals:g}))(t),r=JSON.stringify(e);let n=btoa(r),i=document.getElementById("pivot-bookmark");i&&i.setAttribute("href",i.dataset.baseurl+"#"+n)}export{Ar as pivotSetup};
