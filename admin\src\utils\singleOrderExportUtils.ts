import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import { Order } from '@/types/order';
import {
  formatDateForOrderDisplay,
  getOrderPaymentStatusLabel,
  getOrderStatusLabel,
  EXCEL_EXPORT
} from '@/constants/constants';
import { formatCurrency } from '@/lib/utils';

export const exportSingleOrderToExcel = async (order: Order, selectedFields: string[]) => {
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet(`DonHang_${order.id}`);

  // Add title
  worksheet.addRow([`CHI TIẾT ĐƠN HÀNG${order.is_showroom ? " SHOWROOM ": " "}#${order.id}`]);
  worksheet.getRow(1).font = { bold: true, size: 16 };
  worksheet.addRow([]);

  // Add selected order information
  selectedFields.forEach(fieldValue => {
    const fieldData = getFieldData(order, fieldValue);
    if (fieldData) {
      worksheet.addRow([fieldData.label, fieldData.value]);
    }
  });

  // Add items detail section if items_detail is selected
  if (selectedFields.includes('items_detail')) {
    worksheet.addRow([]);
    worksheet.addRow(['CHI TIẾT SẢN PHẨM']);
    worksheet.getRow(worksheet.lastRow!.number).font = { bold: true };

    // Add items header
    const isShowroomOrder = order.is_showroom;
    let itemHeaders: string[];

    if (isShowroomOrder) {
      itemHeaders = ['STT', 'Mã hàng', 'Tên sản phẩm', 'Phân loại', 'Số lượng', 'Khối lượng (kg)'];
    } else {
      itemHeaders = ['STT', 'Mã hàng', 'Tên sản phẩm', 'Phân loại', 'Số lượng', 'Đơn giá', 'Thành tiền'];
    }

    worksheet.addRow(itemHeaders);
    worksheet.getRow(worksheet.lastRow!.number).font = { bold: true };

    // Add items data
    order.items.forEach((item, index) => {
      if (isShowroomOrder) {
        // For showroom orders: STT, Product Code, Product, Variant, Quantity, Weight
        worksheet.addRow([
          index + 1,
          item.product_code || 'N/A',
          item.product_name,
          item.variant_name || 'N/A',
          item.quantity,
          item.product_weight || 'N/A'
        ]);
      } else {
        // For regular orders: STT, Product Code, Product, Variant, Quantity, Unit Price, Total Price
        worksheet.addRow([
          index + 1,
          item.product_code || 'N/A',
          item.product_name,
          item.variant_name || 'N/A',
          item.quantity,
          formatCurrency(item.price),
          formatCurrency(item.total_price)
        ]);
      }
    });

    // Add total row only for non-showroom orders
    if (!isShowroomOrder) {
      worksheet.addRow([]);
      worksheet.addRow(['', '', '', '', '', 'TỔNG CỘNG:', formatCurrency(order.total_price)]);
      worksheet.getRow(worksheet.lastRow!.number).font = { bold: true };
    }
  }

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    if (column.values && column.values.length > 0) {
      const lengths = column.values
        .filter(v => v !== null && v !== undefined)
        .map(v => v.toString().length);
      if (lengths.length > 0) {
        const maxLength = Math.max(...lengths);
        column.width = Math.min(Math.max(maxLength + 2, 10), 50);
      }
    }
  });

  // Generate and download file
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: EXCEL_EXPORT.FILE_TYPE });
  const fileName = `DonHang_${order.id}_${new Date().toISOString().split('T')[0]}.xlsx`;
  saveAs(blob, fileName);
};

const getFieldData = (order: Order, fieldValue: string): { label: string; value: string } | null => {
  switch (fieldValue) {
    case 'id':
      return { label: 'Mã đơn hàng:', value: order.id.toString() };
    case 'created_at':
      return { label: 'Ngày tạo:', value: formatDateForOrderDisplay(order.created_at) };
    case 'customer_name':
      return { label: 'Khách hàng:', value: order.user?.full_name || 'N/A' };
    case 'phone_number':
      return { label: 'Số điện thoại:', value: order.phone_number || 'N/A' };
    case 'email':
      return { label: 'Email:', value: order.email || 'N/A' };
    case 'shipping_address':
      return { label: 'Địa chỉ giao hàng:', value: order.shipping_address || 'N/A' };
    case 'ward':
      return { label: 'Phường/Xã:', value: order.ward || 'N/A' };
    case 'district':
      return { label: 'Quận/Huyện:', value: order.district || 'N/A' };
    case 'city':
      return { label: 'Tỉnh/Thành phố:', value: order.city || 'N/A' };
    case 'items_summary':
      return {
        label: 'Sản phẩm (Tóm tắt):',
        value: order.items.map(item =>
          `${item.product_name}${item.variant_name ? ` (${item.variant_name})` : ''} x${item.quantity}`
        ).join('; ')
      };
    case 'payment_method':
      return { label: 'Phương thức thanh toán:', value: order.payment_method || 'N/A' };
    case 'payment_status':
      return { label: 'Trạng thái thanh toán:', value: getOrderPaymentStatusLabel(order.payment_status) };
    case 'shipping_unit':
      return { label: 'Đơn vị vận chuyển:', value: order.shipping_unit || 'N/A' };
    case 'shipping_fee':
      return { label: 'Phí vận chuyển:', value: formatCurrency(order.shipping_fee) };
    case 'discount':
      return { label: 'Giảm giá:', value: formatCurrency(order.discount) };
    case 'tax':
      return { label: 'Thuế:', value: formatCurrency(order.tax) };
    case 'total_price':
      return { label: 'Tổng tiền hàng:', value: formatCurrency(order.total_price) };
    case 'final_total':
      return { label: 'Tổng thanh toán:', value: formatCurrency(order.final_total) };
    case 'status':
      return { label: 'Trạng thái đơn hàng:', value: getOrderStatusLabel(order.status) };
    case 'sales_admin':
      return {
        label: 'Nhân viên bán hàng:',
        value: order.sales_admin
          ? `${order.sales_admin.first_name} ${order.sales_admin.last_name}`.trim() || order.sales_admin.username
          : 'N/A'
      };
    case 'delivery_staff':
      return {
        label: 'Nhân viên giao hàng:',
        value: order.delivery_staff
          ? `${order.delivery_staff.first_name} ${order.delivery_staff.last_name}`.trim() || order.delivery_staff.username
          : 'N/A'
      };
    case 'delivery_date':
      return { label: 'Ngày giao hàng:', value: order.delivery_date || 'N/A' };
    case 'delivery_time':
      return { label: 'Giờ giao hàng:', value: order.delivery_time || 'N/A' };
    case 'confirmation_time':
      return {
        label: 'Thời gian xác nhận:',
        value: order.confirmation_time ? formatDateForOrderDisplay(order.confirmation_time) : 'N/A'
      };
    case 'completion_time':
      return {
        label: 'Thời gian hoàn thành:',
        value: order.completion_time ? formatDateForOrderDisplay(order.completion_time) : 'N/A'
      };
    case 'notes':
      return { label: 'Ghi chú:', value: order.notes || 'N/A' };
    case 'is_showroom':
      return { label: 'Showroom:', value: order.is_showroom ? 'Có' : 'Không' };
    default:
      return null;
  }
};
