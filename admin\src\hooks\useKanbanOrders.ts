import { useQuery } from "@tanstack/react-query";
import { Order } from "../types/order";
import { useOrdersBase } from "./useOrdersBase";

interface UseKanbanOrdersParams {
  userId?: number;
  userRole?: string;
}

export function useKanbanOrders({ userId, userRole }: UseKanbanOrdersParams = {}) {
  const {
    statusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedDeliveryStaff,
    selectedDeliveryStaffs,
    selectedShippingUnit,
    selectedShippingUnits,
    statusCounts,
    staffList,
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleSalesAdminsChange,
    handleDeliveryStaffChange,
    handleDeliveryStaffsChange,
    handleShippingUnitChange,
    handleShippingUnitsChange,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
    getOrdersQueryFn,
  } = useOrdersBase({
    userId,
    userRole,
    queryKey: "kanban_orders",
    noPage: true,
  });

  // Fetch orders without pagination
  const { data: ordersData, isLoading } = useQuery<{ results: Order[]; count: number }>({
    queryKey: [
      "kanban_orders",
      statusFilter,
      searchParams,
      selectedSalesAdmin?.id,
      selectedSalesAdmins,
      selectedDeliveryStaff?.id,
      selectedDeliveryStaffs,
      selectedShippingUnit,
      selectedShippingUnits,
    ],
    queryFn: getOrdersQueryFn,
  });

  return {
    // States
    statusFilter,
    searchParams,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedDeliveryStaff,
    selectedDeliveryStaffs,
    selectedShippingUnit,
    selectedShippingUnits,

    // Data
    orders: ordersData?.results || [],
    statusCounts,
    staffList,
    isLoading,

    // Actions
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleSalesAdminsChange,
    handleDeliveryStaffChange,
    handleDeliveryStaffsChange,
    handleShippingUnitChange,
    handleShippingUnitsChange,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
  };
}
