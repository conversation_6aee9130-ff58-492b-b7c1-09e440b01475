/**
 * Footer component that displays build information
 */
export function Footer() {
  // Build timestamp is added at build time with environment substitution
  const buildDate = `${import.meta.env.VITE_BUILD_DATE || "Development"}`;
  
  // Get short commit hash
  const commitHash = import.meta.env.VITE_COMMIT_HASH || "dev";
  const shortHash = commitHash.substring(0, 7);

  return (
    <footer className="border-t py-4 px-6 mt-auto">
      <div className="container mx-auto flex flex-col md:flex-row justify-between items-center text-xs text-muted-foreground">
        <div>© {new Date().getFullYear()} 3T Admin</div>
        <div className="flex items-center gap-4 mt-2 md:mt-0">
          <div>Build: {buildDate}</div>
          <div>
            Commit: <span className="text-primary">{shortHash}</span>
          </div>
        </div>
      </div>
    </footer>
  );
} 