{% extends "explorer/base.html" %}
{% load explorer_tags i18n %}

{% block sql_explorer_content %}
<div class="container">
    <h3>Connections</h3>
    <div class="mt-3">
        <div class="d-flex align-items-center gap-2 mb-3">
            <a href="{% url 'explorer_connection_create' %}" class="btn btn-primary">Add New Connection</a>
            <a href="{% url 'explorer_upload_create' %}" class="btn btn-primary">Upload File</a>
            {% if object_list|length == 0 %}
                <span class="text-secondary d-flex align-items-center fw-bold">
                    <i class="bi-arrow-left me-1"></i>Connect to an existing database, or upload a csv, json, or sqlite file.
                </span>
            {% endif %}
        </div>
        <table class="table table-striped" id="connections_table">
            <thead>
                <tr>
                    <th>Alias</th>
                    <th>Name</th>
                    <th>Engine</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for connection in object_list %}
                <tr>
                    <td>
                        {% if connection.id %}
                            <a href="{% url 'explorer_connection_detail' connection.pk %}">{{ connection.alias }}</a>
                        {% else %}
                            {{ connection.alias }}
                        {% endif %}
                    </td>
                    <td>{{ connection.name }}</td>
                    <td>{{ connection.get_engine_display }}{% if connection.is_upload %} (uploaded){% endif %}</td>
                    <td>
                        <a href="../play/?connection={{ connection.id }}" class="px-2"><i class="bi-arrow-up-right-square small me-1"></i>Query</a>
                        {% if connection.id %}
                            <a href="{% url 'explorer_connection_update' connection.pk %}" class="px-2"><i class="bi-pencil-square"></i></a>
                            <a href="{% url 'explorer_connection_delete' connection.pk %}"><i class="bi-trash"></i></a>
                            <a title="Refresh schema and data. For uploads, will force a refresh from S3." href="{% url 'explorer_connection_refresh' connection.pk %}"><i class="bi-arrow-repeat"></i></a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        function getQueryParam(param) {
            let params = new URLSearchParams(window.location.search);
            return params.get(param);
        }

        let highlight = getQueryParam('highlight');
        if (highlight) {
            let table = document.getElementById('connections_table');
            let rows = table.getElementsByTagName('tr');
            for (let i = 1; i < rows.length; i++) { // Start from 1 to skip the header row
                let aliasCell = rows[i].getElementsByTagName('td')[0];
                if (aliasCell && aliasCell.textContent.includes(highlight)) {
                    rows[i].classList.add('table-active');
                    break;
                }
            }
        }
    });
</script>

{% endblock %}
