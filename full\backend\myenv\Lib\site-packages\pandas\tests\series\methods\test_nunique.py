import numpy as np

from pandas import (
    Categorical,
    Series,
)


def test_nunique():
    # basics.rst doc example
    series = Series(np.random.default_rng(2).standard_normal(500))
    series[20:500] = np.nan
    series[10:20] = 5000
    result = series.nunique()
    assert result == 11


def test_nunique_categorical():
    # GH#18051
    ser = Series(Categorical([]))
    assert ser.nunique() == 0

    ser = Series(Categorical([np.nan]))
    assert ser.nunique() == 0
