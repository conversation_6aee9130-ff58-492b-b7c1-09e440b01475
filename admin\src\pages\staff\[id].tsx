import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Form, Input, Select, Button, message, Card, Spin } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { apiCall, endpoints } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";

const roles = [
  { value: "sales_admin", label: "Nhân viên bán hàng" },
  { value: "sales_manager", label: "Quản lý" },
  { value: "delivery_staff", label: "Nhân viên giao hàng" },
  { value: "warehouse_staff", label: "Nhân viên kho" },
];

interface StaffData {
  id: number;
  email: string;
  first_name: string;
  phone_number?: string;
  profile: {
    role: string;
    telegram_chat_id?: string;
  };
}

const StaffEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  // Fetch staff data
  const { data: staffData, isLoading } = useQuery<StaffData>({
    queryKey: ["staff", id],
    queryFn: () => apiCall("GET", endpoints.staff.detail(Number(id))),
  });

  // Set form values when staff data is loaded
  useEffect(() => {
    if (staffData) {
      form.setFieldsValue({
        firstname: staffData.first_name,
        email: staffData.email,
        phonenumber: staffData.phone_number || "",
        role: staffData.profile.role,
        telegramchatid: staffData.profile.telegram_chat_id || "",
      });
    }
  }, [staffData, form]);

  const onFinish = async (values: any) => {
    try {
      setLoading(true);

      const payload = {
        firstname: values.firstname,
        email: values.email,
        phonenumber: values.phonenumber,
        role: values.role,
        telegramchatid: values.telegramchatid,
      };

      await apiCall("PATCH", endpoints.staff.updateUser(Number(id)), payload);

      message.success("Cập nhật thông tin nhân viên thành công");
      navigate("/staff");
    } catch (error: any) {
      message.error(
        error.response?.data?.message || "Có lỗi xảy ra khi cập nhật thông tin"
      );
    } finally {
      setLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate("/staff")}
            type="text"
          >
            Trở về
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">
            Chỉnh sửa thông tin nhân viên
          </h1>
        </div>
      </div>

      <div className="flex justify-center">
        <Card className="w-full max-w-md">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            className="w-full"
          >
            <Form.Item
              label="Họ tên"
              name="firstname"
              rules={[{ required: true, message: "Vui lòng nhập họ tên" }]}
            >
              <Input placeholder="Nhập họ tên nhân viên" />
            </Form.Item>

            <Form.Item
              label="Email"
              name="email"
              rules={[
                { required: true, message: "Vui lòng nhập email" },
                { type: "email", message: "Email không hợp lệ" },
              ]}
            >
              <Input placeholder="Nhập email" />
            </Form.Item>

            <Form.Item label="Số điện thoại" name="phonenumber">
              <Input placeholder="Nhập số điện thoại" />
            </Form.Item>

            <Form.Item
              label="Vai trò"
              name="role"
              rules={[{ required: true, message: "Vui lòng chọn vai trò" }]}
            >
              <Select options={roles} placeholder="Chọn vai trò" />
            </Form.Item>

            <Form.Item label="Telegram Chat ID" name="telegramchatid">
              <Input placeholder="Nhập Telegram Chat ID" />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                Cập nhật thông tin
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default StaffEdit;