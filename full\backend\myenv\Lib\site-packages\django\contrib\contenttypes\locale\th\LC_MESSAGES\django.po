# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Thai (http://www.transifex.com/django/django/language/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr ""

msgid "python model class name"
msgstr "python model class name"

msgid "content type"
msgstr "content type"

msgid "content types"
msgstr "content types"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "ประเภทของเนื้อหา %(ct_id)s ไม่มีอ็อบเจ็กที่มีรูปแบบที่เกี่ยวข้อง"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "ไม่มีประเภทของเนื้อหา %(ct_id)s วัตถุ %(obj_id)s"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s ไม่มีเมธอด get_absolute_url()"
