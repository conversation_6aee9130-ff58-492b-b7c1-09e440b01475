import React, { useState } from 'react';
import { Button } from 'antd';
import { FileDown } from 'lucide-react';
import { Order } from '@/types/order';
import { useToast } from '@/context/toast-hooks';
import SingleOrderFieldSelectionModal from './SingleOrderFieldSelectionModal';
import { exportSingleOrderToExcel } from '@/utils/singleOrderExportUtils';

interface SingleOrderExportButtonProps {
  order: Order;
  disabled?: boolean;
}

const SingleOrderExportButton: React.FC<SingleOrderExportButtonProps> = ({
  order,
  disabled = false
}) => {
  const { showToast } = useToast();
  const [isFieldSelectionModalVisible, setIsFieldSelectionModalVisible] = useState(false);
  const [selectedExportFields, setSelectedExportFields] = useState<string[]>([
    'id',
    'created_at',
    'customer_name',
    'phone_number',
    'shipping_address',
    'items_detail',
    'payment_method',
    'final_total',
    'status',
    'payment_status',
    'notes'
  ]);
  const [isExporting, setIsExporting] = useState(false);

  const handleExportClick = () => {
    if (!order) {
      showToast("Không có dữ liệu đơn hàng để xuất", "error");
      return;
    }
    setIsFieldSelectionModalVisible(true);
  };

  const handleProceedWithExport = async () => {
    if (selectedExportFields.length === 0) {
      showToast("Vui lòng chọn ít nhất một trường để xuất", "error");
      return;
    }

    try {
      setIsExporting(true);
      await exportSingleOrderToExcel(order, selectedExportFields);
      showToast("Xuất file Excel thành công", "success");
      setIsFieldSelectionModalVisible(false);
    } catch (error) {
      console.error('Lỗi khi xuất file Excel:', error);
      showToast("Có lỗi xảy ra khi xuất file Excel", "error");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <Button
        type="default"
        icon={<FileDown size={16} />}
        onClick={handleExportClick}
        disabled={disabled}
      >
        Export
      </Button>

      <SingleOrderFieldSelectionModal
        visible={isFieldSelectionModalVisible}
        onOk={handleProceedWithExport}
        onCancel={() => setIsFieldSelectionModalVisible(false)}
        selectedFields={selectedExportFields}
        onFieldsChange={setSelectedExportFields}
        isExporting={isExporting}
      />
    </>
  );
};

export default SingleOrderExportButton;
