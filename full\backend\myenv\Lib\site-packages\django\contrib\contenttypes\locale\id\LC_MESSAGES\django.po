# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# M <PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2011-2012
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-02-06 02:33+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Indonesian (http://www.transifex.com/django/django/language/"
"id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "Jen<PERSON>"

msgid "python model class name"
msgstr "nama kelas model python"

msgid "content type"
msgstr "tipe konten"

msgid "content types"
msgstr "tipe konten"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Tipe konten objek %(ct_id)s tidak memiliki model yang terkait"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Jenis isi %(ct_id)s object %(obj_id)s tidak ada"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "objek %(ct_name)s tidak memiliki metode get_absolute_url()"
