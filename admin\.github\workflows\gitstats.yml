name: Update Contributor Stats in gitstats.md

on:
  push:
    branches:
      - main
      - dev
  schedule:
    - cron: '0 3 * * 0' # 03:00 UTC on Sunday
  workflow_dispatch:

jobs:
  update_gitstats_file:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Determine Target Branch
        id: get_branch
        run: |
          target="main"
          if [ "${{ github.event_name }}" == "push" ]; then
            branch_name=$(echo "${{ github.ref }}" | sed 's!refs/heads/!!')
            if [ "$branch_name" == "dev" ]; then
              target="dev"
            fi
          fi
          echo "target_branch=${target}" >> $GITHUB_OUTPUT

      - name: Checkout full repository history
        uses: actions/checkout@v4
        with:
          ref: ${{ steps.get_branch.outputs.target_branch }}
          fetch-depth: 0

      - name: Calculate Contributor Stats
        id: calculate_stats
        run: |
          # Get complete git log with proper formatting
          echo "Fetching git log data..."
          git log --all --format="%aN <%aE>" | sort -u > authors.txt
          echo "Found $(wc -l < authors.txt) unique authors"
          
          # Create an empty results file
          echo "Author,Added,Deleted,Net" > stats.csv
          
          # Process each author separately for improved reliability
          while read author; do
            echo "Processing stats for $author"
            added=$(git log --author="$author" --pretty=tformat: --numstat | awk '{ add += $1; } END { print add }')
            deleted=$(git log --author="$author" --pretty=tformat: --numstat | awk '{ del += $2; } END { print del }')
            
            # Calculate net (handle case where commands might return empty)
            added=${added:-0}
            deleted=${deleted:-0}
            net=$((added - deleted))
            
            # Only add to stats if they have contributions
            if [ "$added" -gt 0 ] || [ "$deleted" -gt 0 ]; then
              # Escape commas in author name and add to CSV
              escaped_author=$(echo "$author" | sed 's/,/\\,/g')
              echo "$escaped_author,$added,$deleted,$net" >> stats.csv
            fi
          done < authors.txt
          
          # Sort by net contribution (largest first)
          sort -t, -k4 -nr stats.csv > sorted_stats.csv
          
          # Save current content if file exists for comparison
          if [ -f "gitstats.md" ]; then
            cp gitstats.md gitstats.md.old
          else
            touch gitstats.md.old
          fi
          
          # Create markdown table
          branch_name="${{ steps.get_branch.outputs.target_branch }}"
          {
            echo "# Git Repository Statistics"
            echo ""
            echo "### Contributor Lines of Code (Branch: $branch_name)"
            echo ""
            echo "| Author | Lines Added | Lines Deleted | Net Lines |"
            echo "|--------|-------------|--------------|-----------|"
            
            # Check if we have any stats
            if [ "$(wc -l < sorted_stats.csv)" -le 1 ]; then
              echo "| *No contributions found* | 0 | 0 | 0 |"
            else
              # Skip header row
              tail -n +2 sorted_stats.csv | while IFS=, read -r author added deleted net; do
                # Remove email part for cleaner display
                display_name=$(echo "$author" | sed 's/ <.*>//')
                # Escape pipe characters
                display_name=$(echo "$display_name" | sed 's/|/\\|/g')
                echo "| $display_name | $added | $deleted | $net |"
              done
            fi
            
            echo ""
            echo "_Last updated: $(date -u +"%Y-%m-%d %H:%M:%S UTC")_"
          } > gitstats.md
          
          # Show the results for debugging
          echo "Generated gitstats.md content:"
          cat gitstats.md

      - name: Check for Changes
        id: check_changes
        run: |
          # Always consider it a change if file didn't exist before
          if [ ! -f "gitstats.md.old" ]; then
            echo "New file created"
            echo "changes_made=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # Compare files ignoring whitespace changes
          if diff -wB gitstats.md gitstats.md.old > /dev/null; then
            echo "No changes detected"
            echo "changes_made=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected"
            echo "changes_made=true" >> $GITHUB_OUTPUT
          fi
          
          # Clean up old file
          rm gitstats.md.old

      - name: Create Pull Request
        id: cpr
        if: steps.check_changes.outputs.changes_made == 'true'
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore(stats): Update contributor LoC stats for ${{ steps.get_branch.outputs.target_branch }}"
          committer: GitHub Actions <<EMAIL>>
          author: ${{ github.actor }} <${{ github.actor_id }}+${{ github.actor }}@users.noreply.github.com>
          branch: update-gitstats-for-${{ steps.get_branch.outputs.target_branch }}
          base: ${{ steps.get_branch.outputs.target_branch }}
          add-paths: gitstats.md
          title: 'chore(stats): Update Git Stats for ${{ steps.get_branch.outputs.target_branch }}'
          body: |
            Automated update of contributor statistics for the `${{ steps.get_branch.outputs.target_branch }}` branch.
            
            _This PR was created automatically by a GitHub Action._

      - name: Merge Pull Request
        if: steps.cpr.outputs.pull-request-number != ''
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PR_NUMBER: ${{ steps.cpr.outputs.pull-request-number }}
        run: |
          echo "Attempting to merge PR #${PR_NUMBER}"
          sleep 5
          gh pr merge "$PR_NUMBER" --merge --delete-branch || echo "Couldn't auto-merge PR"
