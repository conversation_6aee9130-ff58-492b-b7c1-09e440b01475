import { CustomerService } from "./customerService";

export interface OrderDiscountCalculation {
  subtotal: number;
  rankDiscount: number;
  shippingFee: number;
  total: number;
  discountPercentage: number;
  totalDiscount: number;
}

export interface CreateOrderData {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  notes?: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_unit?: string;
  shipping_fee?: number;
  is_showroom: boolean;
  delivery_date?: string;
  items: {
    product: number;
    variant?: number;
    quantity: number;
    total_price: number;
  }[];
}

export class OrderService {
  /**
   * Calculate order totals with rank discount
   */
  static calculateOrderTotals(
    subtotal: number,
    shippingFee: number,
    customerRank: 'normal' | 'silver' | 'gold',
    applyRankDiscount: boolean = true,
    existingDiscount: number = 0
  ): OrderDiscountCalculation {
    const discountPercentage = applyRankDiscount
      ? CustomerService.RANK_DISCOUNTS[customerRank]
      : 0;

    const rankDiscount = applyRankDiscount
      ? CustomerService.calculateRankDiscount(subtotal, customerRank)
      : 0;

    const totalDiscount = existingDiscount + rankDiscount;
    const total = subtotal - totalDiscount + shippingFee;

    return {
      subtotal,
      rankDiscount,
      shippingFee,
      total,
      discountPercentage,
      totalDiscount,
    };
  }

  /**
   * Get order discount breakdown for display
   */
  static getDiscountBreakdown(
    subtotal: number,
    shippingFee: number,
    customerRank: 'normal' | 'silver' | 'gold',
    applyRankDiscount: boolean = true,
    existingDiscount: number = 0
  ) {
    const calculation = this.calculateOrderTotals(subtotal, shippingFee, customerRank, applyRankDiscount, existingDiscount);
    const rankInfo = CustomerService.getRankInfo(customerRank);

    const items = [
      {
        label: 'Tạm tính',
        amount: calculation.subtotal,
        type: 'subtotal' as const,
      }
    ];

    // Add existing discount if any
    if (existingDiscount > 0) {
      items.push({
        label: 'Giảm giá khác',
        amount: -existingDiscount,
        type: 'discount' as const,
      });
    }

    // Add rank discount if applicable
    if (calculation.rankDiscount > 0) {
      items.push({
        label: `Giảm giá ${rankInfo.label} (${calculation.discountPercentage}%)`,
        amount: -calculation.rankDiscount,
        type: 'discount' as const,
      });
    }

    // Add shipping fee
    items.push({
      label: 'Phí vận chuyển',
      amount: calculation.shippingFee,
      type: 'shipping' as const,
    });

    return {
      items,
      total: calculation.total,
      savings: calculation.rankDiscount,
      totalDiscount: calculation.totalDiscount,
    };
  }
}
