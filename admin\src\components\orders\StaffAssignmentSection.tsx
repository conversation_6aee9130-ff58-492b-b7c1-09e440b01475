import { Order } from "../../types/order";
import { Staff } from "../../types/staff";

interface StaffAssignmentSectionProps {
  order: Order;
  onSelectSalesAdmin?: () => void;
  onSelectDeliveryStaff?: () => void;
}

export function StaffAssignmentSection({
  order,
  onSelectSalesAdmin,
  onSelectDeliveryStaff,
}: StaffAssignmentSectionProps) {
  const renderStaffInfo = (staff: Staff | undefined) => {
    if (!staff) return null;
    return (
      <div>
        <div className="font-medium">{staff.username}</div>
        <div className="text-sm text-gray-500 truncate">{staff.email}</div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-3">Phân công nhân viên</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Sales Admin</label>
          <div
            onClick={onSelectSalesAdmin}
            className={`w-full border rounded p-2 ${
              onSelectSalesAdmin ? "cursor-pointer hover:bg-gray-50" : ""
            }`}
          >
            {order.sales_admin ? (
              renderStaffInfo(order.sales_admin)
            ) : (
              <span className="text-gray-500">Chọn nhân viên sale</span>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Nhân viên giao hàng
          </label>
          <div
            onClick={onSelectDeliveryStaff}
            className={`w-full border rounded p-2 ${
              onSelectDeliveryStaff ? "cursor-pointer hover:bg-gray-50" : ""
            }`}
          >
            {order.delivery_staff ? (
              renderStaffInfo(order.delivery_staff)
            ) : (
              <span className="text-gray-500">Chọn nhân viên giao hàng</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
