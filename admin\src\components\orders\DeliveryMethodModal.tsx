import React, { useState } from "react";
import { Mo<PERSON>, Select, Button } from "antd";
import { Staff } from "../../types/staff";
import { SHIPPING_UNIT_OPTIONS } from "../../types/order";

interface DeliveryMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (params: {
    delivery_staff_id?: number | null;
    shipping_unit?: string;
  }) => void;
  staffList: Staff[];
  type: "staff" | "shipping_unit";
  title?: string;
}

export function DeliveryMethodModal({
  isOpen,
  onClose,
  onConfirm,
  staffList,
  type,
  title,
}: DeliveryMethodModalProps) {
  const [selectedStaffId, setSelectedStaffId] = useState<number | null>(null);
  const [selectedShippingUnit, setSelectedShippingUnit] = useState<
    string | null
  >(null);

  // Filter delivery staff
  const deliveryStaffList = staffList.filter(
    (staff) => staff.role === "delivery_staff"
  );

  // Filter shipping unit options (exclude motorbike)
  const filteredShippingUnitOptions = SHIPPING_UNIT_OPTIONS.filter(
    (option) => option.value !== "motorbike"
  );

  const handleConfirm = () => {
    if (type === "staff" && selectedStaffId) {
      onConfirm({
        delivery_staff_id: selectedStaffId,
        shipping_unit: "motorbike",
      });
      onClose();
    } else if (type === "shipping_unit" && selectedShippingUnit) {
      onConfirm({
        shipping_unit: selectedShippingUnit,
        delivery_staff_id: null,
      });
      onClose();
    }
    // Không đóng modal nếu không có giá trị được chọn
  };

  const handleCancel = () => {
    setSelectedStaffId(null);
    setSelectedShippingUnit(null);
    onClose();
  };

  return (
    <Modal
      title={
        title ||
        (type === "staff"
          ? "Chọn nhân viên giao hàng"
          : "Chọn đơn vị vận chuyển")
      }
      open={isOpen}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
          disabled={
            (type === "staff" && !selectedStaffId) ||
            (type === "shipping_unit" && !selectedShippingUnit)
          }
        >
          Xác nhận
        </Button>,
      ]}
    >
      <div className="mb-2">
        {type === "staff" ? (
          <p className="text-red-500 mb-2">
            * Bắt buộc chọn nhân viên giao hàng
          </p>
        ) : (
          <p className="text-red-500 mb-2">* Bắt buộc chọn đơn vị vận chuyển</p>
        )}
      </div>
      {type === "staff" ? (
        <Select
          placeholder="Chọn nhân viên giao hàng"
          style={{ width: "100%" }}
          value={selectedStaffId}
          onChange={setSelectedStaffId}
          options={deliveryStaffList.map((staff) => ({
            value: staff.id,
            label: `${staff.first_name} ${staff.last_name}`,
          }))}
          status={!selectedStaffId && isOpen ? "error" : undefined}
          required
        />
      ) : (
        <Select
          placeholder="Chọn đơn vị vận chuyển"
          style={{ width: "100%" }}
          value={selectedShippingUnit}
          onChange={setSelectedShippingUnit}
          options={filteredShippingUnitOptions.map((option) => ({
            value: option.value,
            label: option.label,
          }))}
          status={!selectedShippingUnit && isOpen ? "error" : undefined}
          required
        />
      )}
    </Modal>
  );
}
