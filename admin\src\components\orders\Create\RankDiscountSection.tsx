import React from 'react';
import { Card, Checkbox, Typography, Space, Divider } from 'antd';
import { CustomerRankBadge } from '@/components/customers/CustomerRankBadge';
import { CustomerService } from '@/services/customerService';
import { OrderService } from '@/services/orderService';

const { Title, Text } = Typography;

interface RankDiscountSectionProps {
  customerRank: 'normal' | 'silver' | 'gold';
  subtotal: number;
  shippingFee: number;
  applyRankDiscount: boolean;
  onApplyRankDiscountChange: (apply: boolean) => void;
  totalSpent?: number;
  existingDiscount?: number;
}

export const RankDiscountSection: React.FC<RankDiscountSectionProps> = ({
  customerRank,
  subtotal,
  shippingFee,
  applyRankDiscount,
  onApplyRankDiscountChange,
  totalSpent,
  existingDiscount = 0,
}) => {
  const rankInfo = CustomerService.getRankInfo(customerRank);
  const discountBreakdown = OrderService.getDiscountBreakdown(
    subtotal,
    shippingFee,
    customerRank,
    applyRankDiscount,
    existingDiscount
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Don't show if customer has no discount
  if (customerRank === 'normal') {
    return null;
  }

  return (
    <Card className="mb-4">
      <div className="flex justify-between items-start mb-4">
        <div>
          <Title level={5} className="mb-2">Giảm giá theo hạng khách hàng</Title>
          <Space>
            <CustomerRankBadge rank={customerRank} totalSpent={totalSpent} />
            <Text type="secondary">
              Khách hàng được giảm {rankInfo.discount}% cho đơn hàng này
            </Text>
          </Space>
        </div>
        <Checkbox
          checked={applyRankDiscount}
          onChange={(e) => onApplyRankDiscountChange(e.target.checked)}
        >
          Áp dụng
        </Checkbox>
      </div>

      <Divider className="my-3" />

      <div className="space-y-2">
        {discountBreakdown.items.map((item, index) => (
          <div key={index} className="flex justify-between items-center">
            <Text className={item.type === 'discount' ? 'text-green-600' : ''}>
              {item.label}
            </Text>
            <Text
              className={`font-medium ${
                item.type === 'discount' ? 'text-green-600' : ''
              }`}
            >
              {item.amount >= 0 ? '+' : ''}{formatCurrency(item.amount)}
            </Text>
          </div>
        ))}

        <Divider className="my-2" />

        <div className="flex justify-between items-center">
          <Text strong className="text-lg">Tổng cộng</Text>
          <Text strong className="text-lg text-blue-600">
            {formatCurrency(discountBreakdown.total)}
          </Text>
        </div>
      </div>
    </Card>
  );
};

export default RankDiscountSection;
