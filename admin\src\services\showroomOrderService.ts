import { api, endpoints } from "../lib/api";
import { ShowroomOrderType } from "../components/orders/Create/ShowroomOrderSection";
import { OrderItem as BaseOrderItem } from "../types/order";

interface OrderItem extends BaseOrderItem {
  unit?: string;
}

interface OrderForm {
  user?: number;
  phone_number: string;
  email: string;
  shipping_address: string;
  ward: string;
  district: string;
  city: string;
  notes?: string;
  payment_method: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  shipping_unit?: string;
  shipping_fee?: number;
  discount?: number;
  is_showroom: boolean;
  delivery_date?: string;
}

interface TransportInfo {
  name: string;
  address: string;
  phone: string;
  operatingHours: string;
}

interface CreateOrderParams {
  orderData: OrderForm;
  items: OrderItem[];
  useTransportation: boolean;
  transportInfo: TransportInfo;
  shippingFee: number;
  notes: string;
}

export class ShowroomOrderService {
  /**
   * Create a single order with specified showroom settings
   */
  static async createSingleOrder(
    params: CreateOrderParams,
    isShowroomOrder: boolean = false
  ) {
    const { orderData, items, useTransportation, transportInfo, shippingFee, notes } = params;

    const orderPayload = {
      ...orderData,
      notes,
      is_showroom: isShowroomOrder,
      shipping_fee: shippingFee,
      items: items.map((item) => ({
        product: item.product,
        variant: item.variant,
        quantity: item.quantity,
        total_price: isShowroomOrder ? 0 : item.total_price,
      })),
    };

    // Add shipping_unit if using transportation
    if (useTransportation) {
      orderPayload.shipping_unit = "transport_partner";

      // If transport is enabled, append transport info to the customer's address
      if (transportInfo.name && transportInfo.address) {
        const transportAddressText = `\nThông tin chành xe: ${
          transportInfo.name
        }, ${transportInfo.address}${
          transportInfo.phone ? `, SĐT: ${transportInfo.phone}` : ""
        }${
          transportInfo.operatingHours
            ? `, Giờ hoạt động: ${transportInfo.operatingHours}`
            : ""
        }`;

        // Append transport info to the shipping address
        orderPayload.shipping_address =
          orderPayload.shipping_address + transportAddressText;
      }
    }

    return await api.post(endpoints.orders.create, orderPayload);
  }

  /**
   * Create combined orders (warehouse to showroom + showroom to customer)
   */
  static async createCombinedOrders(params: CreateOrderParams) {
    // First order: Warehouse to Showroom (is_showroom = true, price = 0)
    const warehouseToShowroomOrder = await this.createSingleOrder(params, true);

    // Second order: Showroom to Customer (is_showroom = false, normal price)
    const showroomToCustomerOrder = await this.createSingleOrder(params, false);

    return {
      warehouseOrder: warehouseToShowroomOrder,
      customerOrder: showroomToCustomerOrder,
    };
  }

  /**
   * Handle order creation based on showroom order type
   */
  static async handleOrderCreation(
    showroomOrderType: ShowroomOrderType | null,
    params: CreateOrderParams
  ) {
    if (showroomOrderType === 'combined_orders') {
      // Option 3: Create 2 orders
      const result = await this.createCombinedOrders(params);
      return {
        type: 'combined' as const,
        orders: result,
        message: "Tạo 2 đơn hàng thành công",
        redirectOrderId: result.customerOrder.data.id,
      };
    } else {
      // Default, Option 1 or 2: Create single order
      const isShowroom = showroomOrderType === 'warehouse_to_showroom';
      const response = await this.createSingleOrder(params, isShowroom);

      return {
        type: 'single' as const,
        order: response,
        message: "Tạo đơn hàng thành công",
        redirectOrderId: response.data.id,
      };
    }
  }

  /**
   * Handle showroom order type selection and update items accordingly
   */
  static handleShowroomOrderTypeSelection(
    type: ShowroomOrderType,
    _items: OrderItem[], // Prefix with underscore to indicate intentionally unused
    setItems: (items: OrderItem[] | ((prev: OrderItem[]) => OrderItem[])) => void,
    _showToast: (message: string, type: 'success' | 'error' | 'info') => void // Prefix with underscore to indicate intentionally unused
  ) {
    switch (type) {
      case 'normal_order':
        // Default: Normal order, restore normal pricing if coming from warehouse option
        setItems((prevItems) =>
          prevItems.map((item) => ({
            ...item,
            total_price: Math.round(item.price * item.quantity),
          }))
        );
        break;
      case 'warehouse_to_showroom':
        // Option 1: Set items to 0 price for showroom order
        setItems((prevItems) =>
          prevItems.map((item) => ({
            ...item,
            total_price: 0,
          }))
        );
        break;
      case 'normal_revenue':
        // Option 2: Restore normal pricing for revenue calculation
        setItems((prevItems) =>
          prevItems.map((item) => ({
            ...item,
            total_price: Math.round(item.price * item.quantity),
          }))
        );
        break;
      case 'combined_orders':
        // Option 3: Restore normal pricing for customer order display
        setItems((prevItems) =>
          prevItems.map((item) => ({
            ...item,
            total_price: Math.round(item.price * item.quantity),
          }))
        );
        break;
    }
  }

  /**
   * Handle showroom toggle (for backward compatibility)
   */
  static handleShowroomToggle(
    value: boolean,
    _items: OrderItem[], // Prefix with underscore to indicate intentionally unused
    setItems: (items: OrderItem[] | ((prev: OrderItem[]) => OrderItem[])) => void,
    showToast: (message: string, type: 'success' | 'error' | 'info') => void
  ) {
    if (value) {
      setItems((prevItems) =>
        prevItems.map((item) => ({
          ...item,
          total_price: 0,
        }))
      );
    } else {
      setItems([]);
      showToast("Vui lòng chọn lại sản phẩm", "info");
    }
  }
}

export type { CreateOrderParams, OrderForm, TransportInfo };
