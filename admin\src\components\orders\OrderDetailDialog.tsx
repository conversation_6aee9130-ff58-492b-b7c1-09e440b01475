import { Order } from "../../types/order";
import { formatCurrency } from "../../lib/utils";

interface OrderDetailDialogProps {
  order: Order;
  onClose: () => void;
}

export function OrderDetailDialog({ order, onClose }: OrderDetailDialogProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-background p-6 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Đơn hàng #{order.id}</h2>
          <button onClick={onClose} className="text-muted-foreground hover:text-foreground">
            ✕
          </button>
        </div>

        {/* Customer Information */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Thông tin khách hàng</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Email</p>
              <p>{order.email}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Số điện thoại</p>
              <p>{order.phone_number}</p>
            </div>
            <div className="col-span-2">
              <p className="text-sm text-muted-foreground">Địa chỉ giao hàng</p>
              <p>{order.shipping_address}</p>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Chi tiết đơn hàng</h3>
          <div className="border rounded-lg">
            <table className="min-w-full divide-y">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase">
                    Mã hàng
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase">
                    Sản phẩm
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase">
                    Số lượng
                  </th>
                  {!order.is_showroom && (
                    <>
                      <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase">
                        Đơn giá
                      </th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase">
                        Thành tiền
                      </th>
                    </>
                  )}
                  {order.is_showroom && (
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground uppercase">
                      Khối lượng (kg)
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="divide-y">
                {order.items.map((item) => (
                  <tr key={item.id}>
                    <td className="px-4 py-2">
                      <div className="text-sm text-muted-foreground">
                        {item.product_code || 'N/A'}
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      <div>
                        <div className="font-medium">{item.product_name}</div>
                        {item.variant_name && (
                          <div className="text-sm text-muted-foreground">
                            {item.variant_name}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-2">{item.quantity}</td>
                    {!order.is_showroom && (
                      <>
                        <td className="px-4 py-2">{formatCurrency(item.price)}</td>
                        <td className="px-4 py-2">{formatCurrency(item.total_price)}</td>
                      </>
                    )}
                    {order.is_showroom && (
                      <td className="px-4 py-2">
                        {item.product_weight ? `${item.product_weight} kg` : 'N/A'}
                      </td>
                    )}
                  </tr>
                ))}
                {!order.is_showroom && (
                  <tr className="bg-muted/50">
                    <td colSpan={4} className="px-4 py-2 text-right font-medium">
                      Tổng cộng:
                    </td>
                    <td className="px-4 py-2 font-medium">
                      {formatCurrency(order.total_price)}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Order Status */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Trạng thái đơn hàng</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Trạng thái hiện tại</p>
              <p className="font-medium">{order.status_display}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Ngày đặt hàng</p>
              <p>{new Date(order.created_at).toLocaleDateString("vi-VN")}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Cập nhật lần cuối</p>
              <p>{new Date(order.updated_at).toLocaleDateString("vi-VN")}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
