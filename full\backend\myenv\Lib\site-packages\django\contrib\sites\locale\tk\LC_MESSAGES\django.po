# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2020-07-06 11:50+0000\n"
"Last-Translator: Resulkary <<EMAIL>>\n"
"Language-Team: Turkmen (http://www.transifex.com/django/django/language/"
"tk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tk\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Sahypalar"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Domen adynda bo<PERSON><PERSON>lar ý<PERSON>-da goýmalar bolup bilmez."

msgid "domain name"
msgstr "domen ady"

msgid "display name"
msgstr "görkezilýän ady"

msgid "site"
msgstr "sahypa"

msgid "sites"
msgstr "sahypalar"
