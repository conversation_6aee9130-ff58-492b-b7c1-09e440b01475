name: Deploy Backend to Cloud Run

on:
  push:
    branches:
      - main
    paths:
      - 'backend/**'
      - '.github/workflows/deploy-backend.yml'

env:
  PROJECT_ID: ttmi-451216
  REGION: asia-southeast1
  SERVICE_NAME: nguyenlieu3t

jobs:
  deploy:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Google Auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: Authorize Docker push
        run: gcloud auth configure-docker

      - name: Build and Push Container
        run: |-
          docker build -t gcr.io/$PROJECT_ID/$SERVICE_NAME:${{ github.sha }} ./backend
          docker push gcr.io/$PROJECT_ID/$SERVICE_NAME:${{ github.sha }}

      - name: Deploy to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE_NAME }}
          region: ${{ env.REGION }}
          image: gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.sha }}
          env_vars: |
            REDIS_URL=${{ secrets.REDIS_URL }}
            DATABASE_URL=${{ secrets.DATABASE_URL }}
            STORE_CORS=${{ secrets.STORE_CORS }}
            ADMIN_CORS=${{ secrets.ADMIN_CORS }}
            AUTH_CORS=${{ secrets.AUTH_CORS }}
            JWT_SECRET=${{ secrets.JWT_SECRET }}
            COOKIE_SECRET=${{ secrets.COOKIE_SECRET }}

      - name: Show Output
        run: echo ${{ steps.deploy.outputs.url }}
