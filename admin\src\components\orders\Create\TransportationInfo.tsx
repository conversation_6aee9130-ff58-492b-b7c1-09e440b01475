import React, { useEffect, useState, useRef } from "react";
import { Card, Checkbox, Form, Input, Typography } from "antd";

const { Title } = Typography;

interface TransportationInfoProps {
  notes: string;
  onTransportationChange?: (useTransportation: boolean) => void;
  onTransportInfoChange?: (transportInfo: {
    name: string;
    address: string;
    phone: string;
    operatingHours: string;
  }) => void;
  transportInfo?: {
    name: string;
    address: string;
    phone: string;
    operatingHours: string;
  };
  onShippingFeeChange?: (fee: number) => void;
  shippingFee?: number;
}

const TransportationInfo: React.FC<TransportationInfoProps> = ({
  notes,
  onTransportationChange,
  onTransportInfoChange,
  transportInfo: externalTransportInfo,
  onShippingFeeChange,
  shippingFee = 0,
}) => {
  const [useTransportation, setUseTransportation] = useState(false);
  const [transportName, setTransportName] = useState("");
  const [transportAddress, setTransportAddress] = useState("");
  const [transportPhone, setTransportPhone] = useState("");
  const [transportOperatingHours, setTransportOperatingHours] = useState("");
  const [transportShippingFee, setTransportShippingFee] = useState(shippingFee);

  // Use refs to track if we're updating from external props to avoid loops
  const isUpdatingFromExternal = useRef(false);
  const prevExternalInfo = useRef({
    name: "",
    address: "",
    phone: "",
    operatingHours: "",
  });

  // Initialize from notes on first render only
  useEffect(() => {
    const transportRegex =
      /Thông tin chành xe: tên (.*?), địa chỉ (.*?)(?:,|$)/;
    const match = notes.match(transportRegex);

    if (match) {
      setUseTransportation(true);
      setTransportName(match[1] || "");
      setTransportAddress(match[2] || "");

      if (onTransportationChange) {
        onTransportationChange(true);
      }

      if (onTransportInfoChange) {
        onTransportInfoChange({
          name: match[1] || "",
          address: match[2] || "",
          phone: "",
          operatingHours: "",
        });
      }
    }
  }, []);

  // Use external transport info if provided and different from current
  useEffect(() => {
    if (
      externalTransportInfo &&
      (externalTransportInfo.name !== prevExternalInfo.current.name ||
        externalTransportInfo.address !== prevExternalInfo.current.address ||
        externalTransportInfo.phone !== prevExternalInfo.current.phone ||
        externalTransportInfo.operatingHours !==
          prevExternalInfo.current.operatingHours)
    ) {
      // Only update if the values are actually different from current state
      if (
        transportName !== externalTransportInfo.name ||
        transportAddress !== externalTransportInfo.address ||
        transportPhone !== externalTransportInfo.phone ||
        transportOperatingHours !== externalTransportInfo.operatingHours
      ) {
        isUpdatingFromExternal.current = true;
        setTransportName(externalTransportInfo.name);
        setTransportAddress(externalTransportInfo.address);
        setTransportPhone(externalTransportInfo.phone);
        setTransportOperatingHours(externalTransportInfo.operatingHours);
        prevExternalInfo.current = { ...externalTransportInfo };
      }
    }
  }, [externalTransportInfo]);

  // Update parent when transportation info changes, but only if not triggered by external update
  useEffect(() => {
    // Skip the first render and when updating from external props
    if (isUpdatingFromExternal.current) {
      isUpdatingFromExternal.current = false;
      return;
    }

    if (!useTransportation) {
      if (onTransportInfoChange) {
        onTransportInfoChange({
          name: "",
          address: "",
          phone: "",
          operatingHours: "",
        });
      }
      return;
    }

    if (!transportName && !transportAddress) return;

    if (onTransportInfoChange) {
      onTransportInfoChange({
        name: transportName,
        address: transportAddress,
        phone: transportPhone,
        operatingHours: transportOperatingHours,
      });
    }
  }, [
    useTransportation,
    transportName,
    transportAddress,
    transportPhone,
    transportOperatingHours,
  ]);

  // Handle shipping fee changes
  useEffect(() => {
    if (onShippingFeeChange) {
      onShippingFeeChange(transportShippingFee);
    }
  }, [transportShippingFee, onShippingFeeChange]);

  return (
    <Card>
      <Title level={4}>Thông tin vận chuyển</Title>
      <Form layout="vertical">
        <Form.Item>
          <Checkbox
            checked={useTransportation}
            onChange={(e) => {
              const isChecked = e.target.checked;
              setUseTransportation(isChecked);
              if (onTransportationChange) {
                onTransportationChange(isChecked);
              }
              if (!isChecked) {
                setTransportName("");
                setTransportAddress("");
                setTransportPhone("");
                setTransportOperatingHours("");
              }
            }}
          >
            Vận chuyển bằng chành xe
          </Checkbox>
        </Form.Item>

        {useTransportation && (
          <>
            <Form.Item label="Tên chành xe" required={useTransportation}>
              <Input
                placeholder="Nhập tên chành xe"
                value={transportName}
                onChange={(e) => setTransportName(e.target.value)}
              />
            </Form.Item>
            <Form.Item label="Địa chỉ chành xe" required={useTransportation}>
              <Input
                placeholder="Nhập địa chỉ chành xe"
                value={transportAddress}
                onChange={(e) => setTransportAddress(e.target.value)}
              />
            </Form.Item>
            <Form.Item label="Số điện thoại">
              <Input
                placeholder="Nhập số điện thoại chành xe"
                value={transportPhone}
                onChange={(e) => setTransportPhone(e.target.value)}
              />
            </Form.Item>
            <Form.Item label="Thời gian hoạt động">
              <Input
                placeholder="Nhập thời gian hoạt động (vd: 8h-17h)"
                value={transportOperatingHours}
                onChange={(e) => setTransportOperatingHours(e.target.value)}
              />
            </Form.Item>
          </>
        )}

        <Form.Item label="Phí vận chuyển">
          <Input
            type="text"
            style={{ width: "100%" }}
            placeholder="Nhập phí vận chuyển (mặc định: 0)"
            value={transportShippingFee.toLocaleString()}
            onChange={(e) => {
              const value = e.target.value.replace(/[^\d]/g, "");
              setTransportShippingFee(parseInt(value) || 0);
            }}
            addonAfter="VND"
          />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TransportationInfo;
