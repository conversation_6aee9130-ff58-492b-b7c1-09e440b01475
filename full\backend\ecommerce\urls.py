"""
URL configuration for ecommerce project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve
from rest_framework_simplejwt.views import TokenRefreshView
from store.views.auth_views import EmailTokenObtainPairView
from store.admin_site import store_admin_site
import json
import os
from django.http import HttpResponse, JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from pathlib import Path

from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions

schema_view = get_schema_view(
    openapi.Info(
        title="eCommerce API",
        default_version='v1',
        description="API documentation for eCommerce platform",
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

def index(request):
    try:
        version_file = Path("/app/version.json")
        if version_file.exists():
            with open(version_file) as f:
                version_info = json.load(f)
        else:
            # Fallback if version file doesn't exist
            version_info = {
                "version": "1.0",
                "git_hash": "unknown",
                "last_commit": "unknown"
            }

        version_info["status"] = "API is running"
        return JsonResponse(version_info)
    except Exception as e:
        return JsonResponse({
            "status": "API is running",
            "version": "1.0",
            "error": str(e)
        })

def health(request):
    return HttpResponse("OK")

urlpatterns = [
    path('', index),
    path('health/', health),
    path('admin/', store_admin_site.urls),
    path('api/', include('store.urls')),  # Changed from 'store/' to 'api/'

    # JWT Authentication endpoints
    path('api/token/', EmailTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # SQL Explorer (restricted to staff/superuser)
    path('explorer/', include('explorer.urls')),

    # API Documentation
    path('swagger<format>/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    # Serve media files in production
    path('media/<path:path>', serve, {'document_root': settings.MEDIA_ROOT}),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
