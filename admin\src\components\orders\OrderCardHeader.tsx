import { CalendarIcon } from "@heroicons/react/24/outline";
import { Order } from "../../types/order";
import { statusColors } from "./orderUtils";

interface OrderCardHeaderProps {
  order: Order;
  onUpdateStatus: (orderId: number, status: Order["status"]) => void;
  isDesktop?: boolean;
}

export function OrderCardHeader({ order, onUpdateStatus, isDesktop }: OrderCardHeaderProps) {
  return (
    <div className={`flex items-start ${isDesktop ? "gap-4" : "p-4 justify-between"}`}>
      <div className="flex-1">
        <div className="font-medium text-lg">#{order.id}</div>
        <div className="text-muted-foreground flex items-center gap-1.5 text-sm mt-1">
          <CalendarIcon className="h-4 w-4" />
          {new Date(order.created_at).toLocaleDateString("vi-VN")}
        </div>
      </div>
      {!isDesktop && (
        <select
          value={order.status}
          onChange={(e) => onUpdateStatus(order.id, e.target.value as Order["status"])}
          className={`border-0 rounded-lg px-3 py-2 text-sm bg-muted min-w-[130px] font-medium ${statusColors[order.status]}`}
        >
          <option value="pending">Chờ xử lý</option>
          <option value="processing">Đang xử lý</option>
          <option value="shipped">Đã gửi hàng</option>
          <option value="delivered">Đã giao hàng</option>
          <option value="cancelled">Đã hủy</option>
        </select>
      )}
    </div>
  );
}
