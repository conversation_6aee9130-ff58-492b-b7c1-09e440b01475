import { useState, useEffect } from "react";
import { Tabs } from "antd";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { ProductRevenueTable } from "@/components/dashboard/ProductRevenueTable";
import { StaffFilter } from "@/components/orders/StaffFilter";
import { apiCall, endpoints } from "@/lib/api";
import { Staff } from "@/types/staff";
import { useAuth } from "@/context/auth-hooks";

export default function ProductRevenueReport() {
  const { user } = useAuth();
  const [urlSearchParams, setUrlSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<"actual" | "all">("actual");

  // Initialize states from URL params
  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<Staff | null>(
    () => {
      if (user?.role === "sales_admin") {
        return { id: user.id } as Staff;
      }
      const id = urlSearchParams.get("sales_admin");
      return id ? ({ id: Number(id) } as Staff) : null;
    }
  );
  const [selectedSalesAdmins, setSelectedSalesAdmins] = useState<number[]>([]);

  // Fetch staff list
  const { data: staffList = [] } = useQuery<Staff[]>({
    queryKey: ["staff"],
    queryFn: async () => {
      const response = await apiCall<{ results: Staff[] }>(
        "GET",
        endpoints.staff.list + "?no_page=true"
      );

      return response.results;
    },
  });

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams(urlSearchParams);

    if (selectedSalesAdmins.length > 0) {
      params.set("sales_admin", selectedSalesAdmins.join(','));
    } else if (selectedSalesAdmin) {
      params.set("sales_admin", String(selectedSalesAdmin.id));
    } else {
      params.delete("sales_admin");
    }

    setUrlSearchParams(params, { replace: true });
  }, [selectedSalesAdmin, selectedSalesAdmins, setUrlSearchParams]);

  const handleSalesAdminChange = (staffId: number | null) => {
    setSelectedSalesAdmin(
      staffId ? staffList.find((s) => s.id === staffId) || null : null
    );
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold tracking-tight">
            Phân tích doanh thu sản phẩm
          </h1>
        </div>

        <StaffFilter
          userRole={user?.role || ""}
          salesAdmin={selectedSalesAdmin}
          selectedSalesAdmins={selectedSalesAdmins}
          onSalesAdminChange={handleSalesAdminChange}
          onSalesAdminsChange={setSelectedSalesAdmins}
          onDeliveryStaffChange={() => {}}
          staffList={staffList}
          enableMultiSelect={true}
        />

        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as "actual" | "all")}
          items={[
            {
              key: "actual",
              label: "Doanh thu thực tế",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị doanh thu thực tế
                </div>
              ),
            },
            {
              key: "all",
              label: "Doanh thu tổng",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị tổng doanh thu (bao gồm cả đơn hàng đã hủy và đang xử
                  lí)
                </div>
              ),
            },
          ]}
        />
      </div>

      <div className="rounded-md bg-card">
        <div className="p-6 relative">
          {activeTab === "all" && (
            <div className="absolute right-6 top-6 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full z-10">
              Doanh thu tổng
            </div>
          )}
          <ProductRevenueTable revenueType={activeTab} />
        </div>
      </div>
    </div>
  );
}
