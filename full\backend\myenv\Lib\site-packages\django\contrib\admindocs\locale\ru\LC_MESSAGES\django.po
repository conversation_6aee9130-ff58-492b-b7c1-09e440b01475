# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011
# Panasoft, 2021
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2012-2015
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-16 22:51+0000\n"
"Last-Translator: Panasoft\n"
"Language-Team: Russian (http://www.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Administrative Documentation"
msgstr "Документация для администраторов"

msgid "Home"
msgstr "Начало"

msgid "Documentation"
msgstr "Документация"

msgid "Bookmarklets"
msgstr "Закладки"

msgid "Documentation bookmarklets"
msgstr "Закладки документации"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Чтобы установить букмарклет, перетащите ссылку на панель закладок браузера "
"или откройте кликом по правой кнопке мышки контекстное меню и добавьте "
"ссылку в закладки. Теперь у вас есть доступ к букмарклету с любой страницы "
"сайта."

msgid "Documentation for this page"
msgstr "Документация для данной страницы"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Перенаправляет вас с любой страницы к документации для view, который "
"генерирует эту страницу."

msgid "Tags"
msgstr "Теги"

msgid "List of all the template tags and their functions."
msgstr "Список всех template tag-ов и их функций."

msgid "Filters"
msgstr "Фильтры"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Фильтры - это действия, которые могут быть выполнены над переменными в "
"шаблоне, чтобы изменить их отображение."

msgid "Models"
msgstr "Модели"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Модели - это описания всех объектов, существующих в системе и связанные с "
"ними поля. Каждая модель имеет список полей, которые могут быть использованы "
"в качестве переменных в шаблонах"

msgid "Views"
msgstr "View"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Каждая страница сайта создаётся с помощью view. View определяет, какой "
"шаблон использовать, чтобы создать страницу и какие объекты будут доступны в "
"этом шаблоне."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Инструменты для вашего браузера для быстрого доступа к функциональности "
"административного раздела сайта (букмарклеты)."

msgid "Please install docutils"
msgstr "Пожалуйста, установите docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Для системы административной документации требуется библиотека Python <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Пожалуйста, попросите ваших администраторов установить <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Модель: %(name)s"

msgid "Fields"
msgstr "Поля"

msgid "Field"
msgstr "Поле"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Описание"

msgid "Methods with arguments"
msgstr "Методы с аргументами"

msgid "Method"
msgstr "Метод"

msgid "Arguments"
msgstr "Аргументы"

msgid "Back to Model documentation"
msgstr "Назад к документации моделей"

msgid "Model documentation"
msgstr "Документация моделей"

msgid "Model groups"
msgstr "Группы моделей"

msgid "Templates"
msgstr "Шаблоны"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Шаблон: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Путь к местонахождению шаблона <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(не существует)"

msgid "Back to Documentation"
msgstr "Назад к документации"

msgid "Template filters"
msgstr "Фильтры в шаблонах"

msgid "Template filter documentation"
msgstr "Документация о фильтрах в шаблонах"

msgid "Built-in filters"
msgstr "Встроенные фильтры"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Чтобы использовать эти фильтры, поместите <code>%(code)s</code> в ваш шаблон "
"выше строки с использованием фильтра."

msgid "Template tags"
msgstr "Теги шаблонов"

msgid "Template tag documentation"
msgstr "Документация по тегам шаблонов"

msgid "Built-in tags"
msgstr "Встроенные теги"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Чтобы использовать эти теги, поместите <code>%(code)s</code> в ваш шаблон "
"выше строки с использованием тега."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Шаблоны:"

msgid "Back to View documentation"
msgstr "Назад к документации о представлений"

msgid "View documentation"
msgstr "Документация о view"

msgid "Jump to namespace"
msgstr "Перейти к пространству имён"

msgid "Empty namespace"
msgstr "Глобальное пространство имён"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "View в пространстве имён %(name)s"

msgid "Views by empty namespace"
msgstr "View в глобальном пространстве имён"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"View-функция: <code>%(full_name)s</code>. Имя url-шаблона: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "тег:"

msgid "filter:"
msgstr "фильтр:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Приложение «%(app_label)r» не найдено"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Модель %(model_name)r не найдена в приложении %(app_label)r"

msgid "model:"
msgstr "модель:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "зависимый `%(app_label)s.%(data_type)s` объект"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "связанные объекты `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "все %s"

#, python-format
msgid "number of %s"
msgstr "количество %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не похож на объект urlpattern"
