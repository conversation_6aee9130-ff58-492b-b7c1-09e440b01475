# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-24 13:46+0200\n"
"PO-Revision-Date: 2017-09-24 14:24+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Telugu (http://www.transifex.com/django/django/language/te/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: te\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Personal info"
msgstr "వ్యక్తిగత సమాచారం  "

msgid "Permissions"
msgstr "అనుమతులు"

msgid "Important dates"
msgstr "ముఖ్యమైన తేదీలు"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

msgid "Password changed successfully."
msgstr "సంకేతపదాన్ని విజయవంతంగా మార్చారు."

#, python-format
msgid "Change password: %s"
msgstr "సంకేతపదాన్ని మార్చుకోండి: %s"

msgid "Authentication and Authorization"
msgstr ""

msgid "password"
msgstr "సంకేతపదం"

msgid "last login"
msgstr "చివరి ప్రవేశం"

msgid "No password set."
msgstr ""

msgid "Invalid password format or unknown hashing algorithm."
msgstr ""

msgid "The two password fields didn't match."
msgstr "ఈ రెండు అనుమతి పదాలు అసమానమైనంగ ఉన్నాయి"

msgid "Password"
msgstr "సంకేతపదం"

msgid "Password confirmation"
msgstr "సంకేపదపు నిర్ధారణ"

msgid "Enter the same password as before, for verification."
msgstr ""

msgid ""
"Raw passwords are not stored, so there is no way to see this user's "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""

msgid "This account is inactive."
msgstr "ఈ ఖాతా అచేతనమైనది"

msgid "Email"
msgstr ""

msgid "New password"
msgstr "కొత్త సంకేతపదం"

msgid "New password confirmation"
msgstr "కొత్త సంకేతపదపు నిర్ధారణ"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""

msgid "Old password"
msgstr "పాత  సంకేతపదం"

msgid "Password (again)"
msgstr "సంకేతపదం (మళ్ళీ)"

msgid "algorithm"
msgstr ""

msgid "iterations"
msgstr ""

msgid "salt"
msgstr ""

msgid "hash"
msgstr ""

msgid "variety"
msgstr ""

msgid "version"
msgstr ""

msgid "memory cost"
msgstr ""

msgid "time cost"
msgstr ""

msgid "parallelism"
msgstr ""

msgid "work factor"
msgstr ""

msgid "checksum"
msgstr ""

msgid "name"
msgstr "పేరు"

msgid "content type"
msgstr ""

msgid "codename"
msgstr "సంహితనామము"

msgid "permission"
msgstr "అనుమతి"

msgid "permissions"
msgstr "అనుమతులు"

msgid "group"
msgstr "గుంపు"

msgid "groups"
msgstr "గుంపులు"

msgid "superuser status"
msgstr ""

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

msgid "user permissions"
msgstr "వాడుకరి అనుమతులు"

msgid "Specific permissions for this user."
msgstr ""

msgid "username"
msgstr "వాడుకరిపేరు"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

msgid "A user with that username already exists."
msgstr "ఈ నామముతొ ఇంకొ వినియొగదారి ఉన్నరు"

msgid "first name"
msgstr "మొదటి పేరు"

msgid "last name"
msgstr "ఇంటి పేరు"

msgid "email address"
msgstr ""

msgid "staff status"
msgstr "ఉద్యోగస్తుల  స్థితి"

msgid "Designates whether the user can log into this admin site."
msgstr ""

msgid "active"
msgstr "క్రియాశీలం"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

msgid "date joined"
msgstr "చేరిన తేదీ"

msgid "user"
msgstr "వాడుకరి"

msgid "users"
msgstr "వాడుకరులు"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr ""

msgid "Your password can't be too similar to your other personal information."
msgstr ""

msgid "This password is too common."
msgstr ""

msgid "Your password can't be a commonly used password."
msgstr ""

msgid "This password is entirely numeric."
msgstr ""

msgid "Your password can't be entirely numeric."
msgstr ""

#, python-format
msgid "Password reset on %(site_name)s"
msgstr ""

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

msgid "Logged out"
msgstr "నిష్క్రమించారు"

msgid "Password reset"
msgstr ""

msgid "Password reset sent"
msgstr ""

msgid "Enter new password"
msgstr ""

msgid "Password reset unsuccessful"
msgstr ""

msgid "Password reset complete"
msgstr ""

msgid "Password change"
msgstr ""

msgid "Password change successful"
msgstr ""
