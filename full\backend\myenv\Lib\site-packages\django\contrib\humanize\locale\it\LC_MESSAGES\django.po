# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2014
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2011
# <PERSON>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2015
# <PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2023-04-24 18:40+0000\n"
"Last-Translator: <PERSON> <pao<PERSON>@melchiorre.org>, 2023\n"
"Language-Team: Italian (http://www.transifex.com/django/django/language/"
"it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr "Umanizzazione "

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}esimo"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}esimo"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milione"
msgstr[1] "%(value)s milioni"
msgstr[2] "%(value)s milioni"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miliardo"
msgstr[1] "%(value)s miliardi"
msgstr[2] "%(value)s miliardi"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s migliaio di miliardi"
msgstr[1] "%(value)s migliaia di miliardi"
msgstr[2] "%(value)s migliaia di miliardi"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s milione di miliardi"
msgstr[1] "%(value)s milioni di miliardi"
msgstr[2] "%(value)s milioni di miliardi"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s miliardo di miliardi"
msgstr[1] "%(value)s miliardi di miliardi"
msgstr[2] "%(value)s miliardi di miliardi"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s migliaio di miliardi di miliardi"
msgstr[1] "%(value)s migliaia di miliardi di miliardi"
msgstr[2] "%(value)s migliaia di miliardi di miliardi"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s milione di miliardi di miliardi"
msgstr[1] "%(value)s milioni di miliardi di miliardi"
msgstr[2] "%(value)s milioni di miliardi di miliardi"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s miliardo di miliardi di miliardi"
msgstr[1] "%(value)s miliardi di miliardi di miliardi"
msgstr[2] "%(value)s miliardi di miliardi di miliardi"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s migliaio di miliardi di miliardi di miliardi"
msgstr[1] "%(value)s migliaia di miliardi di miliardi di miliardi"
msgstr[2] "%(value)s migliaia di miliardi di miliardi di miliardi"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s milione di miliardi di miliardi di miliardi"
msgstr[1] "%(value)s milioni di miliardi di miliardi di miliardi"
msgstr[2] "%(value)s milioni di miliardi di miliardi di miliardi"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"
msgstr[2] "%(value)s googol"

msgid "one"
msgstr "uno"

msgid "two"
msgstr "due"

msgid "three"
msgstr "tre"

msgid "four"
msgstr "quattro"

msgid "five"
msgstr "cinque"

msgid "six"
msgstr "sei"

msgid "seven"
msgstr "sette"

msgid "eight"
msgstr "otto"

msgid "nine"
msgstr "nove"

msgid "today"
msgstr "oggi"

msgid "tomorrow"
msgstr "domani"

msgid "yesterday"
msgstr "ieri"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s fa"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "un ora fa"
msgstr[1] "%(count)s ore fa"
msgstr[2] "%(count)s ore fa"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "un minuto fa"
msgstr[1] "%(count)s minuti fa"
msgstr[2] "%(count)s minuti fa"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "un secondo fa"
msgstr[1] "%(count)s secondi fa"
msgstr[2] "%(count)s secondi fa"

msgid "now"
msgstr "adesso"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "tra un secondo"
msgstr[1] "tra %(count)s secondi"
msgstr[2] "tra %(count)s secondi"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "tra un minuto"
msgstr[1] "tra %(count)s minuti"
msgstr[2] "tra %(count)s minuti"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "tra un'ora"
msgstr[1] "tra %(count)s ore"
msgstr[2] "tra %(count)s ore"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s da adesso"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d anno"
msgstr[1] "%(num)d anni"
msgstr[2] "%(num)d anni"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mese"
msgstr[1] "%(num)d mesi"
msgstr[2] "%(num)d mesi"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d settimana"
msgstr[1] "%(num)d settimane"
msgstr[2] "%(num)d settimane"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d giorno"
msgstr[1] "%(num)d giorni"
msgstr[2] "%(num)d giorni"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d ora"
msgstr[1] "%(num)d ore"
msgstr[2] "%(num)d ore"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuto"
msgstr[1] "%(num)d minuti"
msgstr[2] "%(num)d minuti"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d anno"
msgstr[1] "%(num)d anni"
msgstr[2] "%(num)d anni"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mese"
msgstr[1] "%(num)d mesi"
msgstr[2] "%(num)d mesi"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d settimana"
msgstr[1] "%(num)d settimane"
msgstr[2] "%(num)d settimane"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d giorno"
msgstr[1] "%(num)d giorni"
msgstr[2] "%(num)d giorni"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d ora"
msgstr[1] "%(num)d ore"
msgstr[2] "%(num)d ore"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuto"
msgstr[1] "%(num)d minuti"
msgstr[2] "%(num)d minuti"
