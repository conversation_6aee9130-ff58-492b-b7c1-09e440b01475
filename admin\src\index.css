@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    /* Deep blue theme */
    --navy-50: 220 100% 95%;
    --navy-100: 220 95% 90%;
    --navy-200: 220 90% 80%;
    --navy-300: 220 85% 70%;
    --navy-400: 220 80% 55%;
    --navy-500: 220 85% 40%;
    --navy-600: 220 90% 30%;
    --navy-700: 223 90% 25%;
    --navy-800: 225 90% 20%;
    --navy-900: 225 95% 15%;
    --navy-950: 228 100% 10%;

    /* Base colors */
    --background: 220 30% 98%;
    --foreground: 225 80% 5%;

    --card: 0 0% 100%;
    --card-foreground: 225 80% 5%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 225 80% 5%;
 
    --primary: 220 85% 40%;
    --primary-foreground: 210 20% 98%;
 
    --secondary: 220 30% 96%;
    --secondary-foreground: 220 85% 40%;
 
    --muted: 220 30% 96%;
    --muted-foreground: 220 30% 40%;
 
    --accent: 220 30% 96%;
    --accent-foreground: 220 85% 40%;
 
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 30% 90%;
    --input: 220 30% 90%;
    --ring: 220 85% 40%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 225 80% 5%;
    --foreground: 210 20% 98%;
 
    --card: 225 80% 5%;
    --card-foreground: 210 20% 98%;
 
    --popover: 225 80% 5%;
    --popover-foreground: 210 20% 98%;
 
    --primary: 220 85% 40%;
    --primary-foreground: 210 20% 98%;
 
    --secondary: 220 50% 15%;
    --secondary-foreground: 210 20% 98%;
 
    --muted: 220 50% 15%;
    --muted-foreground: 220 20% 70%;
 
    --accent: 220 50% 15%;
    --accent-foreground: 210 20% 98%;
 
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 20% 98%;
 
    --border: 220 50% 15%;
    --input: 220 50% 15%;
    --ring: 220 85% 40%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Prevent horizontal scroll on mobile */
  html, body {
    overflow-x: hidden;
  }

  /* Touch-friendly scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

@layer components {
  /* Touch-friendly button base */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  /* Touch-friendly input base */
  .input-touch {
    @apply min-h-[44px] text-base touch-manipulation;
    font-size: 16px; /* Prevent iOS zoom */
  }

  /* Mobile-first container */
  .container-responsive {
    @apply px-4 py-4 sm:px-6 sm:py-6 lg:px-8 lg:py-8;
  }

  /* Responsive grid layouts */
  .grid-responsive-1 {
    @apply grid grid-cols-1 gap-4 sm:gap-6 lg:gap-8;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8;
  }

  /* Card responsive */
  .card-responsive {
    @apply p-4 mb-4 sm:p-6 sm:mb-6;
  }

  /* Form responsive */
  .form-responsive {
    @apply space-y-3 sm:space-y-4 lg:space-y-6;
  }

  /* Button group responsive */
  .btn-group-responsive {
    @apply flex flex-col gap-3 sm:flex-row sm:gap-4;
  }

  /* Text responsive */
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  /* Modal responsive */
  .modal-responsive {
    @apply w-full h-full max-w-none max-h-none rounded-none sm:w-[90vw] sm:max-w-2xl sm:max-h-[90vh] sm:rounded-lg lg:w-[80vw] lg:max-w-4xl lg:max-h-[85vh];
  }

  /* Table responsive - hide on mobile, show cards instead */
  .table-responsive {
    @apply hidden sm:block;
  }

  .table-card-view {
    @apply block sm:hidden space-y-4;
  }

  /* Sidebar responsive */
  .sidebar-responsive {
    @apply fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out sm:relative sm:translate-x-0;
  }

  /* Safe area support for iOS */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  .min-h-screen-safe {
    min-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }

  /* Ant Design responsive overrides */
  .ant-card {
    @apply transition-all duration-200;
  }

  .ant-card .ant-card-body {
    @apply p-4 sm:p-6;
  }

  .ant-btn {
    @apply transition-all duration-150;
  }

  .ant-input,
  .ant-select-selector {
    @apply transition-all duration-150;
  }

  /* Mobile drawer specific styles */
  .mobile-drawer .ant-drawer-body {
    @apply p-0;
  }

  .mobile-drawer .ant-menu-item {
    @apply text-base;
    height: 48px;
    line-height: 48px;
  }

  .mobile-drawer .ant-menu-submenu-title {
    @apply text-base;
    height: 48px;
    line-height: 48px;
  }

  /* Fixed button on mobile */
  .mobile-fixed-button {
    @apply fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 z-50;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  }

  /* Responsive table utilities */
  @media (max-width: 767px) {
    .ant-table-wrapper {
      @apply hidden;
    }

    .table-card-view {
      @apply block;
    }
  }

  @media (min-width: 768px) {
    .table-card-view {
      @apply hidden;
    }
  }

  /* Touch feedback for mobile */
  @media (hover: none) and (pointer: coarse) {
    .ant-btn:active {
      @apply scale-95;
    }

    .ant-menu-item:active {
      @apply bg-gray-100;
    }
  }
}
