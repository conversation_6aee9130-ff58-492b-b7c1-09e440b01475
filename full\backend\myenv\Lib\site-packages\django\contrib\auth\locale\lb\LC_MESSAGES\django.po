# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-03-18 09:16+0100\n"
"PO-Revision-Date: 2015-03-18 10:30+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Luxembourgish (http://www.transifex.com/projects/p/django/"
"language/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Personal info"
msgstr ""

msgid "Permissions"
msgstr ""

msgid "Important dates"
msgstr ""

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

msgid "Password changed successfully."
msgstr ""

#, python-format
msgid "Change password: %s"
msgstr ""

msgid "Authentication and Authorization"
msgstr ""

msgid "No password set."
msgstr ""

msgid "Invalid password format or unknown hashing algorithm."
msgstr ""

msgid "The two password fields didn't match."
msgstr ""

msgid "Password"
msgstr ""

msgid "Password confirmation"
msgstr ""

msgid "Enter the same password as above, for verification."
msgstr ""

msgid ""
"Raw passwords are not stored, so there is no way to see this user's "
"password, but you can change the password using <a href=\"password/\">this "
"form</a>."
msgstr ""

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""

msgid "This account is inactive."
msgstr ""

msgid "Email"
msgstr ""

msgid "New password"
msgstr ""

msgid "New password confirmation"
msgstr ""

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""

msgid "Old password"
msgstr ""

msgid "Password (again)"
msgstr ""

msgid "algorithm"
msgstr ""

msgid "iterations"
msgstr ""

msgid "salt"
msgstr ""

msgid "hash"
msgstr ""

msgid "work factor"
msgstr ""

msgid "checksum"
msgstr ""

msgid "name"
msgstr ""

msgid "codename"
msgstr ""

msgid "permission"
msgstr ""

msgid "permissions"
msgstr ""

msgid "group"
msgstr ""

msgid "groups"
msgstr ""

msgid "password"
msgstr ""

msgid "last login"
msgstr ""

msgid "superuser status"
msgstr ""

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

msgid "user permissions"
msgstr ""

msgid "Specific permissions for this user."
msgstr ""

msgid "username"
msgstr ""

msgid "Required. 30 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

msgid ""
"Enter a valid username. This value may contain only letters, numbers and @/./"
"+/-/_ characters."
msgstr ""

msgid "A user with that username already exists."
msgstr ""

msgid "first name"
msgstr ""

msgid "last name"
msgstr ""

msgid "email address"
msgstr ""

msgid "staff status"
msgstr ""

msgid "Designates whether the user can log into this admin site."
msgstr ""

msgid "active"
msgstr ""

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

msgid "date joined"
msgstr ""

msgid "user"
msgstr ""

msgid "users"
msgstr ""

#, python-format
msgid "Password reset on %(site_name)s"
msgstr ""

msgid "Logged out"
msgstr ""

msgid "Password reset"
msgstr ""

msgid "Password reset sent"
msgstr ""

msgid "Enter new password"
msgstr ""

msgid "Password reset unsuccessful"
msgstr ""

msgid "Password reset complete"
msgstr ""

msgid "Password change"
msgstr ""

msgid "Password change successful"
msgstr ""
