# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON>, 2022-2023
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2013-2014
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2011,2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2023-12-04 18:40+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022-2023\n"
"Language-Team: Slovenian (http://app.transifex.com/django/django/language/"
"sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || "
"n%100==4 ? 2 : 3);\n"

msgid "Humanize"
msgstr "Počloveči"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr ""

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr ""

#. Translators: Ordinal format when value ends with 3, e.g. 83rd, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr ""

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr ""

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr ""

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milijon"
msgstr[1] "%(value)s milijona"
msgstr[2] "%(value)s milijoni"
msgstr[3] "%(value)s milijonov"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miljarda"
msgstr[1] "%(value)s miljardi"
msgstr[2] "%(value)s miljarde"
msgstr[3] "%(value)s miljard"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s triljon"
msgstr[1] "%(value)s triljona"
msgstr[2] "%(value)s triljoni"
msgstr[3] "%(value)s triljonov"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s kvadrilijon"
msgstr[1] "%(value)s kvadrilijona"
msgstr[2] "%(value)s kvadrilijoni"
msgstr[3] "%(value)s kvadrilijonov"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s kvintilijon"
msgstr[1] "%(value)s kvintilijona"
msgstr[2] "%(value)s kvintilijoni"
msgstr[3] "%(value)s kvintilijonov"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sekstilijon"
msgstr[1] "%(value)s sekstilijona"
msgstr[2] "%(value)s sekstilijoni"
msgstr[3] "%(value)s sekstilijonov"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septilijon"
msgstr[1] "%(value)s septilijona"
msgstr[2] "%(value)s septilijoni"
msgstr[3] "%(value)s septilijonov"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s oktilijon"
msgstr[1] "%(value)s oktilijona"
msgstr[2] "%(value)s oktilijoni"
msgstr[3] "%(value)s oktilijonov"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonilijon"
msgstr[1] "%(value)s nonilijona"
msgstr[2] "%(value)s nonilijoni"
msgstr[3] "%(value)s nonilijonov"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s decilijon"
msgstr[1] "%(value)s decilijona"
msgstr[2] "%(value)s decilijoni"
msgstr[3] "%(value)s decilijonov"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s gugol"
msgstr[1] "%(value)s gugola"
msgstr[2] "%(value)s gugoli"
msgstr[3] "%(value)s gugolov"

msgid "one"
msgstr "ena"

msgid "two"
msgstr "dve"

msgid "three"
msgstr "tri"

msgid "four"
msgstr "štiri"

msgid "five"
msgstr "pet"

msgid "six"
msgstr "šest"

msgid "seven"
msgstr "sedem"

msgid "eight"
msgstr "osem"

msgid "nine"
msgstr "devet"

msgid "today"
msgstr "danes"

msgid "tomorrow"
msgstr "jutri"

msgid "yesterday"
msgstr "včeraj"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "pred %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "pred %(count)s uro"
msgstr[1] "pred %(count)s urama"
msgstr[2] "pred %(count)s urami"
msgstr[3] "pred %(count)s urami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "pred %(count)s minuto"
msgstr[1] "pred %(count)s minutama"
msgstr[2] "pred %(count)s minutami"
msgstr[3] "pred %(count)s minutami"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "pred %(count)s sekundo"
msgstr[1] "pred %(count)s sekundama"
msgstr[2] "pred %(count)s sekundami"
msgstr[3] "pred %(count)s sekundami"

msgid "now"
msgstr "zdaj"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "čez %(count)s sekundo"
msgstr[1] "čez %(count)s sekundi"
msgstr[2] "čez %(count)s sekunde"
msgstr[3] "čez %(count)s sekund"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "čez %(count)s minuto"
msgstr[1] "čez %(count)s minuti"
msgstr[2] "čez %(count)s minute"
msgstr[3] "čez %(count)s minut"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "čez %(count)s uro"
msgstr[1] "čez %(count)s uri"
msgstr[2] "čez %(count)s ure"
msgstr[3] "čez %(count)s ur"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)sod zdaj"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d leto"
msgstr[1] "%(num)d leti"
msgstr[2] "%(num)d let"
msgstr[3] "%(num)d let"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mesec"
msgstr[1] "%(num)d meseca"
msgstr[2] "%(num)d mesecev"
msgstr[3] "%(num)d mesecev"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d teden"
msgstr[1] "%(num)d tedna"
msgstr[2] "%(num)d tednov"
msgstr[3] "%(num)d tednov"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dan"
msgstr[1] "%(num)d dneva"
msgstr[2] "%(num)d dni"
msgstr[3] "%(num)d dni"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d ura"
msgstr[1] "%(num)d uri"
msgstr[2] "%(num)d ur"
msgstr[3] "%(num)d ur"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuta"
msgstr[1] "%(num)d minuti"
msgstr[2] "%(num)d minut"
msgstr[3] "%(num)d minut"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d leto"
msgstr[1] "%(num)d leti"
msgstr[2] "%(num)d let"
msgstr[3] "%(num)d let"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mesec"
msgstr[1] "%(num)d meseca"
msgstr[2] "%(num)d mesecev"
msgstr[3] "%(num)d mesecev"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d teden"
msgstr[1] "%(num)d tedena"
msgstr[2] "%(num)d tedne"
msgstr[3] "%(num)d tedne"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dan"
msgstr[1] "%(num)d dneva"
msgstr[2] "%(num)d dni"
msgstr[3] "%(num)d dni"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d ura"
msgstr[1] "%(num)d uri"
msgstr[2] "%(num)d ure"
msgstr[3] "%(num)d ure"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuta"
msgstr[1] "%(num)d minuti"
msgstr[2] "%(num)d minut"
msgstr[3] "%(num)d minut"
