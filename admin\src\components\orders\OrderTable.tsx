import { useNavigate } from "react-router-dom";
import { Order, ShowroomStatus } from "../../types/order";
import { OrderTableView } from "./OrderTableView";
import { Staff } from "../../types/staff";

interface OrderTableProps {
  orders: Order[];
  staffList: Staff[];
  onUpdateStatus: (orderId: number, status: Order["status"]) => void;
  onUpdateDeliveryMethod: (
    orderId: number,
    params: { delivery_staff_id?: number | null; shipping_unit?: string }
  ) => void;
  onUpdatePaymentMethod?: (
    orderId: number,
    paymentMethod: "cod" | "cash" | "bank_transfer"
  ) => void;
  onUpdatePaymentStatus?: (
    orderId: number,
    paymentStatus: "paid" | "unpaid"
  ) => void;
  onUpdateShowroomStatus?: (
    orderId: number,
    showroomStatus: ShowroomStatus
  ) => void;
  isShowroomTable?: boolean;
}

export function OrderTable({
  orders,
  staffList,
  onUpdateStatus,
  onUpdateDeliveryMethod,
  onUpdatePaymentMethod,
  onUpdatePaymentStatus,
  onUpdateShowroomStatus,
  isShowroomTable = false,
}: OrderTableProps) {
  const navigate = useNavigate();
  // const isDesktop = useBreakpoint("lg");

  // if (!isDesktop) {
  //   return (
  //     <div className="space-y-4">
  //       {orders.map((order) => (
  //         <OrderCard
  //           key={order.id}
  //           order={order}
  //           onUpdateStatus={onUpdateStatus}
  //           onViewDetails={(id) => navigate(`/orders/${id}`)}
  //         />
  //       ))}
  //     </div>
  //   );
  // }

  return (
    // <div className="hidden lg:block">
    <OrderTableView
      orders={orders}
      staffList={staffList}
      onUpdateStatus={onUpdateStatus}
      onUpdateDeliveryMethod={onUpdateDeliveryMethod}
      onUpdatePaymentMethod={onUpdatePaymentMethod || (() => {})}
      onUpdatePaymentStatus={onUpdatePaymentStatus}
      onUpdateShowroomStatus={onUpdateShowroomStatus}
      onViewDetails={(id) => navigate(`/orders/${id}`)}
      isShowroomTable={isShowroomTable}
    />
    // </div>
  );
}
