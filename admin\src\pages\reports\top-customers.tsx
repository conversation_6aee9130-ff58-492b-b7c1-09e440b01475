import React from "react";
import {
  Table,
  Card,
  Spin,
  Typography,
  Button,
  InputNumber,
  Space,
  Select,
} from "antd";
import {
  DownloadOutlined,
  UserOutlined,
  TrophyOutlined,
  SortAscendingOutlined,
} from "@ant-design/icons";
import { DateRangePickerWithPresets } from "@/components/common/DateRangePickerWithPresets";
import { TopCustomerItem } from "@/types/report";
import { formatCurrency } from "@/lib/utils";
import dayjs from "dayjs";
import { useTopCustomersReport } from "@/hooks/useTopCustomersReport";

const { Title, Text } = Typography;

const TopCustomersReport: React.FC = () => {
  const {
    // Data states
    loading,
    error,
    data,

    // Filter states
    dateRange,
    orderBy,
    limitOption,
    customLimit,

    // Filter setters
    setDateRange,
    setOrderBy,
    setLimitOption,
    setCustomLimit,

    // Actions
    handleSearch,
    handleExportExcel,

    // Computed values
    displayTitle,
    totalCount,
  } = useTopCustomersReport();

  const columns = [
    {
      title: "Hạng",
      key: "rank",
      width: 80,
      align: "center" as const,
      render: (_: any, record: TopCustomerItem, index: number) => {
        const rank = record.rank || index + 1;
        return (
          <div className="flex items-center justify-center">
            {rank <= 3 && (
              <TrophyOutlined
                className={`mr-1 ${
                  rank === 1
                    ? "text-yellow-500"
                    : rank === 2
                    ? "text-gray-400"
                    : "text-orange-600"
                }`}
              />
            )}
            <span
              className={`font-semibold ${
                rank === 1
                  ? "text-yellow-600"
                  : rank === 2
                  ? "text-gray-500"
                  : rank === 3
                  ? "text-orange-600"
                  : ""
              }`}
            >
              {rank}
            </span>
          </div>
        );
      },
    },
    {
      title: "Khách hàng",
      key: "customer",
      render: (record: TopCustomerItem) => (
        <div className="flex items-center">
          <UserOutlined className="mr-2 text-blue-500" />
          <div>
            <div className="font-medium">{record.user_name}</div>
            <div className="text-sm text-gray-500">{record.email}</div>
            <div className="text-xs text-gray-400 capitalize">
              {record.role}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Tổng doanh thu",
      dataIndex: "total_spent",
      key: "total_spent",
      align: "right" as const,
      render: (value: number) => (
        <span className="font-semibold text-green-600">
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      title: "Số đơn hàng",
      dataIndex: "total_orders",
      key: "total_orders",
      align: "center" as const,
      render: (value: number) => <span className="font-medium">{value}</span>,
    },
    {
      title: "Giá trị TB/đơn",
      key: "average_order_value",
      align: "right" as const,
      render: (record: TopCustomerItem) => {
        const avgValue =
          record.total_orders > 0
            ? record.total_spent / record.total_orders
            : 0;
        return (
          <span className="text-blue-600">{formatCurrency(avgValue)}</span>
        );
      },
    },
    {
      title: "Đơn hàng cuối",
      dataIndex: "last_order_date",
      key: "last_order_date",
      align: "center" as const,
      render: (value: string) => (
        <span className="text-gray-600">
          {value ? dayjs(value).format("DD/MM/YYYY") : "—"}
        </span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4">
        <div>
          <Title level={3} className="mb-1">
            Báo cáo khách hàng có doanh thu cao nhất
          </Title>
          <Text type="secondary">
            Xem danh sách khách hàng có doanh thu cao nhất trong khoảng thời
            gian được chọn
          </Text>
        </div>

        {/* Filters */}
        <Card className="shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Khoảng thời gian
              </label>
              <DateRangePickerWithPresets
                size="large"
                value={dateRange}
                onChange={(dates) => setDateRange(dates)}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Sắp xếp theo
              </label>
              <Select
                size="large"
                value={orderBy}
                onChange={setOrderBy}
                className="w-full"
                options={[
                  { value: "total_spent_desc", label: "Doanh thu cao → thấp" },
                  { value: "total_spent_asc", label: "Doanh thu thấp → cao" },
                  { value: "total_orders_desc", label: "Đơn hàng nhiều → ít" },
                  { value: "total_orders_asc", label: "Đơn hàng ít → nhiều" },
                ]}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Số lượng hiển thị
              </label>
              <Select
                size="large"
                value={limitOption}
                onChange={(value) => {
                  setLimitOption(value);
                  if (value === "custom") {
                    setCustomLimit(10);
                  }
                }}
                className="w-full"
                options={[
                  { value: "top10", label: "Top 10" },
                  { value: "top20", label: "Top 20" },
                  { value: "top50", label: "Top 50" },
                  { value: "all", label: "Tất cả" },
                  { value: "custom", label: "Tùy chỉnh" },
                ]}
              />
            </div>
          </div>

          {limitOption === "custom" && (
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Số lượng tùy chỉnh
              </label>
              <InputNumber
                size="large"
                min={1}
                max={1000}
                value={customLimit}
                onChange={(value) => setCustomLimit(value || 10)}
                addonAfter="người"
                className="w-full max-w-xs"
              />
            </div>
          )}

          <div className="flex justify-end">
            <Space>
              <Button
                type="primary"
                size="large"
                onClick={handleSearch}
                loading={loading}
                icon={<SortAscendingOutlined />}
              >
                Tìm kiếm
              </Button>
              <Button
                size="large"
                icon={<DownloadOutlined />}
                onClick={handleExportExcel}
                disabled={data.length === 0}
              >
                Xuất Excel
              </Button>
            </Space>
          </div>
        </Card>
      </div>

      {error && (
        <div className="text-red-500 bg-red-50 p-4 rounded-md">{error}</div>
      )}

      <Spin spinning={loading}>
        <Card
          title={
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <TrophyOutlined className="mr-2 h-5 w-5 text-yellow-500" />
                <span>{displayTitle}</span>
              </div>
              {totalCount > 0 && (
                <Text type="secondary">Tổng số: {totalCount} khách hàng</Text>
              )}
            </div>
          }
          className="shadow-sm"
        >
          <Table
            columns={columns}
            dataSource={data}
            rowKey="user_id"
            pagination={false}
            locale={{
              emptyText: "Không có dữ liệu khách hàng",
            }}
            rowClassName={(record, index) => {
              const rank = record.rank || index + 1;
              if (rank === 1) return "bg-yellow-50";
              if (rank === 2) return "bg-gray-50";
              if (rank === 3) return "bg-orange-50";
              return "";
            }}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default TopCustomersReport;
