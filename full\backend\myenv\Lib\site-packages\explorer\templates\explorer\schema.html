{% extends "explorer/base.html" %}
{% load i18n %}


{% block sql_explorer_content_takeover %}
<div class="schema-wrapper">
    <h4>{% translate "Schema" %}</h4>
    <div id="schema-contents">
        <p><input class="search form-control" placeholder="{% translate "Search Tables" %}" /></p>
        <div class="row">
            <div class="col">
                <a class="link-primary" id="collapse_all">
                    {% translate "Collapse Tables" %}
                </a>
            </div>
            <div class="col">
                <a class="link-primary" id="expand_all">
                    {% translate "Expand Tables" %}
                </a>
            </div>
        </div>
        <div class="mt-3">
            <ul class="list">
                {% for m in schema %}
                    <li>
                        <div class="app-name schema-header fw-semibold" style="display: inline-block">{{ m.0 }}</div>
                        <div class="schema-table">
                            <table class="table table-sm">
                                <tbody>
                                    {% for c in m.1 %}
                                        <tr>
                                            <td><code class="copyable">{{ c.0 }}</code></td>
                                            <td class="text-muted small">{{ c.1 }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}
{% block sql_explorer_footer %}{% endblock %}
