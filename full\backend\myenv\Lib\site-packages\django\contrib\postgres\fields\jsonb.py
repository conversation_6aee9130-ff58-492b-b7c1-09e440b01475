from django.db.models import <PERSON><PERSON><PERSON>ield as BuiltinJSO<PERSON>ield

__all__ = ["JSONField"]


class J<PERSON><PERSON>ield(BuiltinJSONField):
    system_check_removed_details = {
        "msg": (
            "django.contrib.postgres.fields.JSONField is removed except for "
            "support in historical migrations."
        ),
        "hint": "Use django.db.models.JSONField instead.",
        "id": "fields.E904",
    }
