# Generated by Django 5.0.4 on 2024-08-22 01:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('explorer', '0025_alter_query_database_connection_alter_querylog_database_connection'),
    ]

    operations = [
        migrations.CreateModel(
            name='TableDescription',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('table_name', models.CharField(max_length=512)),
                ('description', models.TextField()),
                ('database_connection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='explorer.databaseconnection')),
            ],
            options={
                'unique_together': {('database_connection', 'table_name')},
            },
        ),
    ]
