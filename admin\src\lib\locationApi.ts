import axios from 'axios';

const ESGOO_API_URL = 'https://esgoo.net/api-tinhthanh';

export interface LocationItem {
  value: string;
  label: string;
}

interface EsgooResponseList<T> {
  error: number;
  error_text: string;
  data_name: string;
  data: T[];
}

interface EsgooResponseSingle {
  error: number;
  error_text: string;
  data: {
    name: string;
    name_en: string;
    full_name: string;
    full_name_en: string;
    latitude: string;
    longitude: string;
    tinh: string;
    quan: string;
    phuong: number;
  };
}

interface EsgooLocation {
  id: string;
  name: string;
  name_en: string;
  full_name: string;
  full_name_en: string;
  latitude: string;
  longitude: string;
}

const esgooAxios = axios.create({
  baseURL: ESGOO_API_URL,
});

export const locationApi = {
  async getProvinces(): Promise<LocationItem[]> {
    try {
      const response = await esgooAxios.get<EsgooResponseList<EsgooLocation>>('/1/0.htm');
      if (response.data.error === 0) {
        return response.data.data.map(province => ({
          value: province.id,
          label: province.full_name
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch provinces:', error);
      return [];
    }
  },

  async getDistricts(provinceId: string): Promise<LocationItem[]> {
    try {
      const response = await esgooAxios.get<EsgooResponseList<EsgooLocation>>(`/2/${provinceId}.htm`);
      if (response.data.error === 0) {
        return response.data.data.map(district => ({
          value: district.id,
          label: district.full_name
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch districts:', error);
      return [];
    }
  },

  async getWards(districtId: string): Promise<LocationItem[]> {
    try {
      const response = await esgooAxios.get<EsgooResponseList<EsgooLocation>>(`/3/${districtId}.htm`);
      if (response.data.error === 0) {
        return response.data.data.map(ward => ({
          value: ward.id,
          label: ward.full_name
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch wards:', error);
      return [];
    }
  },

  async getLocationName(locationId: string): Promise<string> {
    try {
      const response = await esgooAxios.get<EsgooResponseSingle>(`/5/${locationId}.htm`);
      if (response.data.error === 0) {
        return response.data.data.full_name;
      }
      return '';
    } catch (error) {
      console.error('Failed to fetch location name:', error);
      return '';
    }
  }
};
