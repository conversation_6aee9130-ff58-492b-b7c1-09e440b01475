# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# Fot<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2016
# Pã<PERSON>ș <<EMAIL>>, 2014
# Yo<PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-08-04 06:47+0000\n"
"Last-Translator: Fotis <PERSON> <<EMAIL>>\n"
"Language-Team: Greek (http://www.transifex.com/django/django/language/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Διαχειριστική Τεκμηρίωση"

msgid "Home"
msgstr "Αρχική"

msgid "Documentation"
msgstr "Τεκμηρίωση"

msgid "Bookmarklets"
msgstr "Σελιδοδείκτες"

msgid "Documentation bookmarklets"
msgstr "Σελιδοδείκτες τεκμηρίωσης"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Για την εγκατάσταση bookmarklets, σύρετε το link στην γραμμή εργαλείων των "
"bookmarks του browser σας ή δεξί-κλικ πάνω στο link και προσθέστε το στα "
"bookmarks. Τώρα μπορείτε να επιλέξετε το bookmarklet μέσα από κάθε σελίδα "
"του site."

msgid "Documentation for this page"
msgstr "Τεκμηρίωση για αυτήν τη σελίδα"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Μεταπήδηση από οποιαδήποτε σελίδα στην τεκμηρίωση για το view που "
"δημιούργησε τη σελίδα αυτή."

msgid "Tags"
msgstr "Ετικέτες"

msgid "List of all the template tags and their functions."
msgstr "Λίστα όλωςν τον ετικετών περιγραμμάτων και των λειτουργιών τους."

msgid "Filters"
msgstr " Φίλτρα"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Τα φίλτρα είναι ενέργειες που μπορούν να εφαρμοστούν σε μεταβλητές μέσα σε "
"ένα περίγραμμα για να αλλάξουν το αποτέλεσμα."

msgid "Models"
msgstr "Μοντέλα"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Τα μοντέλα είναι περιγραφές όλωςν τον αντικειμένων στο σύστημα και των "
"συνδεδεμένων πεδίων. Κάθε μοντέλο έχει μια λίστα πεδιών που είναι προσβάσιμα "
"ως μεταβλητές περιγραμμάτων"

msgid "Views"
msgstr "Οπτικές"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Κάθε σελίδα στη δημόσια ιστοσελίδα παράγεται από μια οπτική. Η οπτική ορίζει "
"ποιό περίγραμμα θα χρησιμοποιηθεί για να παραχθεί η σελίδα και ποιά "
"αντικείμενα είναι διαθέσιμα σε αυτό το περίγραμμα."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Εργαλεία για το πρόγραμμα περιήγησης για γρήγορη πρόσβαση σε διαχειριστική "
"λειτουργικότητα. "

msgid "Please install docutils"
msgstr "Παρακαλώ εγκαταστείστε τα docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Το σύστημα διαχειριστικής τεκμηρίωσης απαιτεί την βιβλιοθήκη της Python <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Παρακαλώ ζητήστε απο τον διαχειριστή σας να εγκαταστήσει τα <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Μοντέλο: %(name)s"

msgid "Fields"
msgstr "Πεδία"

msgid "Field"
msgstr "Πεδίο"

msgid "Type"
msgstr "Τύπος"

msgid "Description"
msgstr "Περιγραφή"

msgid "Methods with arguments"
msgstr "Μέθοδοι με ορίσματα"

msgid "Method"
msgstr "Μέθοδος"

msgid "Arguments"
msgstr "Ορίσματα"

msgid "Back to Model documentation"
msgstr "Πίσω στην τεκμηρίωση των Μοντέλων"

msgid "Model documentation"
msgstr "Τεκμηρίωση Μοντέλων"

msgid "Model groups"
msgstr "Ομάδες μοντέλου"

msgid "Templates"
msgstr "Περιγράμματα"

#, python-format
msgid "Template: %(name)s"
msgstr "Περίγραμμα: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Περίγραμμα: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Αναζήτηση μονοπατιού για περίγραμμα <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(δεν υπάρχει)"

msgid "Back to Documentation"
msgstr "Επιστροφή στην Τεκμηρίωση"

msgid "Template filters"
msgstr "Φίλτρα περιγραμμάτων"

msgid "Template filter documentation"
msgstr "Τεκμηρίωση φίλτρων περιγραμμάτων"

msgid "Built-in filters"
msgstr "Ενσωματωμένα φίλτρα"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Για να χρησιμοποιήσετε αυτά τα φίλτρα, βάλτε <code>%(code)s</code> στο "
"περίγραμμα σας, προτού χρησιμοποιήσετε το φίλτρο."

msgid "Template tags"
msgstr "Ετικέτες περιγραμμάτων"

msgid "Template tag documentation"
msgstr "Τεκμηρίωση ετικετών περιγραμμάτων"

msgid "Built-in tags"
msgstr "Ενσωματωμένες ετικέτες"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Για να χρησιμοποιήσετε αυτές τις ετικέτες, βάλτε <code>%(code)s</code> στο "
"περίγραμμα σας, προτού χρησιμοποιήσετε την ετικέτα."

#, python-format
msgid "View: %(name)s"
msgstr "Οπτική: %(name)s"

msgid "Context:"
msgstr "Πλαίσιο:"

msgid "Templates:"
msgstr "Περιγράμματα:"

msgid "Back to View documentation"
msgstr "Πίσω στην τεκμηρίωση του View"

msgid "View documentation"
msgstr "Προβολή Τεκμηρίωσης"

msgid "Jump to namespace"
msgstr "Μεταφερθείτε στο namespace"

msgid "Empty namespace"
msgstr "Άδειο namespace"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Οπτικές ανά namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Οπτικές ανά κενό namespace"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Συνάρτηση οπτικής: <code>%(full_name)s</code>. Όνομα: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "ετικέτα:"

msgid "filter:"
msgstr "φίλτρο:"

msgid "view:"
msgstr "προβολή:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Η εφαρμογή %(app_label)r δεν βρέθηκε"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"Το μοντέλο %(model_name)r δεν μπορεί να βρεθεί στην εφαρμογή %(app_label)r"

msgid "model:"
msgstr "μοντέλο:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "το συσχετισμένο `%(app_label)s.%(data_type)s`  αντικείμενο"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "συσχετισμένα `%(app_label)s.%(object_name)s` αντικείμενα"

#, python-format
msgid "all %s"
msgstr "όλα %s"

#, python-format
msgid "number of %s"
msgstr "πλήθος number of %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "Το %s δε φαίνεται να είναι ένα αντικείμενο urlpattern"
