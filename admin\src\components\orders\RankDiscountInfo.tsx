import React from 'react';
import { Card, Typography, Space, Divider } from 'antd';
import { CustomerRankBadge } from '@/components/customers/CustomerRankBadge';
import { CustomerService } from '@/services/customerService';
import { Order } from '@/types/order';
import { formatCurrency } from '@/lib/utils';

const { Text } = Typography;

interface RankDiscountInfoProps {
  order: Order;
}

export const RankDiscountInfo: React.FC<RankDiscountInfoProps> = ({ order }) => {
  const customerRank = order.user.rank || 'normal';
  const rankInfo = CustomerService.getRankInfo(customerRank);
  
  if (customerRank === 'normal') {
    return null;
  }

  return (
    <Card className="mb-4" size="small">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Text strong>Thông tin hạng khách hàng</Text>
          <CustomerRankBadge rank={customerRank} size="small" />
        </div>
        
        <Divider className="my-2" />
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Text type="secondary">Hạng khách hàng:</Text>
            <Text>{rankInfo.label}</Text>
          </div>
          
          <div className="flex justify-between items-center">
            <Text type="secondary">Phần trăm giảm giá:</Text>
            <Text>{rankInfo.discount}%</Text>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default RankDiscountInfo;
