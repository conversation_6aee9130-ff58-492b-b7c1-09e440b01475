# Generated by Django 5.0.4 on 2024-05-14 15:55

import django_cryptography.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('explorer', '0017_databaseconnection'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='databaseconnection',
            name='host',
            field=django_cryptography.fields.encrypt(models.Char<PERSON>ield(blank=True, max_length=255)),
        ),
        migrations.AlterField(
            model_name='databaseconnection',
            name='password',
            field=django_cryptography.fields.encrypt(models.<PERSON>r<PERSON>ield(blank=True, max_length=255)),
        ),
        migrations.Alter<PERSON>ield(
            model_name='databaseconnection',
            name='user',
            field=django_cryptography.fields.encrypt(models.CharField(blank=True, max_length=255)),
        ),
    ]
