# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Kazakh (http://www.transifex.com/django/django/language/kk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: kk\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL  кеңейтулері"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr ""

msgid "Nested arrays must have the same length."
msgstr "Бір-бірін ішіне салынған ауқымдардың ұзындықтары бірдей болу керек"

msgid "Map of strings to strings/nulls"
msgstr ""

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr ""

msgid "Input must be a JSON dictionary."
msgstr ""

msgid "Enter two valid values."
msgstr ""

msgid "The start of the range must not exceed the end of the range."
msgstr ""

msgid "Enter two whole numbers."
msgstr ""

msgid "Enter two numbers."
msgstr ""

msgid "Enter two valid date/times."
msgstr ""

msgid "Enter two valid dates."
msgstr ""

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr ""

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr ""

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr ""
