# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011-2012
# <AUTHOR> <EMAIL>, 2014-2015,2017,2019
# <PERSON><PERSON><PERSON>s <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2022-04-24 19:03+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Esperanto (http://www.transifex.com/django/django/language/"
"eo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Altnivelaj elektoj"

msgid "Flat Pages"
msgstr "Simplaj paĝoj"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Ekzemple: “/about/contact/”. Certigu, ke estas suprenstrekoj komence kaj "
"fine."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Ĉi tiu valoro devus enhavi sole literojn, ciferojn, punktojn, substrekojn, "
"haltostrekojn, oblikvajn strekojn, aŭ tildojn."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Ekzemple: “/about/contact”. Certigu, ke estas suprenstreko komence."

msgid "URL is missing a leading slash."
msgstr "La strek-signo ‘/’ ne ĉeestas en la komenco de URL."

msgid "URL is missing a trailing slash."
msgstr "La strek-signo ‘/’ ne ĉeestas en la fino de URL."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Simpla paĝo kun URL %(url)s jam ekzistas por la retejo %(site)s"

msgid "title"
msgstr "titolo"

msgid "content"
msgstr "enhavo"

msgid "enable comments"
msgstr "ebligi rimarkojn"

msgid "template name"
msgstr "nomo de ŝablono"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Ekzemple: “flatpages/contact_page.html”. Se la ŝablono ne estas indikita, "
"estos uzata “flatpages/default.html”."

msgid "registration required"
msgstr "registriĝo postulita"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Kiam la marko-butono estas elektita, nur ensalutintaj uzantoj povas rigardi "
"la paĝon."

msgid "sites"
msgstr "retejoj"

msgid "flat page"
msgstr "simpla paĝo"

msgid "flat pages"
msgstr "simplaj paĝoj"
