import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { fileURLToPath } from "url";
import { execSync } from "child_process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get current date formatted as YYYY-MM-DD
const buildDate = new Date().toISOString().split('T')[0];

// Try to get git commit hash, default to dev if not in a git repo
let commitHash = "dev";
try {
  commitHash = execSync("git rev-parse HEAD").toString().trim();
} catch (e) {
  console.log("Not a git repository or git not found");
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
  },
  define: {
    // Simple build info variables
    'import.meta.env.VITE_BUILD_DATE': JSON.stringify(buildDate),
    'import.meta.env.VITE_COMMIT_HASH': JSON.stringify(commitHash),
  },
});
