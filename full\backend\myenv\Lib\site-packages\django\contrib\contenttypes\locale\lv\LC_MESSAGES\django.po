# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-07 07:19+0000\n"
"Last-Translator: NullIsNot0 <<EMAIL>>\n"
"Language-Team: Latvian (http://www.transifex.com/django/django/language/"
"lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

msgid "Content Types"
msgstr "Satura tipi"

msgid "python model class name"
msgstr "python modeļa klases nosaukums"

msgid "content type"
msgstr "satura tips"

msgid "content types"
msgstr "satura tipi"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Satura tipa %(ct_id)s objektam nav asociētā modeļa"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Satura tipa %(ct_id)s objekts %(obj_id)s neeksistē"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s objekti nesatur get_absolute_url() metodi"
