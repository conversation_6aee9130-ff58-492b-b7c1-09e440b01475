# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <PERSON> <<EMAIL>>, 2019,2023,2025
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: <PERSON> <PERSON> <<EMAIL>>, 2019,2023,2025\n"
"Language-Team: Afrikaans (http://app.transifex.com/django/django/language/"
"af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administratiewe dokumentasie"

msgid "Home"
msgstr "Tuisblad"

msgid "Documentation"
msgstr "Dokumentasie"

msgid "Bookmarklets"
msgstr ""

msgid "Documentation bookmarklets"
msgstr ""

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "Dokumentasie vir dié bladsy"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Neem jou van enige bladsy na die dokumentasie vir die vertooning wat daardie "
"bladsy genereer."

msgid "Tags"
msgstr "Etikette"

msgid "List of all the template tags and their functions."
msgstr "Lys van alle templaat tags en hulle funksies."

msgid "Filters"
msgstr "Filters"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr "Modelle"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modelle is beskrywings van al die objekte in die stelsel met hulle velde. "
"Elke model het 'n lys velde wat bereikbaar is as veranderlikes in 'n "
"sjabloon."

msgid "Views"
msgstr ""

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Elke bladsy op die publieke werf word gegenereer deur 'n vertoning. Die "
"vertoning definiëer watter templaat word gebruik om die bladsy te genereer "
"en watter objekte is beskikbaar aan daardie templaat."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr "Installeer asseblief docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Die admin dokumentasie stelsel benodig Python se <a "
"href=\"%(link)s\">docutils</a> biblioteek."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Vra asseblief jou administrateurs om <a href=\"%(link)s\">docutils</a> te "
"installeer."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Velde"

msgid "Field"
msgstr "Veld"

msgid "Type"
msgstr "Tipe"

msgid "Description"
msgstr "Beskrywing"

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr "Terug na Model dokumentasie"

msgid "Model documentation"
msgstr "Model dokumentasie"

msgid "Model groups"
msgstr "Model groepe"

msgid "Templates"
msgstr "Template"

#, python-format
msgid "Template: %(name)s"
msgstr "Templaat: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Templaat: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr ""

msgid "(does not exist)"
msgstr "(bestaan nie)"

msgid "Back to Documentation"
msgstr "Terug na Dokumentasie"

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr "Konteks:"

msgid "Templates:"
msgstr "Sjablone:"

msgid "Back to View documentation"
msgstr "Terug na Sien dokumentasie"

msgid "View documentation"
msgstr "Sien dokumentasie"

msgid "Jump to namespace"
msgstr "Spring na naamruimte"

msgid "Empty namespace"
msgstr "Leë naamruimte"

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr ""

msgid "filter:"
msgstr ""

msgid "view:"
msgstr ""

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""

msgid "model:"
msgstr ""

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr ""

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr ""

#, python-format
msgid "all %s"
msgstr "alle %s"

#, python-format
msgid "number of %s"
msgstr ""

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr ""
