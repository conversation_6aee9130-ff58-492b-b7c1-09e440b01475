import React from 'react';
import { Badge, Tooltip } from 'antd';
import { CustomerService } from '@/services/customerService';
import { formatCurrency } from '@/lib/utils';

interface CustomerRankBadgeProps {
  rank: 'normal' | 'silver' | 'gold';
  totalSpent?: number;
  showProgress?: boolean;
  size?: 'small' | 'default' | 'large';
}

export const CustomerRankBadge: React.FC<CustomerRankBadgeProps> = ({
  rank,
  totalSpent,
  showProgress = false,
  size = 'default',
}) => {
  const rankInfo = CustomerService.getRankInfo(rank);
  
  const getBadgeStyle = () => {
    const baseStyle = {
      backgroundColor: rankInfo.color,
      color: rank === 'silver' ? '#000' : '#fff',
      fontWeight: 'bold' as const,
      border: 'none',
    };

    if (size === 'small') {
      return { ...baseStyle, fontSize: '10px', padding: '0 4px' };
    } else if (size === 'large') {
      return { ...baseStyle, fontSize: '14px', padding: '4px 8px' };
    }
    
    return baseStyle;
  };

  const getTooltipContent = () => {
    if (!totalSpent) {
      return `Hạng ${rankInfo.label} - Giảm giá ${rankInfo.discount}%`;
    }

    const progress = CustomerService.calculateRankProgress(totalSpent, rank);
    const formattedSpent = formatCurrency(totalSpent);
    
    let content = `Hạng ${rankInfo.label}\nTổng chi tiêu: ${formattedSpent}\nGiảm giá: ${rankInfo.discount}%`;
    
    if (progress.nextThreshold) {
      const remaining = progress.nextThreshold - totalSpent;
      const formattedRemaining = formatCurrency(remaining);
      const nextRank = rank === 'normal' ? 'Bạc' : 'Vàng';
      content += `\n\nCần thêm ${formattedRemaining} để lên hạng ${nextRank}`;
    }

    return content;
  };

  const badge = (
    <Badge
      count={`${rankInfo.icon} ${rankInfo.label}`}
      style={getBadgeStyle()}
    />
  );

  if (showProgress && totalSpent) {
    const progress = CustomerService.calculateRankProgress(totalSpent, rank);
    
    return (
      <div className="flex items-center gap-2">
        <Tooltip title={getTooltipContent()} placement="top">
          {badge}
        </Tooltip>
        {progress.nextThreshold && (
          <div className="flex-1 min-w-0">
            <div className="text-xs text-gray-500 mb-1">
              Tiến độ lên hạng: {progress.progress}%
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress.progress}%` }}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <Tooltip title={getTooltipContent()} placement="top">
      {badge}
    </Tooltip>
  );
};

export default CustomerRankBadge;
