Metadata-Version: 2.2
Name: django-safedelete
Version: 1.4.1
Summary: Mask your objects instead of deleting them from your database.
Home-page: https://github.com/makinacorpus/django-safedelete
Download-URL: https://github.com/makinacorpus/django-safedelete/tarball/1.4.1
Author: <PERSON><PERSON><PERSON> Auguste
Author-email: <EMAIL>
License: BSD
Keywords: django,delete,safedelete,softdelete
Classifier: Framework :: Django
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Development Status :: 4 - Beta
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: Django
Requires-Dist: packaging
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: download-url
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: requires-dist
Dynamic: summary

Django safedelete
=================

.. image:: https://github.com/makinacorpus/django-safedelete/workflows/Python%20application/badge.svg
    :target: https://github.com/makinacorpus/django-safedelete/actions?query=workflow%3A%22Python+application%22

.. image:: https://img.shields.io/pypi/dm/django-safedelete.svg
        :target: https://pypi.python.org/pypi/django-safedelete

.. image:: https://coveralls.io/repos/makinacorpus/django-safedelete/badge.png
    :target: https://coveralls.io/r/makinacorpus/django-safedelete


What is it ?
------------

For various reasons, you may want to avoid deleting objects from your database.

This Django application provides an abstract model, that allows you to transparently retrieve or delete your objects,
without having them deleted from your database.

You can choose what happens when you delete an object :
 - it can be masked from your database (SOFT_DELETE, the default behavior)
 - it can be masked from your database and mask any dependent models. (SOFT_DELETE_CASCADE)
 - it can be normally deleted (HARD_DELETE)
 - it can be hard-deleted, but if its deletion would delete other objects, it will only be masked (HARD_DELETE_NOCASCADE)
 - it can be never deleted or masked from your database (NO_DELETE, use with caution)


Example
-------

.. code-block:: python

    # imports
    from safedelete.models import SafeDeleteModel
    from safedelete.models import HARD_DELETE_NOCASCADE

    # Models

    # We create a new model, with the given policy : Objects will be hard-deleted, or soft deleted if other objects would have been deleted too.
    class Article(SafeDeleteModel):
        _safedelete_policy = HARD_DELETE_NOCASCADE

        name = models.CharField(max_length=100)

    class Order(SafeDeleteModel):
        _safedelete_policy = HARD_DELETE_NOCASCADE

        name = models.CharField(max_length=100)
        articles = models.ManyToManyField(Article)


    # Example of use

    >>> article1 = Article(name='article1')
    >>> article1.save()

    >>> article2 = Article(name='article2')
    >>> article2.save()

    >>> order = Order(name='order')
    >>> order.save()
    >>> order.articles.add(article1)

    # This article will be masked, but not deleted from the database as it is still referenced in an order.
    >>> article1.delete()

    # This article will be deleted from the database.
    >>> article2.delete()


Compatibilities
---------------

* Branch 0.2.x is compatible with django >= 1.2
* Branch 0.3.x is compatible with django >= 1.4
* Branch 0.4.x is compatible with django >= 1.8
* Branch 0.5.x is compatible with django >= 1.11
* Branch 1.0.x, 1.1.x and 1.2.x are compatible with django >= 2.2
* Branch 1.3.x is compatible with django >= 3.2 and Python >= 3.7

Current branch (1.3.x) is tested with :

*  Django 3.2 using python 3.7 to 3.10.
*  Django 4.0 using python 3.8 to 3.10.
*  Django 4.1 using python 3.8 to 3.10.
*  Django 4.2 using python 3.8 to 3.11.


Installation
------------

Installing from pypi (using pip). ::

    pip install django-safedelete


Installing from github. ::

    pip install -e git://github.com/makinacorpus/django-safedelete.git#egg=django-safedelete

Add ``safedelete`` in your ``INSTALLED_APPS``:

.. code-block:: python

    INSTALLED_APPS = [
        'safedelete',
        [...]
    ]


The application doesn't have any special requirement.


Configuration
-------------

In the main django settings you can activate the boolean variable ``SAFE_DELETE_INTERPRET_UNDELETED_OBJECTS_AS_CREATED``.
If you do this the ``update_or_create()`` function from django's standard manager class will return ``True`` for
the ``created`` variable if the object was soft-deleted and is now "revived".

By default, the field that indicates a database entry is soft-deleted is ``deleted``, however, you can override the field name
using the ``SAFE_DELETE_FIELD_NAME`` setting.

Documentation
-------------

The documentation is available `here <http://django-safedelete.readthedocs.org>`_. Generate your own documentation using:

    tox -e docs


Licensing
---------

Please see the LICENSE file.

Contacts
--------

Please see the AUTHORS file.

.. image:: https://drupal.org/files/imagecache/grid-3/Logo_slogan_300dpi.png
    :target: http://www.makina-corpus.com


=========
CHANGELOG
=========

1.4.1 (2025-03-05)
==================

- Adding check for nullity in related models delete #243

1.4.0 (2024-05-13)
==================

- Support Python 3.11 / Django 5.0
- Drop support for Django 3.2 / 4.0 / 4.1

1.3.3 (2023-11-13)
==================

- Add Hard Delete Action to SafeDeleteAdmin #236 

1.3.2 (2023-04-05)
==================

- Fix undelete count in admin #220
- Fix de translation
- Fix argument type passed to db_for_write #222
- Django 4.2 support #223
- Fix SafeDeleteQueryset.as_manager() now returns SafeDeleteManager

1.3.1 (2022-10-03)
==================

- Fix exclude exception #216

1.3.0 (2022-08-17)
==================

- Add typings #213
- Drop support for Django < 3.2 and Python 3.6
- Add an index for deleted field #153

1.2.3 (2022-08-17)
==================

- Implement NO_DELETE and HARD_DELETE for queries #209
- Soft-delete-cascade raises an exception when trying to delete a object that related object is PROTECT #210

1.2.2 (2022-05-03)
==================

- Fix soft delete cascade for generic relations #207
- Add count return to delete and undelete methods #204

1.2.1 (2022-04-29)
==================

- Fix related_objects for non-safedelete models.

1.2.0 (2022-04-26)
==================

- Adding SafeDeleteCascadeControlModel #197
- Primary key uniqueness not considered in update_or_create #200

1.1.2 (2021-12-16)
==================

- Add django 4.0 and python 3.10 compatibility and tests #191
- Fix highlight_deleted_field translations #183 

1.1.1 (2021-11-15)
==================

- Add highlight_deleted_field #177
- Add new safedelete admin filter #175
- Bug: has_unique_fields() returns false for models.UniqueConstraint #179

1.1.0 (2021-09-23)
==================

- Fix attribute error when union queryset of safe delete model with others #152 
- SET related objects after deleting #156
- Avoiding recursive call for delete function. #121
- Add SAFE_DELETE_FIELD_NAME setting #164
- Move filter visibility logic to Query to fix subquery in annotations #160
- Add functions to customize the policies delete behavior #167


1.0.0 (2021-02-15)
==================

- Drop support for Django < 2.2
- Fix deprecation warnings for Django 4.0

0.5.7 (2021-01-28)
==================

- Add visibility filter for union operations #145
- Set related fields when soft deleted #144
- has_unique_fields() now takes into account unique_together option #147
- Fix filtering in subqueries #148

0.5.6 (2020-07-20)
==================

- call self.save() instead of super in delete()

0.5.5 (2020-05-14)
==================

- Admin action now warns if some objects weren't undeleted
- Remove some pre-Django 1.11 support

0.5.4 (2019-12-02)
==================

- Django 3.0 compatibility

0.5.3 (2019-12-02)
==================

- Fix "Doing a .get on a queryset adds deleted objects #131"

0.5.2 (2019-08-19)
==================

- Fix performance issue with Django 2.2
- Fix executing soft delete on already soft-deleted items during cascade soft delete

0.5.1 (2018-07-02)
==================

- Fix possible unicode error in admin

0.5.0 (2018-05-29)
==================

- Remove support for Django 1.8 to 1.10 and Python 3.3.
     (it should still works for now but isn't tested, use at your own risks).
- Fix update_or_create (#98)

0.4.5 (2018-01-31)
==================

- Fix an issue with Django 1.8 and `values_list`
- Django 2.0 compatibility


0.4.4 (2018-01-09)
==================

** Bugfixes **

- Fix latest and earliest

0.4.3
=====

** Bugfixes **

- Set SafeDeleteMixin as abstract


0.4.2
=====

** Bugfixes **

- iterator() now filter the deleted objects correctly.
- Fix prefetch_related() with all()
- Fix: "Cannot filter a query once a slice has been taken" error.

** Refactoring **

- Resolve Django 1.9+ allow_tags deprecation warning
- Fix docstring typo in SafeDeleteManager, SOFT_DELETE should be DELETED_INVISIBLE


0.4.1
=====

** New **

- Filtering on the deleted field is done on evaluation.
- Added specific managers: all_objects and deleted_objects.
- Added 'force_policy' feature to SafeDeleteQuerySet.

** Removed **

-

** Bugfixes **

- Fixed abstract intermediate models not working with SOFT_DELETE_CASCADE

** Refactoring **

- Renamed SafeDeleteMixin to SafeDeleteModel to better reflect the intended behavior. Using SafeDeleteMixin now throws a DeprecationWarning.
- Moved SafeDeleteQueryset


0.4.0
=====

** New **

- ``deleted`` is now a datetime.

** Refactoring **

- Globals (HARD_DELETE, SOFT_DELETE, ...) have been moved `to config.py`.
- Removed support for Django 1.4 to 1.7. You should use the 0.3.x branch if you need to use safedelete in Django <= 1.7.
- Remove factories to use mixins instead.

0.3.5
=====

** New **

- Change ``DELETED_VISIBLE_BY_PK`` to ``DELETED_VISIBLE_BY_FIELD`` to be able to change the field used.

0.3.4
=====

** New **

- Add a ``SOFT_DELETE_CASCADE`` policy which perform a SOFT_DELETE on safedelete related objects
- Django 1.8 compatibility

0.3.2
=====

- Prevent migration errors on django 1.8 by declaring the SafeDeleteManager (internal class in managers) as global

0.3.1
=====

- Fix issue with release on pypi not being the good one


0.3.0
=====

** New **

- Add a ``NO_DELETE`` policy which never soft or hard deletes an instance
- Django 1.8 compatibility

** Removed **

- Support of Django 1.2 and Django 1.3 has been removed

** Bugfixes **

- ``all_with_deleted`` method doesn't lose current queryset on a related manager
- uniqueness is now checked against soft deleted instances too
- prefetch_related() now works with SafedeleteQuerySet
- Fix the undelete action in the administration for active objects.


0.2.1 (2014-12-15)
==================

** New **

- Extends Django compatibility to Django 1.2


0.2.0 (2014-12-09)
==================

** New **

- Django compatilibty 1.3 => 1.7
- Add administration utilities
