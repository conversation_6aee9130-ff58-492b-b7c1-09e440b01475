import React from "react";
import { Select, Checkbox, Typography, Card } from "antd";
import { useDeviceType, useIsMobile } from "../../../hooks/useBreakpoint";
import { getResponsiveClasses, responsiveClasses } from "../../../utils/responsive";

const { Title } = Typography;

interface CreateOrderPaymentMethodProps {
  paymentMethod: "cod" | "cash" | "bank_transfer";
  paymentStatus: "paid" | "unpaid";
  companyPaymentReceived: boolean;
  onPaymentMethodChange: (value: "cod" | "cash" | "bank_transfer") => void;
  onPaymentStatusChange: (value: "paid" | "unpaid") => void;
  onCompanyPaymentReceivedChange: (value: boolean) => void;
}

export default function CreateOrderPaymentMethod({
  paymentMethod,
  paymentStatus,
  companyPaymentReceived,
  onPaymentMethodChange,
  onPaymentStatusChange,
  onCompanyPaymentReceivedChange,
}: CreateOrderPaymentMethodProps) {
  const deviceType = useDeviceType();
  const isMobile = useIsMobile();

  return (
    <Card className={getResponsiveClasses(responsiveClasses.card, deviceType)} style={{ marginTop: isMobile ? "16px" : "32px" }}>
      <Title level={isMobile ? 5 : 4}>Thông tin thanh toán</Title>
      <div className={getResponsiveClasses(responsiveClasses.grid.payment, deviceType)}>
        <div>
          <Typography.Text
            strong
            style={{ display: "block", marginBottom: "8px" }}
          >
            Phương thức thanh toán
          </Typography.Text>
          <Select
            value={paymentMethod}
            onChange={onPaymentMethodChange}
            style={{ width: "100%" }}
            className={getResponsiveClasses(responsiveClasses.input, deviceType)}
            options={[
              { value: "cod", label: "COD" },
              { value: "cash", label: "Tiền mặt" },
              { value: "bank_transfer", label: "Chuyển khoản" },
            ]}
          />
        </div>
        <div>
          <Typography.Text
            strong
            style={{ display: "block", marginBottom: "8px" }}
          >
            Trạng thái thanh toán
          </Typography.Text>
          <Select
            value={paymentStatus}
            onChange={onPaymentStatusChange}
            style={{ width: "100%" }}
            className={getResponsiveClasses(responsiveClasses.input, deviceType)}
            options={[
              { value: "paid", label: "Đã thanh toán" },
              { value: "unpaid", label: "Chưa thanh toán" },
            ]}
          />
        </div>
      </div>

      <div style={{ marginTop: "16px" }}>
        <Checkbox
          checked={companyPaymentReceived}
          onChange={(e) => onCompanyPaymentReceivedChange(e.target.checked)}
        >
          Công ty đã nhận được thanh toán
        </Checkbox>
      </div>
    </Card>
  );
}
