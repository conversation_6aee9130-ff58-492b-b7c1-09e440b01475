import React, { useState, useEffect, useCallback } from "react";
import { Select, Table, DatePicker, Tabs } from "antd";
import { Banknote, BanknoteIcon } from "lucide-react";
import { apiCall, endpoints } from "@/lib/api"; // <PERSON><PERSON><PERSON>nh import đúng
import moment from "moment"; // <PERSON><PERSON>m bảo đã cài moment

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho từng mục trong data array
interface RevenueDataItem {
  month?: number;
  quarter?: number;
  year?: number;
  revenue: number;
  total_products_sold: number;
  total_orders: number;
}

// Kiểu dữ liệu cho response từ API
interface ApiResponse {
  type: FilterType;
  data: RevenueDataItem[];
}

// Kiểu dữ liệu đã chuyển đổi cho bảng
interface RevenueData {
  period: string;
  revenue: number;
  totalProducts: number;
  totalOrders: number;
}

// Kiểu dữ liệu cho row trong bảng với tổng
interface RevenueTableRow {
  key: string;
  metric: string;
  total: number;
  [period: string]: string | number; // Dynamic period columns
}

type FilterType = "month" | "quarter" | "year";

const RevenueReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"actual" | "all">("actual");
  const [filterType, setFilterType] = useState<FilterType>("month");
  const [year1, setYear1] = useState<string | undefined>(undefined);
  const [year2, setYear2] = useState<string | undefined>(undefined);

  // Separate data states for actual and all revenue
  const [actualData, setActualData] = useState<RevenueData[]>([]);
  const [allData, setAllData] = useState<RevenueData[]>([]);

  // Current data based on active tab
  const data = activeTab === "actual" ? actualData : allData;

  // Separate loading and error states
  const [actualLoading, setActualLoading] = useState<boolean>(false);
  const [allLoading, setAllLoading] = useState<boolean>(false);
  const [actualError, setActualError] = useState<string | null>(null);
  const [allError, setAllError] = useState<string | null>(null);

  // Current loading and error states based on active tab
  const loading = activeTab === "actual" ? actualLoading : allLoading;
  const error = activeTab === "actual" ? actualError : allError;

  // --- Logic Fetch Actual Revenue Data ---
  const fetchActualData = useCallback(async () => {
    // Validation is now handled in the useEffect
    setActualLoading(true);
    setActualError(null);
    let url = `${endpoints.reports.compareRevenue}?filter_by=${filterType}`;

    if (filterType === "year" && year1 && year2) {
      url += `&year_1=${year1}&year_2=${year2}`;
    }

    console.log("Fetching actual revenue data with URL:", url);

    try {
      // Sử dụng kiểu ApiResponse đã định nghĩa
      const response = await apiCall<ApiResponse>("GET", url);

      // Kiểm tra và chuyển đổi dữ liệu từ API
      if (response && Array.isArray(response.data)) {
        const transformedData: RevenueData[] = response.data.map((item) => {
          let period = "";
          if (item.month) period = `Tháng ${item.month}`;
          else if (item.quarter) period = `Quý ${item.quarter}`;
          else if (item.year) period = `Năm ${item.year}`;

          return {
            period,
            revenue: item.revenue,
            totalProducts: item.total_products_sold,
            totalOrders: item.total_orders || 0,
          };
        });

        setActualData(transformedData);
        console.log("Actual revenue data set successfully:", transformedData);
      } else {
        console.warn("Invalid API response structure or no data:", response);
        setActualData([]);
        setActualError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching actual revenue data:", err);
      setActualError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setActualData([]);
    } finally {
      setActualLoading(false);
    }
  }, [filterType, year1, year2]);

  // --- Logic Fetch All Revenue Data (including cancelled orders) ---
  const fetchAllData = useCallback(async () => {
    // Validation is now handled in the useEffect
    setAllLoading(true);
    setAllError(null);
    let url = `${endpoints.reports.compareRevenueAll}?filter_by=${filterType}`;

    if (filterType === "year" && year1 && year2) {
      url += `&year_1=${year1}&year_2=${year2}`;
    }

    console.log("Fetching all revenue data with URL:", url);

    try {
      const response = await apiCall<ApiResponse>("GET", url);

      if (response && Array.isArray(response.data)) {
        const transformedData: RevenueData[] = response.data.map((item) => {
          let period = "";
          if (item.month) period = `Tháng ${item.month}`;
          else if (item.quarter) period = `Quý ${item.quarter}`;
          else if (item.year) period = `Năm ${item.year}`;

          return {
            period,
            revenue: item.revenue,
            totalProducts: item.total_products_sold,
            totalOrders: item.total_orders || 0,
          };
        });

        setAllData(transformedData);
        console.log("All revenue data set successfully:", transformedData);
      } else {
        console.warn("Invalid API response structure or no data:", response);
        setAllData([]);
        setAllError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching all revenue data:", err);
      setAllError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setAllData([]);
    } finally {
      setAllLoading(false);
    }
  }, [filterType, year1, year2]);

  // --- useEffect để gọi fetch functions khi dependencies thay đổi ---
  useEffect(() => {
    // Only fetch data when filterType is valid and if it's "year", both years must be selected
    if (filterType === "year" && (!year1 || !year2)) {
      return;
    }

    // Fetch both types of data
    fetchActualData();
    fetchAllData();

    // Log for debugging
    console.log(
      `Fetching data with filter: ${filterType}, year1: ${year1}, year2: ${year2}`
    );
  }, [filterType, year1, year2, fetchActualData, fetchAllData]);

  // --- Handler cho việc thay đổi Filter ---
  const handleFilterChange = (value: FilterType) => {
    setFilterType(value);
    // Reset năm khi không còn filter theo năm
    if (value !== "year") {
      setYear1(undefined);
      setYear2(undefined);
    }
    // No need to call API here - the useEffect will handle it
  };

  // --- Handler cho DatePicker ---
  const handleYear1Change = (date: moment.Moment | null) => {
    setYear1(date ? date.format("YYYY") : undefined);
    // No need to call API here - the useEffect will handle it
  };

  const handleYear2Change = (date: moment.Moment | null) => {
    setYear2(date ? date.format("YYYY") : undefined);
    // No need to call API here - the useEffect will handle it
  };

  // --- Tính toán Max Revenue và Max Orders ---
  // Thêm kiểm tra data có phần tử không trước khi dùng Math.max
  const maxRevenue =
    data.length > 0 ? Math.max(...data.map((item) => item.revenue)) : 0;
  const maxOrders =
    data.length > 0 ? Math.max(...data.map((item) => item.totalOrders)) : 0;

  // --- Render Hàm Doanh Thu ---
  const renderRevenue = (revenue: number | undefined | null, isTotal: boolean = false) => {
    // Kiểm tra revenue có phải là số hợp lệ không
    if (typeof revenue !== "number" || isNaN(revenue)) {
      return <span>-</span>;
    }

    const formattedRevenue = new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      minimumFractionDigits: 0,
    }).format(revenue);

    const icon = revenue > 0 ? (
      <BanknoteIcon className="inline-block w-4 h-4 text-green-500" />
    ) : (
          // Hiển thị icon đỏ cho cả số 0 hoặc số âm
      <Banknote className="inline-block w-4 h-4 text-red-500" />
    );

    return (
      <span className={isTotal ? "font-semibold text-blue-600" : ""}>
        {formattedRevenue} {icon}
      </span>
    );
  };

  // --- Tạo Columns động cho Bảng ---
  // Memoize việc tạo columns nếu cần tối ưu hiệu năng với useMemo,
  // nhưng với số lượng cột nhỏ thì không quá cần thiết.
  const columns = [
    {
      title: "Chỉ số",
      dataIndex: "metric",
      key: "metric",
      fixed: "left" as const, // Fix cột đầu bên trái
      width: 200,
    },
    // Chỉ tạo cột khi có data
    ...(data.length > 0
      ? data.map((item) => ({
          title: item.period,
          dataIndex: item.period,
          key: item.period,
          align: "right" as const,
          width: 150, // Set width cố định cho mỗi cột period
          render: (value: any, record: RevenueTableRow) => {
            if (record.metric === "Doanh thu") {
              return renderRevenue(value);
            }
            if (
              (record.metric === "Tổng sản phẩm bán được" ||
                record.metric === "Tổng đơn hàng") &&
              typeof value === "number"
            ) {
              return new Intl.NumberFormat("vi-VN").format(value);
            }
            return value ?? "-";
          },
        }))
      : []),
    // Cột Tổng cố định bên phải
    {
      title: "Tổng",
      dataIndex: "total",
      key: "total",
      fixed: "right" as const, // Fix cột tổng bên phải
      width: 180,
      align: "right" as const,
      render: (value: number, record: RevenueTableRow) => {
        if (record.metric === "Doanh thu") {
          return renderRevenue(value, true); // isTotal = true for total column
        }
        if (
          (record.metric === "Tổng sản phẩm bán được" ||
            record.metric === "Tổng đơn hàng") &&
          typeof value === "number"
        ) {
          return (
            <span className="font-semibold text-blue-600">
              {new Intl.NumberFormat("vi-VN").format(value)}
            </span>
          );
        }
        return value ?? "-";
      },
    },
  ];

  // --- Tạo DataSource cho Bảng với cột Tổng ---
  const dataSource: RevenueTableRow[] =
    data.length > 0
      ? [
          {
            key: "revenue_row",
            metric: "Doanh thu",
            total: data.reduce((sum, item) => sum + item.revenue, 0), // Tính tổng doanh thu
            ...data.reduce((acc, item) => {
              acc[item.period] = item.revenue;
              return acc;
            }, {} as { [key: string]: number }),
          },
          {
            key: "orders_row",
            metric: "Tổng đơn hàng",
            total: data.reduce((sum, item) => sum + item.totalOrders, 0), // Tính tổng đơn hàng
            ...data.reduce((acc, item) => {
              acc[item.period] = item.totalOrders;
              return acc;
            }, {} as { [key: string]: number }),
          },
          {
            key: "products_row",
            metric: "Tổng sản phẩm bán được",
            total: data.reduce((sum, item) => sum + item.totalProducts, 0), // Tính tổng sản phẩm
            ...data.reduce((acc, item) => {
              acc[item.period] = item.totalProducts;
              return acc;
            }, {} as { [key: string]: number }),
          },
        ]
      : [];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Báo cáo doanh thu</h1>

      {/* Tabs for switching between actual and all revenue */}
      <div className="mb-4">
        <Tabs
          activeKey={activeTab}
          onChange={(key) => {
            // Just update the tab state - no need to fetch data again
            setActiveTab(key as "actual" | "all");
          }}
          items={[
            {
              key: "actual",
              label: "Doanh thu thực tế",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị doanh thu thực tế
                </div>
              ),
            },
            {
              key: "all",
              label: "Doanh thu tổng",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị tổng doanh thu (bao gồm cả đơn hàng đã hủy và đang xử
                  lí)
                </div>
              ),
            },
          ]}
        />
      </div>

      {/* Phần Filter */}
      <div className="mb-6 flex flex-wrap gap-4 items-center">
        <Select
          style={{ width: 150 }} // Điều chỉnh width nếu cần
          value={filterType}
          onChange={handleFilterChange}
          options={[
            { label: "Theo tháng", value: "month" },
            { label: "Theo quý", value: "quarter" },
            { label: "Theo năm", value: "year" },
          ]}
        />
        {filterType === "year" && (
          <>
            <DatePicker
              picker="year"
              placeholder="Chọn năm 1"
              onChange={handleYear1Change}
              // value={year1 ? moment(year1, 'YYYY') : null} // Hiển thị giá trị đã chọn (cần cài moment)
              // disabledDate={(current) => year2 ? current && current.year() >= parseInt(year2) : false} // Ngăn chọn năm >= năm 2
            />
            <DatePicker
              picker="year"
              placeholder="Chọn năm 2"
              onChange={handleYear2Change}
              // value={year2 ? moment(year2, 'YYYY') : null} // Hiển thị giá trị đã chọn
              // disabledDate={(current) => year1 ? current && current.year() <= parseInt(year1) : false} // Ngăn chọn năm <= năm 1
            />
          </>
        )}
        {/* Hiển thị trạng thái loading hoặc lỗi */}
        {loading && <span className="ml-4">Đang tải dữ liệu...</span>}
        {error && <span className="ml-4 text-red-600">{error}</span>}
      </div>

      {/* Bảng Dữ liệu */}
      <div className="relative">
        {activeTab === "all" && (
          <div className="absolute right-0 top-0 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full z-10">
            Doanh thu tổng
          </div>
        )}
        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="key"
          loading={loading}
          scroll={{
            x: data.length > 0 ? 200 + (data.length * 150) + 180 : "max-content" // Tính toán width dựa trên số cột
          }}
          bordered
          pagination={false}
          rowClassName={(record) => {
            if (record.metric === "Doanh thu") {
              // Kiểm tra xem có giá trị nào trong record (ngoại trừ metric, key, total) bằng maxRevenue không
              const hasMaxRevenue = Object.entries(record)
                .filter(
                  ([key, value]) =>
                    key !== "metric" &&
                    key !== "key" &&
                    key !== "total" &&
                    typeof value === "number"
                )
                .some(
                  ([_, value]) =>
                    typeof value === "number" &&
                    typeof maxRevenue === "number" &&
                    value === maxRevenue &&
                    maxRevenue > 0
                );

              if (hasMaxRevenue) {
                return "bg-[#e6f7ff] font-semibold";
              }
            }

            if (record.metric === "Tổng đơn hàng") {
              // Kiểm tra xem có giá trị nào trong record (ngoại trừ metric, key, total) bằng maxOrders không
              const hasMaxOrders = Object.entries(record)
                .filter(
                  ([key, value]) =>
                    key !== "metric" &&
                    key !== "key" &&
                    key !== "total" &&
                    typeof value === "number"
                )
                .some(
                  ([_, value]) =>
                    typeof value === "number" &&
                    typeof maxOrders === "number" &&
                    value === maxOrders &&
                    maxOrders > 0
                );

              if (hasMaxOrders) {
                return "bg-[#f6ffed] font-semibold";
              }
            }

            return "";
          }}
        />
      </div>
    </div>
  );
};

export default RevenueReport;
