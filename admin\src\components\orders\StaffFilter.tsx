import { Select } from "antd";
import { Staff } from "../../types/staff";
import { UserIcon, TruckIcon } from "@heroicons/react/24/outline";
import { useLocation } from "react-router-dom";
import { SHIPPING_UNIT_OPTIONS } from "../../types/order";

interface StaffFilterProps {
  userRole: string;
  salesAdmin?: Staff | null;
  selectedSalesAdmins?: number[];
  deliveryStaff?: Staff | null;
  selectedDeliveryStaffs?: number[];
  shippingUnit?: string | null;
  selectedShippingUnits?: string[];
  onSalesAdminChange: (staffId: number | null) => void;
  onSalesAdminsChange?: (staffIds: number[]) => void;
  onDeliveryStaffChange: (staffId: number | null) => void;
  onDeliveryStaffsChange?: (staffIds: number[]) => void;
  onShippingUnitChange?: (unit: string | null) => void;
  onShippingUnitsChange?: (units: string[]) => void;
  staffList: Staff[];
  enableMultiSelect?: boolean;
}

export function StaffFilter({
  userRole,
  salesAdmin,
  selectedSalesAdmins,
  deliveryStaff,
  selectedDeliveryStaffs,
  shippingUnit,
  selectedShippingUnits,
  onSalesAdminChange,
  onSalesAdminsChange,
  onDeliveryStaffChange,
  onDeliveryStaffsChange,
  onShippingUnitChange,
  onShippingUnitsChange,
  staffList,
  enableMultiSelect = false,
}: StaffFilterProps) {
  const location = useLocation();
  const isCustomersPage = location.pathname.startsWith("/customers");
  const isDashboardPage = location.pathname === "/";
  const isProductRevenuePage = location.pathname === "/reports/products";

  const handleSalesAdminSelectChange = (value: number | number[] | null) => {
    if (enableMultiSelect && onSalesAdminsChange) {
      onSalesAdminsChange(Array.isArray(value) ? value : []);
    } else {
      onSalesAdminChange(Array.isArray(value) ? null : value);
    }
  };

  const handleDeliveryStaffSelectChange = (value: number | number[] | null) => {
    if (enableMultiSelect && onDeliveryStaffsChange) {
      onDeliveryStaffsChange(Array.isArray(value) ? value : []);
    } else {
      onDeliveryStaffChange(Array.isArray(value) ? null : value);
    }
  };

  const handleShippingUnitSelectChange = (value: string | string[] | null) => {
    if (enableMultiSelect && onShippingUnitsChange) {
      onShippingUnitsChange(Array.isArray(value) ? value : []);
    } else {
      onShippingUnitChange && onShippingUnitChange(Array.isArray(value) ? null : value);
    }
  };

  const salesStaffList = staffList.filter(
    (staff) => staff.role === "sales_admin" || staff.role === "sales_manager"
  );
  const deliveryStaffList = staffList.filter(
    (staff) => staff.role === "delivery_staff"
  );

  // Filter out the motorbike option from shipping unit options
  const filteredShippingUnitOptions = SHIPPING_UNIT_OPTIONS.filter(
    (option) => option.value !== "motorbike"
  );

  return (
    <div className="flex flex-wrap gap-4">
      {userRole !== "sales_admin" && (
        <div className="flex-1 min-w-[200px]">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
            <UserIcon className="h-4 w-4" />
            Nhân viên bán hàng
          </div>
          <Select
            mode={enableMultiSelect ? "multiple" : undefined}
            value={enableMultiSelect ? selectedSalesAdmins : (salesAdmin?.id || null)}
            onChange={handleSalesAdminSelectChange}
            className="w-full"
            size="large"
            placeholder={enableMultiSelect ? "Chọn nhân viên bán hàng" : "Tất cả"}
            allowClear={enableMultiSelect}
            options={enableMultiSelect ?
              salesStaffList.map((staff) => ({
                value: staff.id,
                label: `${staff.first_name} ${staff.last_name}${staff.role === "sales_manager" ? "(Quản lý)" : ""}`,
              })) :
              [
                { value: null, label: "Tất cả" },
                ...salesStaffList.map((staff) => ({
                  value: staff.id,
                  label: `${staff.first_name} ${staff.last_name}${staff.role === "sales_manager" ? "(Quản lý)" : ""}`,
                })),
              ]
            }
          />
        </div>
      )}

      {!isCustomersPage && !isDashboardPage && !isProductRevenuePage && (
        <>
          <div className="flex-1 min-w-[200px]">
            <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
              <UserIcon className="h-4 w-4" />
              Nhân viên giao hàng
            </div>
            <Select
              mode={enableMultiSelect ? "multiple" : undefined}
              value={enableMultiSelect ? selectedDeliveryStaffs : (deliveryStaff?.id || null)}
              onChange={handleDeliveryStaffSelectChange}
              className="w-full"
              size="large"
              placeholder={enableMultiSelect ? "Chọn nhân viên giao hàng" : "Tất cả"}
              allowClear={enableMultiSelect}
              options={enableMultiSelect ?
                deliveryStaffList.map((staff) => ({
                  value: staff.id,
                  label: `${staff.first_name} ${staff.last_name}`,
                })) :
                [
                  { value: null, label: "Tất cả" },
                  ...deliveryStaffList.map((staff) => ({
                    value: staff.id,
                    label: `${staff.first_name} ${staff.last_name}`,
                  })),
                ]
              }
            />
          </div>

          {(onShippingUnitChange || onShippingUnitsChange) && (
            <div className="flex-1 min-w-[200px]">
              <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                <TruckIcon className="h-4 w-4" />
                Phương thức vận chuyển
              </div>
              <Select
                mode={enableMultiSelect ? "multiple" : undefined}
                value={enableMultiSelect ? selectedShippingUnits : (shippingUnit || null)}
                onChange={handleShippingUnitSelectChange}
                className="w-full"
                size="large"
                placeholder={enableMultiSelect ? "Chọn phương thức vận chuyển" : "Tất cả"}
                allowClear={enableMultiSelect}
                options={enableMultiSelect ?
                  filteredShippingUnitOptions.map((option) => ({
                    value: option.value,
                    label: option.label,
                  })) :
                  [
                    { value: null, label: "Tất cả" },
                    ...filteredShippingUnitOptions.map((option) => ({
                      value: option.value,
                      label: option.label,
                    })),
                  ]
                }
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
