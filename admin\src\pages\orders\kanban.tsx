import { useState } from "react";
import { Order } from "../../types/order";
import { Spin, message } from "antd";
import { KanbanList } from "../../components/orders/KanbanList";
import { OrderSearch } from "../../components/orders/OrderSearch";
import { StaffFilter } from "../../components/orders/StaffFilter";
import { useKanbanOrders } from "../../hooks/useKanbanOrders";
import { useAuth } from "../../context/auth-hooks";
import {
  KanbanStatus,
  mapKanbanToApiStatus,
  countOrdersByKanbanStatus,
} from "../../components/orders/kanbanUtils";
import { DeliveryMethodModal } from "../../components/orders/DeliveryMethodModal";
import { PaymentMethodModal } from "../../components/orders/PaymentMethodModal";
import { CancellationReasonModal } from "../../components/orders/CancellationReasonModal";
import { apiCall, endpoints } from "../../lib/api";
import { useQueryClient } from "@tanstack/react-query";

export default function OrderKanbanPage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isDeliveryStaffModalOpen, setIsDeliveryStaffModalOpen] =
    useState(false);
  const [isShippingUnitModalOpen, setIsShippingUnitModalOpen] = useState(false);
  const [isPaymentMethodModalOpen, setIsPaymentMethodModalOpen] =
    useState(false);
  const [isCancellationReasonModalOpen, setIsCancellationReasonModalOpen] =
    useState(false);
  const [pendingOrderId, setPendingOrderId] = useState<number | null>(null);
  const [pendingStatus, setPendingStatus] = useState<Order["status"] | null>(
    null
  );

  const {
    searchParams,
    selectedSalesAdmin,
    selectedSalesAdmins,
    selectedDeliveryStaff,
    selectedDeliveryStaffs,
    selectedShippingUnit,
    selectedShippingUnits,
    orders,
    staffList,
    isLoading,
    handleSearch,
    handleStatusChange,
    handleSalesAdminChange,
    handleSalesAdminsChange,
    handleDeliveryStaffChange,
    handleDeliveryStaffsChange,
    handleShippingUnitChange,
    handleShippingUnitsChange,
    updateOrderStatus,
    updateDeliveryMethod,
    updatePaymentMethod,
  } = useKanbanOrders({
    userId: user?.id,
    userRole: user?.role,
  });

  // Convert API status counts to Kanban status counts
  const kanbanStatusCounts = countOrdersByKanbanStatus(orders);

  const onDragEnd = async (result: any) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const orderId = parseInt(draggableId);
    const newKanbanStatus = destination.droppableId as KanbanStatus;
    const order = orders.find((o) => o.id === orderId);

    if (!order) return;

    // Handle special cases for shipped statuses
    if (newKanbanStatus === "shipped_staff") {
      // Always show delivery staff modal when dragging to shipped_staff
      setPendingOrderId(orderId);
      setPendingStatus("shipped");
      setIsDeliveryStaffModalOpen(true);
      return;
    } else if (newKanbanStatus === "shipped_other") {
      // Always show shipping unit modal when dragging to shipped_other
      setPendingOrderId(orderId);
      setPendingStatus("shipped");
      setIsShippingUnitModalOpen(true);
      return;
    } else if (newKanbanStatus === "delivered") {
      // When moving to delivered status, show payment method modal
      setPendingOrderId(orderId);
      setPendingStatus("delivered");
      setIsPaymentMethodModalOpen(true);
      return;
    } else if (newKanbanStatus === "cancelled") {
      // When moving to cancelled status, show cancellation reason modal
      setPendingOrderId(orderId);
      setPendingStatus("cancelled");
      setIsCancellationReasonModalOpen(true);
      return;
    }

    // For other cases, just update the status
    const apiStatus = mapKanbanToApiStatus(newKanbanStatus);
    // Include the notes field if it exists
    const additionalParams = order.notes ? { notes: order.notes } : undefined;
    await updateOrderStatus(orderId, apiStatus, additionalParams);
  };

  const handleDeliveryMethodConfirm = async (params: {
    delivery_staff_id?: number | null;
    shipping_unit?: string;
  }) => {
    if (!pendingOrderId || !pendingStatus) return;

    // Find the order to get its notes
    const order = orders.find((o) => o.id === pendingOrderId);
    if (!order) return;

    // Validate that either delivery_staff_id or shipping_unit is provided
    if (
      (params.delivery_staff_id === undefined ||
        params.delivery_staff_id === null) &&
      (!params.shipping_unit || params.shipping_unit === "")
    ) {
      message.error("Vui lòng chọn nhân viên giao hàng hoặc đơn vị vận chuyển");
      return;
    }

    // First update delivery method, including notes if they exist
    const deliveryParams = {
      ...params,
      notes: order.notes || undefined,
    };
    await updateDeliveryMethod(pendingOrderId, deliveryParams);

    // Then update status, including notes if they exist
    const additionalParams = order.notes ? { notes: order.notes } : undefined;
    await updateOrderStatus(pendingOrderId, pendingStatus, additionalParams);

    // Reset pending state
    setPendingOrderId(null);
    setPendingStatus(null);

    // Close the modals
    setIsDeliveryStaffModalOpen(false);
    setIsShippingUnitModalOpen(false);
  };

  const handlePaymentMethodConfirm = async (params: {
    payment_method: "cod" | "cash" | "bank_transfer";
    payment_status: "paid" | "unpaid";
  }) => {
    if (!pendingOrderId || !pendingStatus) return;

    // Find the order to get its notes
    const order = orders.find((o) => o.id === pendingOrderId);
    if (!order) return;

    // First update payment method, including notes if they exist
    const paymentParams = {
      payment_status: params.payment_status,
      notes: order.notes || undefined,
    };
    await updatePaymentMethod(
      pendingOrderId,
      params.payment_method,
      paymentParams
    );

    // Then update status, including notes if they exist
    const statusParams = order.notes ? { notes: order.notes } : undefined;
    await updateOrderStatus(pendingOrderId, pendingStatus, statusParams);

    // Reset pending state
    setPendingOrderId(null);
    setPendingStatus(null);
    setIsPaymentMethodModalOpen(false);
  };

  const handleCancellationReasonConfirm = async (reason: string) => {
    if (!pendingOrderId || pendingStatus !== "cancelled") return;

    // Find the order to get its existing notes
    const order = orders.find((o) => o.id === pendingOrderId);
    if (!order) return;

    // Prepare the updated notes with the cancellation reason
    const existingNotes = order.notes || "";
    const cancellationNote = `Lý do huỷ: ${reason}`;
    const updatedNotes = existingNotes
      ? `${existingNotes}\n${cancellationNote}`
      : cancellationNote;

    try {
      // First update the notes using the regular update API
      await apiCall("PUT", endpoints.orders.update(pendingOrderId), {
        notes: updatedNotes,
        status: "cancelled",
      });

      // Invalidate queries to refresh the data
      await queryClient.invalidateQueries({ queryKey: ["kanban_orders"] });
      message.success("Huỷ đơn hàng thành công");
    } catch (error) {
      console.error("Failed to cancel order:", error);
      message.error("Huỷ đơn hàng thất bại");
    }

    // Reset pending state
    setPendingOrderId(null);
    setPendingStatus(null);
    setIsCancellationReasonModalOpen(false);
  };

  const handleStatusFilterChange = (status: string) => {
    // Convert Kanban status to API status for filtering
    if (status === "shipped_staff" || status === "shipped_other") {
      handleStatusChange("shipped");
    } else {
      handleStatusChange(status);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Kanban Đơn hàng</h1>
      </div>

      <div className="space-y-6">
        <OrderSearch onSearch={handleSearch} initialParams={{...searchParams, enableMultiSelect: true}} />

        <StaffFilter
          userRole={user?.role || ""}
          salesAdmin={selectedSalesAdmin}
          selectedSalesAdmins={selectedSalesAdmins}
          deliveryStaff={selectedDeliveryStaff}
          selectedDeliveryStaffs={selectedDeliveryStaffs}
          shippingUnit={selectedShippingUnit}
          selectedShippingUnits={selectedShippingUnits}
          onSalesAdminChange={handleSalesAdminChange}
          onSalesAdminsChange={handleSalesAdminsChange}
          onDeliveryStaffChange={handleDeliveryStaffChange}
          onDeliveryStaffsChange={handleDeliveryStaffsChange}
          onShippingUnitChange={handleShippingUnitChange}
          onShippingUnitsChange={handleShippingUnitsChange}
          staffList={staffList}
          enableMultiSelect={true}
        />
      </div>

      <div className="flex justify-end mb-4">
        <button
          onClick={() => handleStatusChange("")}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Hiện tất cả đơn hàng
        </button>
      </div>

      <Spin spinning={isLoading}>
        <KanbanList
          orders={orders}
          onDragEnd={onDragEnd}
          statusCounts={kanbanStatusCounts}
          onStatusClick={handleStatusFilterChange}
        />
      </Spin>

      {/* Delivery Staff Modal */}
      <DeliveryMethodModal
        isOpen={isDeliveryStaffModalOpen}
        onClose={() => {
          setIsDeliveryStaffModalOpen(false);
          setPendingOrderId(null);
          setPendingStatus(null);
        }}
        onConfirm={handleDeliveryMethodConfirm}
        staffList={staffList}
        type="staff"
        title="Chọn nhân viên giao hàng"
      />

      {/* Shipping Unit Modal */}
      <DeliveryMethodModal
        isOpen={isShippingUnitModalOpen}
        onClose={() => {
          setIsShippingUnitModalOpen(false);
          setPendingOrderId(null);
          setPendingStatus(null);
        }}
        onConfirm={handleDeliveryMethodConfirm}
        staffList={staffList}
        type="shipping_unit"
        title="Chọn đơn vị vận chuyển"
      />

      {/* Payment Method Modal */}
      <PaymentMethodModal
        isOpen={isPaymentMethodModalOpen}
        onClose={() => {
          setIsPaymentMethodModalOpen(false);
          setPendingOrderId(null);
          setPendingStatus(null);
        }}
        onConfirm={handlePaymentMethodConfirm}
        title="Cập nhật phương thức thanh toán"
      />

      {/* Cancellation Reason Modal */}
      <CancellationReasonModal
        isOpen={isCancellationReasonModalOpen}
        onClose={() => {
          setIsCancellationReasonModalOpen(false);
          setPendingOrderId(null);
          setPendingStatus(null);
        }}
        onConfirm={handleCancellationReasonConfirm}
        title="Lý do huỷ đơn hàng"
      />
    </div>
  );
}
