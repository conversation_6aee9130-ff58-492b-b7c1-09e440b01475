# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2015,2017,2020-2021
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Finnish (http://www.transifex.com/django/django/language/"
"fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "<PERSON><PERSON><PERSON>"

msgid "Home"
msgstr "Etusivu"

msgid "Documentation"
msgstr "Ohjeita"

msgid "Bookmarklets"
msgstr "Kirjanmerkkiset"

msgid "Documentation bookmarklets"
msgstr "Ohjeiden kirjanmerkkiset"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Asenna kirjanmerkkinen raahaamalla linkki kirjanmerkkien työkalupalkkiin tai "
"napsauttamalla linkkiä oikeanpuoleisella hiiren painikkeella ja valitsemalla "
"kirjanmerkkeihin lisäämisen. Sen jälkeen voit valita kirjanmerkkisen miltä "
"tahansa sivuston sivulta."

msgid "Documentation for this page"
msgstr "Tämän sivun ohjeita"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Näyttää ohjeet, jotka koskevat ko. sivun luonutta näkymää."

msgid "Tags"
msgstr "Tagit"

msgid "List of all the template tags and their functions."
msgstr "Lista kaikista mallinetunnisteista ja niiden toiminnasta."

msgid "Filters"
msgstr "Suodattimet"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Suotimet ovat toimintoja, jotka käsittelevät mallineessa käytettävän "
"muuttujan ulostuloa."

msgid "Models"
msgstr "Mallit"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Mallit kuvaavat järjestelmän kaikkia objekteja ja niihin liittyviä kenttiä. "
"Kenttiä pystyy käyttämään mallineista mallinemuuttujina."

msgid "Views"
msgstr "Näkymät"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Jokainen sivuston sivu on näkymän luoma. Näkymä määrittelee mitä mallinetta "
"sivun luontiin käytetään ja mitä objekteja mallineella on käytössä."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Selaintyökaluja, joilla pääset nopeasti hallinnointitoimintoihin."

msgid "Please install docutils"
msgstr "Ole hyvä ja asenna docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Hallinnan dokumentaatio vaatii Pythonin <a href=\"%(link)s\">docutils</a>-"
"kirjaston."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Pyydä ylläpitäjiä asentamaan <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Malli: %(name)s"

msgid "Fields"
msgstr "Kentät"

msgid "Field"
msgstr "Kenttä"

msgid "Type"
msgstr "Tyyppi"

msgid "Description"
msgstr "Selite"

msgid "Methods with arguments"
msgstr "Metodit argumentteineen"

msgid "Method"
msgstr "Metodi"

msgid "Arguments"
msgstr "Argumentit"

msgid "Back to Model documentation"
msgstr "Takaisin mallidokumentaatioon"

msgid "Model documentation"
msgstr "Mallidokumentaatio"

msgid "Model groups"
msgstr "Malliryhmät"

msgid "Templates"
msgstr "Mallineet"

#, python-format
msgid "Template: %(name)s"
msgstr "Malline: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Malline: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Mallineen <q>%(name)s</q> hakupolku"

msgid "(does not exist)"
msgstr "(ei ole olemassa)"

msgid "Back to Documentation"
msgstr "Takaisin dokumentaatioon"

msgid "Template filters"
msgstr "Mallinesuotimet"

msgid "Template filter documentation"
msgstr "Mallinesuodindokumentaatio"

msgid "Built-in filters"
msgstr "Sisäänrakennetut suotimet"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Käyttääksesi näitä suotimia, sisällytä <code>%(code)s</code> mallineesi "
"alkuun."

msgid "Template tags"
msgstr "Mallinetunnisteet"

msgid "Template tag documentation"
msgstr "Mallinetunnistedokumentaatio"

msgid "Built-in tags"
msgstr "Sisäänrakennetut tunnisteet"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Käyttääksesi näitä tunnisteita, sisällytä <code>%(code)s</code> mallineesi "
"alkuun."

#, python-format
msgid "View: %(name)s"
msgstr "Näkymä: %(name)s"

msgid "Context:"
msgstr "Konteksti:"

msgid "Templates:"
msgstr "Mallineet:"

msgid "Back to View documentation"
msgstr "Takaisin näkymädokumentaatioon"

msgid "View documentation"
msgstr "Näkymädokumentaatio"

msgid "Jump to namespace"
msgstr "Siirry nimiavaruuteen"

msgid "Empty namespace"
msgstr "Tyhjä nimiavaruus"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Näkymät nimiavaruuden %(name)s mukaan"

msgid "Views by empty namespace"
msgstr "Näkymät tyhjän nimiavaruuden mukaan"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Näkymäfunktio: <code>%(full_name)s</code>. Nimi: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tagi:"

msgid "filter:"
msgstr "suodatin:"

msgid "view:"
msgstr "näkymä:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Sovellusta %(app_label)r ei löydy"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Sovelluksesta %(app_label)r ei löydy mallia %(model_name)r"

msgid "model:"
msgstr "malli:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "tähän liittyvä `%(app_label)s.%(data_type)s`-kohde"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "tähän liittyvät `%(app_label)s.%(object_name)s`-kohteet"

#, python-format
msgid "all %s"
msgstr "kaikki %s"

#, python-format
msgid "number of %s"
msgstr "%s-kohteiden lukumäärä"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ei näytä olevan urlpattern-olio"
