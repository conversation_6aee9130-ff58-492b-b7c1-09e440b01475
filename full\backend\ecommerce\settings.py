"""
Django settings for ecommerce project.
"""

from pathlib import Path
from datetime import timedelta
import os
from dotenv import load_dotenv
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

# Load environment variables
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'False') == 'True'

# Sentry Initialization
# Chỉ khởi tạo Sentry nếu có biến môi trường ENABLE_SENTRY
ENABLE_SENTRY = os.getenv('ENABLE_SENTRY', 'True') == 'True'

# Kiểm tra xem có phải localhost hay không
def is_localhost():
    import socket
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    return local_ip.startswith('127.') or hostname.lower() in ['localhost', 'desktop', 'laptop']

# Danh sách các lỗi cần bỏ qua
IGNORED_ERRORS = [
    # Lỗi từ thư viện bên thứ ba
    "django.core.exceptions.DisallowedHost",
    "django.db.utils.OperationalError",
    # Lỗi từ client
    "django.http.Http404",
    "django.core.exceptions.ValidationError",
    # Lỗi từ Discord và Email
    "Failed to send email",
    "Discord response status: 400",
    "Lỗi khi gửi tin nhắn Discord",
    # Lỗi từ Zoho Mail
    "Unusual sending activity detected",
    "5.4.6",
    "placeholder.com",
]

# Hàm để lọc các sự kiện Sentry
def filter_sentry_events(event, hint):
    # Sử dụng tham số hint để kiểm tra ngoại lệ gốc
    if 'exc_info' in hint:
        exc_type, exc_value, _ = hint['exc_info']

        # Kiểm tra loại ngoại lệ
        if exc_type.__name__ in ['Http404', 'ValidationError', 'PermissionDenied']:
            return None

        # Kiểm tra thông báo lỗi
        exc_message = str(exc_value)
        for ignored_error in IGNORED_ERRORS:
            if ignored_error in exc_message:
                return None

    # Bỏ qua các sự kiện từ localhost
    if is_localhost(): # Tạm thời vô hiệu hóa để test localhost
        return None

    # Kiểm tra xem có phải là lỗi cần bỏ qua không
    if 'exception' in event and 'values' in event['exception'] and event['exception']['values']:
        error_value = event['exception']['values'][0].get('value', '')
        for ignored_error in IGNORED_ERRORS:
            if ignored_error in error_value:
                return None

    # Kiểm tra xem có phải là lỗi từ các request không quan trọng không
    if 'request' in event and 'url' in event['request']:
        url = event['request']['url']
        # Bỏ qua các request đến các endpoint không quan trọng
        if '/admin/jsi18n/' in url or '/static/' in url or '/media/' in url:
            return None

    # Kiểm tra mức độ nghiêm trọng
    if event.get('level') == 'warning' and DEBUG:
        # Bỏ qua cảnh báo trong môi trường phát triển
        return None

    return event

if ENABLE_SENTRY:
    print("DEBUG: ENABLE_SENTRY is True. Initializing Sentry...")
    sentry_sdk.init(
        dsn="https://<EMAIL>/4504790049685504",
        integrations=[DjangoIntegration()],
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=1.0 if not DEBUG else 0.1,  # Giảm tỷ lệ lấy mẫu trong môi trường phát triển
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
        # Set environment based on DEBUG status
        environment="production" if not DEBUG else "development",
        # Enable tracing for performance monitoring
        enable_tracing=True,
        # Sử dụng hàm lọc sự kiện
        before_send=filter_sentry_events,
    )
    print("DEBUG: Sentry initialized.")
else:
    print("DEBUG: ENABLE_SENTRY is False. Sentry not initialized. Using mock capture_exception.")
    # Tạo một hàm giả để xử lý khi Sentry không được khởi tạo
    def capture_exception(exc):
        import logging
        logging.error(f"[DEV MODE - NOT SENT TO SENTRY] Exception: {exc}")

    # Gán hàm giả vào sentry_sdk để tránh lỗi khi gọi sentry_sdk.capture_exception
    sentry_sdk.capture_exception = capture_exception

ALLOWED_HOSTS = [
    'api.nguyenlieuphache3t.vn',
    'localhost',
    '127.0.0.1',
    'api.nguyenlieu3t.xyz'
]

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',

    # Third-party
    'rest_framework',
    'rest_framework.authtoken',
    'corsheaders',
    'rest_framework_simplejwt',
    'django_filters',
    'drf_yasg',
    'safedelete',
    'explorer',

    # Local
    'store',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
]

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.security.csrf': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
        'django.request': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
        # Add root logger configuration to see INFO messages from apps
        '': { # Empty string '' configures the root logger
            'handlers': ['console'],
            'level': 'DEBUG', # Show DEBUG and higher (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        },
    },
}

ROOT_URLCONF = 'ecommerce.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'store' / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ecommerce.wsgi.application'

# Database configuration
DATABASE_URL = os.getenv('DATABASE_URL')
if DATABASE_URL:
    from urllib.parse import urlparse
    db_url = urlparse(DATABASE_URL)
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': db_url.path[1:],
            'USER': db_url.username,
            'PASSWORD': db_url.password,
            'HOST': db_url.hostname,
            'PORT': db_url.port or '5432',
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('DB_NAME'),
            'USER': os.getenv('DB_USER'),
            'PASSWORD': os.getenv('DB_PASSWORD'),
            'HOST': os.getenv('DB_HOST'),
            'PORT': os.getenv('DB_PORT'),
            'OPTIONS': {
                'options': '-c search_path=public'
            }
        }
    }

# PostgreSQL extensions
# This will enable the unaccent extension needed for accent-insensitive searching
DATABASE_EXTENSIONS = ['unaccent']

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Asia/Ho_Chi_Minh'
USE_I18N = True
USE_TZ = True

# Static and media files
STATIC_URL = '/static/'
STATIC_ROOT = '/app/static'
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
STATICFILES_DIRS = []

MEDIA_URL = '/media/'
MEDIA_ROOT = '/app/media'


# Use environment-specific static and media roots
if os.getenv('ENVIRONMENT') == 'LOCAL' or (os.getenv('ENVIRONMENT') is None and DEBUG):
    # Local development environment
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
    MEDIA_ROOT = os.path.join(BASE_DIR, 'mediafiles')
else:
    # Docker/production environment
    STATIC_ROOT = '/app/static'
    MEDIA_ROOT = '/app/media'


# CORS and Security settings
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS']
CORS_ALLOW_HEADERS = ['*']

# Security settings
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
USE_X_FORWARDED_HOST = True

# CSRF settings
CSRF_TRUSTED_ORIGINS = [
    'http://api.nguyenlieuphache3t.vn',
    'https://api.nguyenlieuphache3t.vn',
    'http://nguyenlieuphache3t.vn',
    'https://nguyenlieuphache3t.vn',
    'http://localhost:8000',
    'http://127.0.0.1:8000'
]

# Only set cookie domain in production
if not DEBUG:
    CSRF_COOKIE_DOMAIN = '.nguyenlieuphache3t.vn'
    SESSION_COOKIE_DOMAIN = '.nguyenlieuphache3t.vn'

CSRF_USE_SESSIONS = True

# DRF settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser'
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 12
}

# JWT settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=365),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=365),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': False,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': os.getenv('JWT_SECRET_KEY', SECRET_KEY),
    'AUTH_HEADER_TYPES': ('Bearer',),
}

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Email settings for password reset
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST','smtp.zoho.com') # 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')

FRONTEND_URL = os.getenv('FRONTEND_URL','http://localhost:3000')  # Update for production

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', 'default') # Thay bằng token thật hoặc lấy từ env
DISCORD_WEBHOOK_URL = os.environ.get('DISCORD_WEBHOOK_URL', 'default')

DISCORD_NOTIFICATION_CHATS = {
    'pending': os.getenv(
        'DISCORD_WEBHOOK_URL_PENDING','https://discord.com/api/webhooks/1376386008551260170/k-bp2OltQVyLB3_42zvETvzawVIb6-pfXYI2QfSUbQotODco897Lm_7T6XTJATMWLw57'#
    ),
    'processing': os.getenv(
        'DISCORD_WEBHOOK_URL_PROCESSING','https://discord.com/api/webhooks/1376386195097124884/67MNQFsVcLDyPRGZkJ02HWBfM7XFI-TjHAHN1L6g8ZFwVCsocQgShu8WRQbr_tqMEYey'#
    ),
    'shipped': os.getenv(
        'DISCORD_WEBHOOK_URL_SHIPPED','https://discord.com/api/webhooks/1376386285832372374/7Um0Qazy17vSjfa1EPT15z8pgp_klAzoxKK_bveWaxTRTqXp7EI53NrEhrZqNieMj4Mu'#
    ),
    'shipped_other': os.getenv(
        'DISCORD_WEBHOOK_URL_SHIPPED_OTHER','https://discord.com/api/webhooks/1376386421274837062/u-qhWGDzbsclIl2m5pPUD4pKeTRl0ih-s9IC4nNc5DDKiRIJGi_-koYFAE1DvQtxuN6M'#
    ),
    'delivered': os.getenv(
        'DISCORD_WEBHOOK_URL_DELIVERED','default'#
    ),
    'cancelled': os.getenv(
        'DISCORD_WEBHOOK_URL_CANCELLED','https://discord.com/api/webhooks/1376386558390833172/yzAUIYo4-9QC4gFgTaB-PVu0Yj2NqGJrCXgkXzK8FjsY26U467LMtR5ee0xfDZmMnLNG'#
    ),
    'returned': os.getenv(
        'DISCORD_WEBHOOK_URL_RETURNED','https://discord.com/api/webhooks/1376386558390833172/yzAUIYo4-9QC4gFgTaB-PVu0Yj2NqGJrCXgkXzK8FjsY26U467LMtR5ee0xfDZmMnLNG'#
    ),
    'showroom': os.getenv(
        'DISCORD_WEBHOOK_URL_SHOWROOM','https://discord.com/api/webhooks/1376386486856843405/8UB83QpZqijt5jmjsNAPgwNh-5CiYwYK03tUFP3l8FXO4Mp6K8zjuFA6RnH3S1rfSca9'#
    ),
    'default': os.getenv(
        'DISCORD_WEBHOOK_URL_DEFAULT','default'#
    ),
}


TELEGRAM_NOTIFICATION_CHATS = {
    'pending': os.getenv(
        'TELEGRAM_CHAT_ID_PENDING','-4760086931'#
    ),
    'processing': os.getenv(
        'TELEGRAM_CHAT_ID_PROCESSING','-4791214699'#
    ),
    'shipped': os.getenv(
        'TELEGRAM_CHAT_ID_SHIPPED','-4698222744'#
    ),
    'shipped_other': os.getenv(
        'TELEGRAM_CHAT_ID_SHIPPED_OTHER','-4659520622'#
    ),
    'delivered': os.getenv(
        'TELEGRAM_CHAT_ID_DELIVERED','-4611435086'#
    ),
    'cancelled': os.getenv(
        'TELEGRAM_CHAT_ID_CANCELLED','-4798204601'#
    ),
    'returned': os.getenv(
        'TELEGRAM_CHAT_ID_RETURNED','-4798204601'#
    ),
    'showroom': os.getenv(
        'TELEGRAM_CHAT_ID_SHOWROOM','-4760086931'
    ),
    'default': os.getenv(
        'TELEGRAM_CHAT_ID_DEFAULT','-4760086931'#
    ),
}


# Admin site URL configuration
ADMIN_SITE_BASE_URL = 'https://admin.nguyenlieuphache3t.vn'

# SQL Explorer settings
EXPLORER_CONNECTIONS = {'Default': 'default'}
EXPLORER_DEFAULT_CONNECTION = 'default'

# Security settings for SQL Explorer
EXPLORER_PERMISSION_VIEW = lambda r: r.user.is_staff
EXPLORER_PERMISSION_CHANGE = lambda r: r.user.is_superuser

# Optional: Limit query execution time (in seconds)
EXPLORER_QUERY_TIMEOUT_MS = 30000  # 30 seconds

# Optional: Enable/disable schema information
EXPLORER_SCHEMA_EXCLUDE_TABLE_PREFIXES = ['django_', 'auth_']

# Optional: Enable CSV downloads
EXPLORER_CSV_DELIMETER = ','

# Fix related_name conflicts with existing models
EXPLORER_USER_QUERY_VIEWS_RELATED_NAME = 'explorer_query_views'
EXPLORER_USER_QUERY_FAVORITES_RELATED_NAME = 'explorer_query_favorites'
