import { AxiosInstance } from "axios";
import { api, apiCall, endpoints } from "./api";

interface RefreshResponse {
  access: string;
}

let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

function subscribeTokenRefresh(cb: (token: string) => void) {
  refreshSubscribers.push(cb);
}

function onTokenRefreshed(token: string) {
  refreshSubscribers.forEach((cb) => cb(token));
  refreshSubscribers = [];
}

export function setupAuthInterceptor(axiosInstance: AxiosInstance) {
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      // If the error is not 401 or the request was for refreshing token, reject
      if (
        error.response?.status !== 401 ||
        originalRequest.url === endpoints.auth.refresh ||
        originalRequest.url === endpoints.auth.login
      ) {
        return Promise.reject(error);
      }

      if (!isRefreshing) {
        isRefreshing = true;

        try {
          const refreshToken = localStorage.getItem("refresh_token");
          if (!refreshToken) {
            throw new Error("No refresh token available");
          }

          const response = await apiCall<RefreshResponse>(
            "POST",
            endpoints.auth.refresh,
            {
              refresh: refreshToken,
            }
          );

          const { access: newAccessToken } = response;
          localStorage.setItem("token", newAccessToken);

          // Update the original request's Authorization header
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          // Notify all subscribers that token has been refreshed
          onTokenRefreshed(newAccessToken);
          isRefreshing = false;

          // Retry the original request
          return axiosInstance(originalRequest);
        } catch (refreshError) {
          // If refresh token fails, logout user
          localStorage.removeItem("token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("user");
          window.location.href = "/login";
          return Promise.reject(refreshError);
        }
      }

      // If refreshing is in progress, wait for the new token
      return new Promise((resolve) => {
        subscribeTokenRefresh((token: string) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          resolve(axiosInstance(originalRequest));
        });
      });
    }
  );
}

// Initialize the interceptor
setupAuthInterceptor(api);
