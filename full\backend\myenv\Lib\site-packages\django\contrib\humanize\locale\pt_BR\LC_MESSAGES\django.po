# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2014
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2012
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2023
# fa9e10542e458baef0599ae856e43651_13d2225, 2012-2013
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2024-08-07 18:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Portuguese (Brazil) (http://app.transifex.com/django/django/"
"language/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Humanize"
msgstr "Humanizar"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 3, e.g. 83rd, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}º"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}º"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s milhão"
msgstr[1] "%(value)s milhões"
msgstr[2] "%(value)s milhões"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s bilhão"
msgstr[1] "%(value)s bilhões"
msgstr[2] "%(value)s bilhões"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s trilhão"
msgstr[1] "%(value)s trilhões"
msgstr[2] "%(value)s trilhões"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s quadrilhão"
msgstr[1] "%(value)s quadrilhões"
msgstr[2] "%(value)s quadrilhões"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s quintilhão"
msgstr[1] "%(value)s quintilhões"
msgstr[2] "%(value)s quintilhões"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sextilhão"
msgstr[1] "%(value)s sextilhões"
msgstr[2] "%(value)s sextilhões"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septilhão"
msgstr[1] "%(value)s septilhões"
msgstr[2] "%(value)s septilhões"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s octilhão"
msgstr[1] "%(value)s octilhões"
msgstr[2] "%(value)s octilhões"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonilhão"
msgstr[1] "%(value)s nonilhões"
msgstr[2] "%(value)s nonilhões"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s decilhão"
msgstr[1] "%(value)s decilhões"
msgstr[2] "%(value)s decilhões"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"
msgstr[2] "%(value)s googol"

msgid "one"
msgstr "um"

msgid "two"
msgstr "dois"

msgid "three"
msgstr "três"

msgid "four"
msgstr "quatro"

msgid "five"
msgstr "cinco"

msgid "six"
msgstr "seis"

msgid "seven"
msgstr "sete"

msgid "eight"
msgstr "oito"

msgid "nine"
msgstr "nove"

msgid "today"
msgstr "hoje"

msgid "tomorrow"
msgstr "amanhã"

msgid "yesterday"
msgstr "ontem"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s atrás"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "uma hora atrás"
msgstr[1] "%(count)s horas atrás"
msgstr[2] "%(count)s horas atrás"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "um minuto atrás"
msgstr[1] "%(count)s minutos atrás"
msgstr[2] "%(count)s minutos atrás"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "um segundo atrás"
msgstr[1] "%(count)s segundos atrás"
msgstr[2] "%(count)s segundos atrás"

msgid "now"
msgstr "agora"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "um segundo a partir de agora"
msgstr[1] "%(count)s segundos a partir de agora"
msgstr[2] "%(count)s segundos a partir de agora"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "um minuto a partir de agora"
msgstr[1] "%(count)s minutos a partir de agora"
msgstr[2] "%(count)s minutos a partir de agora"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "uma hora a partir de agora"
msgstr[1] "%(count)s horas a partir de agora"
msgstr[2] "%(count)s horas a partir de agora"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s a partir de agora"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d ano"
msgstr[1] "%(num)d anos"
msgstr[2] "%(num)d anos"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mês"
msgstr[1] "%(num)d meses"
msgstr[2] "%(num)d meses"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d semana"
msgstr[1] "%(num)d semanas"
msgstr[2] "%(num)d semanas"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dia"
msgstr[1] "%(num)d dias"
msgstr[2] "%(num)d dias"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d hora"
msgstr[1] "%(num)d horas"
msgstr[2] "%(num)d horas"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuto"
msgstr[1] "%(num)d minutos"
msgstr[2] "%(num)d minutos"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d ano"
msgstr[1] "%(num)d anos"
msgstr[2] "%(num)d anos"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mês"
msgstr[1] "%(num)d meses"
msgstr[2] "%(num)d meses"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d semana"
msgstr[1] "%(num)d semanas"
msgstr[2] "%(num)d semanas"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dia"
msgstr[1] "%(num)d dias"
msgstr[2] "%(num)d dias"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d hora"
msgstr[1] "%(num)d horas"
msgstr[2] "%(num)d horas"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minuto"
msgstr[1] "%(num)d minutos"
msgstr[2] "%(num)d minutos"
