# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2024-10-07 18:32+0000\n"
"Last-Translator: Ain<PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Irish (http://app.transifex.com/django/django/language/ga/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ga\n"
"Plural-Forms: nplurals=5; plural=(n==1 ? 0 : n==2 ? 1 : n<7 ? 2 : n<11 ? 3 : "
"4);\n"

msgid "Redirects"
msgstr "Athsheolaidh"

msgid "site"
msgstr "suíomh"

msgid "redirect from"
msgstr "atreoraigh ó"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Ba cheart gur cosán iomlán é seo, gan an t-ainm fearainn a áireamh. Sampla: "
"\\u201c/events/search/\\u201d."

msgid "redirect to"
msgstr "atreoraigh go dtí"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Is féidir gur cosán iomlán é seo (mar atá thuas) nó URL iomlán ag tosú le "
"scéim mar \\u201chttps://\\u201d."

msgid "redirect"
msgstr "athsheol"

msgid "redirects"
msgstr "atreoraithe"
