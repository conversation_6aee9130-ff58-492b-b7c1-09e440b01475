import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Pagination, Space } from "antd";
import { Bar<PERSON>hartOutlined } from "@ant-design/icons";
import { Staff } from "../../types/staff";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiCall, endpoints } from "../../lib/api";
import { CustomerListItem } from "../../types/customer";
import {
  CustomerSearch,
  CustomerSearchParams,
} from "../../components/customers/CustomerSearch";
import { CustomerTable } from "../../components/customers/CustomerTable";
import { useToast } from "../../context/toast-hooks";
import { useAuth } from "../../context/auth-hooks";
import { StaffFilter } from "../../components/orders/StaffFilter";

export default function CustomersPage() {
  const [selectedSalesAdmin, setSelectedSalesAdmin] = useState<number | null>(
    null
  );
  const [selectedSalesAdmins, setSelectedSalesAdmins] = useState<number[]>([]);
  const [staffList, setStaffList] = useState<Staff[]>([]);
  const [page, setPage] = useState(1);
  const [searchParams, setSearchParams] = useState<CustomerSearchParams>({
    searchBy: "email",
  });

  const { user } = useAuth();

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  useEffect(() => {
    const fetchStaffList = async () => {
      try {
        const response = await apiCall<{ results: Staff[] }>(
          "GET",
          endpoints.staff.list + `?no_page=true`
        );
        setStaffList(response.results);
      } catch (error) {
        console.error("Failed to fetch staff list:", error);
      }
    };
    fetchStaffList();
  }, []);

  const { data, isLoading } = useQuery<{
    results: CustomerListItem[];
    count: number;
  }>({
    queryKey: ["customers", page, searchParams, selectedSalesAdmin, selectedSalesAdmins],
    queryFn: () => {
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: "10",
        needdetail: "true",
        role: "customer"
      });

      if (user?.role === "sales_admin") {
        params.append("creator_id", user.id.toString());
      } else if (selectedSalesAdmins.length > 0) {
        params.append("creator_id", selectedSalesAdmins.join(","));
      } else if (selectedSalesAdmin) {
        params.append("creator_id", selectedSalesAdmin.toString());
      }

      if (searchParams.query) {
        params.append("search", searchParams.query);
        params.append("search_by", searchParams.searchBy);
      }

      if (searchParams.dateFrom) {
        params.append("date_from", searchParams.dateFrom);
      }

      if (searchParams.dateTo) {
        params.append("date_to", searchParams.dateTo);
      }

      if (searchParams.status) {
        params.append("is_active", (searchParams.status === "active").toString());
      }

      if (searchParams.hasOrders) {
        params.append("has_orders", "true");
      }

      const url = `${endpoints.customers.list}?${params.toString()}`;
      return apiCall("GET", url);
    },
  });

  const toggleCustomerStatus = async (
    customerId: number,
    isActive: boolean
  ) => {
    try {
      await apiCall("PATCH", endpoints.customers.update(customerId), {
        is_active: !isActive,
      });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
      showToast(
        `Khách hàng đã được ${isActive ? "vô hiệu hóa" : "kích hoạt"}`,
        "success"
      );
    } catch (error) {
      console.error("Không thể cập nhật trạng thái khách hàng:", error);
      showToast("Không thể cập nhật trạng thái khách hàng", "error");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Khách hàng</h1>
        <Space>
          <Button
            size="large"
            icon={<BarChartOutlined />}
            onClick={() => navigate("/reports/top-customers")}
          >
            Báo cáo Top khách hàng
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={() => navigate("/customers/create")}
          >
            Tạo khách hàng
          </Button>
        </Space>
      </div>

      <CustomerSearch onSearch={setSearchParams} />

      {user?.role !== "sales_admin" && (
        <StaffFilter
          userRole={user?.role || ""}
          salesAdmin={staffList.find(
            (staff) => staff.id === selectedSalesAdmin
          )}
          selectedSalesAdmins={selectedSalesAdmins}
          onSalesAdminChange={setSelectedSalesAdmin}
          onSalesAdminsChange={setSelectedSalesAdmins}
          onDeliveryStaffChange={() => {}}
          staffList={staffList}
          enableMultiSelect={true}
        />
      )}

      <CustomerTable
        data={data?.results || []}
        loading={isLoading}
        onToggleStatus={toggleCustomerStatus}
      />

      {data && data.results.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          Không tìm thấy khách hàng phù hợp.
        </div>
      )}

      {data && data.results.length > 0 && (
        <Pagination
          align="center"
          current={page}
          total={data.count}
          pageSize={10}
          onChange={setPage}
          showSizeChanger={false}
        />
      )}
    </div>
  );
}
