# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2014
# Seraf<PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-06-03 18:27+0000\n"
"Last-Translator: Ser<PERSON><PERSON>fan<PERSON> <<EMAIL>>\n"
"Language-Team: Greek (http://www.transifex.com/django/django/language/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Ανακατευθύνσεις"

msgid "site"
msgstr "ιστότοπος"

msgid "redirect from"
msgstr "ανακατεύθυνση από"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Πρέπει να είναι ένα απόλυτο μονοπάτι χωρίς το όνομα τομέα. Για παράδειγμα \"/"
"events/search\"."

msgid "redirect to"
msgstr "ανακατεύθυνση προς"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Μπορεί να είναι είτε ένα απόλυτο μονοπάτι (όπως πιο πάνω) είτε ένα πλήρες "
"URL που ξεκινά με κατάλληλο σχημα πχ \"https://\"."

msgid "redirect"
msgstr "ανακατεύθυνση"

msgid "redirects"
msgstr "ανακατευθύνσεις"
