import React, { useState, useEffect, useCallback, ChangeEvent } from "react"; // Import useCallback and ChangeEvent
import { useNavigate } from "@tanstack/react-router";
import { useCart } from "@/context/CartContext";
import { payosService } from "@/services/payosService";
import { Voucher } from "@/lib/api/voucher/voucher";
import { BASE_SHIPPING_FEE } from "@/modules/checkout/utils";
import { VoucherSelect } from "@/modules/checkout/components/VoucherSelect";
import { orderService } from "@/services/orderService";
import { locationApi } from "@/lib/api/location/location";
import { getUserProfile, updateUserProfile } from "@/lib/api/user/user";
import { NavigationPath } from "@/routes/paths";
import {
  CustomerInfoForm,
  ShippingAddressForm,
  PaymentMethodForm,
  OrderSummaryComponent as OrderSummary,
  CheckoutFormData,
  validateForm,
  isFormValid,
  formatShippingAddress,
  formatDisplayAddress,
} from "@/modules/checkout";
import { getAccessToken } from "@/hooks/useAuth";
import { useAuth } from "@/context/AuthContext";
import { axiosInstance } from "@lib/api";
import { toast } from "react-toastify";

// --- Constants for Free Shipping Logic (Lowercase - Matching Backend & Extracted Format) ---
const INNER_CITY_DISTRICTS_HCM = [
  'quận 1', 'quận 2', 'quận 3', 'quận 4', 'quận 5', 'quận 6', 'quận 7', 'quận 8',
  'quận 10', 'quận 11',
  'quận tân phú',
  'quận tân bình',
  'quận phú nhuận',
  'quận bình thạnh',
  'quận gò vấp'
];
const SUBURBAN_1_DISTRICTS_HCM = [ // Define Suburban 1 districts matching backend (lowercase)
  'quận 9', 'quận 12', 'thành phố thủ đức', 'quận bình tân'
];
const HCMC_CITY_CODE = '79'; // Assuming '79' is the code for Ho Chi Minh City
const FREE_SHIPPING_THRESHOLD_INNER_CITY = 600000; // Threshold for inner city free ship (600k-2M range)
const FREE_SHIPPING_THRESHOLD_GLOBAL = 2000000; // Global free shipping threshold
// --- End Constants ---

interface ExtendedFormData extends CheckoutFormData {
  selectedVouchers: Voucher[];
}

const initialFormData: ExtendedFormData = {
  fullName: "",
  email: "",
  phone: "",
  address: "",
  city: "",
  district: "",
  ward: "",
  paymentMethod: "cash",
  note: "",
  selectedVouchers: [],
};

export default function CheckoutPage() {
  // const isAuthenticated = useAuthRedirect(); // not have to login when checkout
  // const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(!!getAccessToken());
  let isAuthenticated = !!getAccessToken(); // check if user is authenticated
  let isUnauthUser = false;
  const navigate = useNavigate();
  const { register, login } = useAuth();
  const { items, totalPrice, clearCart } = useCart();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFormattingAddress, setIsFormattingAddress] = useState(false);
  const [formData, setFormData] = useState<ExtendedFormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<CheckoutFormData>>({});
  const [apiError, setApiError] = useState<string | null>(null);
  const [selectedDistrictName, setSelectedDistrictName] = useState<string>("");
  const [shippingFee, setShippingFee] = useState<number | null>(null); // State for calculated shipping fee
  const [shippingDistance, setShippingDistance] = useState<number | null>(null); // Optional: state for distance
  const [shippingDuration, setShippingDuration] = useState<string | null>(null); // Optional: state for duration
  const [shippingError, setShippingError] = useState<string | null>(null); // State for shipping calculation error
  const [previousTotalPrice, setPreviousTotalPrice] = useState<number>(totalPrice); // Track previous total price
  const [isCartChanging, setIsCartChanging] = useState<boolean>(false); // Flag to indicate cart is being modified

  // Handler for shipping fee updates from ShippingAddressForm
  const handleShippingFeeUpdate = useCallback((feeResult: { fee: number | null; distance?: number; duration?: string; error?: string | null }) => {
    console.log("[CheckoutPage] Received shipping fee update from API:", feeResult); // Log received update

    let finalFee = feeResult.fee;
// (Line removed)

    // Extract only the district part and convert to lowercase - This might still be useful for other logic or logging
    // const districtPart = selectedDistrictName.split(',')[0] || "";
    // const districtNameLower = districtPart.toLowerCase().trim();

    // Log the fee received from the API
    console.log(`[CheckoutPage] Using fee directly from API: ${finalFee}. City=${formData.city}, District=${selectedDistrictName}, Total=${totalPrice}`);

    setShippingFee(finalFee);
    setShippingDistance(feeResult.distance ?? null);
    setShippingDuration(feeResult.duration ?? null);
    // Keep the original API error if it exists
    setShippingError(feeResult.error ?? null);

    // Clear general API error if shipping calculation succeeds (even if overridden) or resets
    if (!feeResult.error) {
      setApiError(null);
    } else {
      // Optionally set a general API error if shipping fails critically
      // setApiError(feeResult.error);
    }
  }, [formData.city, selectedDistrictName, totalPrice]); // Add dependencies

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const response = await getUserProfile();
        const user = response.success && response.user;
        if (!user) return;

        setFormData((prevData) => ({
          ...prevData,
          fullName: `${user.first_name} ${user.last_name}`.trim(),
          email: user.email,
          phone: user.phone_number || "",
          address: user.shipping_address || "",
          city: user.city || "",
          district: user.district || "",
          ward: user.ward || "",
        }));
      } catch (error) {
        console.error("Error fetching user profile:", error);
      }
    };

    fetchUserProfile();
  }, []);

  // Update district name when district changes
  useEffect(() => {
    const updateDistrictName = async () => {
      if (formData.district) {
        try {
          // Fetch the display name (which might be "Quận X, Thành phố Y")
          const districtName = await locationApi.getLocationName(formData.district);
          setSelectedDistrictName(districtName); // Store the full name
        } catch (error) {
          console.error("Error fetching district name:", error);
        }
      } else {
        setSelectedDistrictName("");
      }
    };
    updateDistrictName();
  }, [formData.district]);

  // Effect to clear vouchers when cart changes (quantity/item changes)
  useEffect(() => {
    if (totalPrice !== previousTotalPrice && formData.selectedVouchers.length > 0 && isCartChanging) {
      setFormData(prev => ({ ...prev, selectedVouchers: [] }));

      toast.info(
        "Voucher đã được bỏ chọn do thay đổi giỏ hàng. Vui lòng chọn lại voucher phù hợp.",
        {
          position: "top-center",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }
      );

      // Reset cart changing flag
      setIsCartChanging(false);
    }

    // Always update previous total price when it changes
    if (totalPrice !== previousTotalPrice) {
      setPreviousTotalPrice(totalPrice);
    }
  }, [totalPrice, previousTotalPrice, formData.selectedVouchers, isCartChanging]);

  // Reset cart changing flag after a short delay to prevent it from getting stuck
  useEffect(() => {
    if (isCartChanging) {
      const timer = setTimeout(() => {
        setIsCartChanging(false);
      }, 1000); // Reset after 1 second

      return () => clearTimeout(timer);
    }
  }, [isCartChanging]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name as keyof CheckoutFormData]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleCartChange = useCallback(() => {
    // Set flag to indicate cart is being modified
    // This will trigger voucher clearing when totalPrice changes
    setIsCartChanging(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Generate password if needed
    const generatePassword = (length = 12) => {
      const allChars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=";
      let password = "";
      while (length > password.length) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      return password;
    };

    setApiError(null);
    let generatedpassword = "";
    let user_email = formData.email;

    if (!user_email && formData.phone)
      user_email = `${formData.phone}@nguyenlieuphache3t.vn`;

    const validationErrors = validateForm({ ...formData, email: user_email });
    // Also check if shipping fee is calculated and valid
    if (shippingFee === null || shippingError) {
      setApiError(shippingError || "Vui lòng nhập địa chỉ hợp lệ để tính phí vận chuyển.");
      // Optionally, scroll to the address form or highlight the error
      return;
    }
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setIsFormattingAddress(true);

    try {
      // Format shipping address with ESGOO location names
      const formattedAddress = await formatShippingAddress(
        formData.address,
        formData.city,
        formData.district,
        formData.ward
      );

      // Format display names for ward, district, city
      const formattedLocationNames = await formatDisplayAddress(
        formData.city,
        formData.district,
        formData.ward
      );

      // Register if not authenticated
      if (!isAuthenticated) {
        generatedpassword = generatePassword();
        const response = await register({
          first_name: formData.fullName,
          last_name: "",
          phone: formData.phone,
          email: user_email,
          password: generatedpassword,
        });

        if (!response.success) {
          throw new Error(
            "Email đã được đăng ký. Vui lòng đăng nhập bằng email này."
          );
        }

        const result = await login(user_email, generatedpassword);
        if (result.success) {
          isAuthenticated = true;
          isUnauthUser = true;
        }
      }

      if (isAuthenticated) {
        // Update user profile with shipping info
        await updateUserProfile({
          first_name: formData.fullName,
          last_name: "",
          phone_number: formData.phone,
          shipping_address: formData.address,
          city: formData.city,
          district: formData.district,
          ward: formData.ward,
        });
      }

      // Check if any freeship voucher is applied
      const hasFreeshipVoucher = formData.selectedVouchers.some(
        voucher => Number(voucher.value).toFixed(2) === '0.00' && !voucher.is_percentage
      );

      // Create order payload
      const orderPayload = {
        shipping_address: formattedAddress,
        phone_number: formData.phone,
        email: user_email,
        items: items.map((item) => ({
          product: parseInt(item.id),
          quantity: item.quantity,
        })),
        // Use the final calculated/overridden shipping fee
        // If a freeship voucher is applied, it takes precedence
        shipping_fee: hasFreeshipVoucher ? 0 : (shippingFee ?? 0),
        promotion_ids: formData.selectedVouchers.map((v) => v.id),
        payment_method: formData.paymentMethod,
        ward: formattedLocationNames.ward,
        district: formattedLocationNames.district,
        city: formattedLocationNames.city,
      };
      console.log("[CheckoutPage] Creating order with payload:", orderPayload); // Log order payload

      // Create order
      const order = await orderService.createOrder(orderPayload);

      // For COD orders, just redirect to success page
      if (formData.paymentMethod === "cash") {
        clearCart();
        navigate({
          to: NavigationPath.ORDER_SUCCESS,
          params: { orderId: order.id.toString() },
        });
        if (isUnauthUser) {
          await axiosInstance.post("/api/password-retrieval/", {
            email: user_email,
            password: generatedpassword,
          });
          // Popup notification
          toast.info(
            "Vui lòng kiểm tra email của bạn để lấy mật khẩu đăng nhập.",
            {
              position: "top-center",
              autoClose: 8000,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
            }
          );
        }

        return;
      }
      // For bank transfer, create PayOS payment link
      const payosPayload = {
        description: `Thanh toán đơn hàng #${order.id}`,
        productName: items.map((item) => item.title).join(", "),
        price:
          Number(order.total_price) -
          formData.selectedVouchers.reduce((total, v) => total + v.value, 0),
        returnUrl: `${window.location.origin}/order-success/${order.id}`,
        cancelUrl: `${window.location.origin}/checkout`,
        orderCode: order.id.toString(),
      };

      const payosResponse = await payosService.createPaymentLink(payosPayload);

      if (payosResponse.error !== 0 || !payosResponse.data) {
        // If payment creation fails, cancel the order
        await orderService.cancelOrder(order.id.toString());
        throw new Error("Failed to create payment link");
      }

      // Redirect to PayOS payment page
      window.location.href = payosResponse.data.checkoutUrl;
    } catch (error) {
      console.error("Checkout error:", error);
      if (error instanceof Error) {
        setApiError(error.message);
      } else {
        setApiError("Có lỗi xảy ra khi tạo đơn hàng. Vui lòng thử lại sau.");
      }
    } finally {
      setIsSubmitting(false);
      setIsFormattingAddress(false);
    }
  };

  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (items.length === 0) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Giỏ hàng trống</h2>
          <p className="mb-4">
            Bạn cần thêm sản phẩm vào giỏ hàng trước khi thanh toán.
          </p>
          <button
            onClick={() => navigate({ to: NavigationPath.PRODUCTS })}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors"
          >
            Tiếp tục mua sắm
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold text-deep-navy mb-6">Thanh Toán</h1>

      {apiError && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {/* Remove array index from error message if present */}
          <p className="text-base">{apiError.replace(/^\d+:\s*/, "")}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
        {/* Left Column - Forms */}
        <div className="lg:col-span-3">
          <form onSubmit={handleSubmit} id="checkoutForm">
            <CustomerInfoForm
              formData={formData}
              errors={errors}
              onChange={handleInputChange}
            />
            <ShippingAddressForm
              formData={formData}
              errors={errors}
              onChange={handleInputChange}
              onShippingFeeUpdate={handleShippingFeeUpdate} // Pass the handler function
              orderTotal={totalPrice} // Pass totalPrice as orderTotal
            />
            <VoucherSelect
              onVouchersChange={(vouchers) =>
                setFormData((prev) => ({ ...prev, selectedVouchers: vouchers }))
              }
              totalPrice={totalPrice}
              selectedVouchers={formData.selectedVouchers}
              cityCode={formData.city} // Pass the city code to check if it's HCMC
            />
            <PaymentMethodForm
              formData={formData}
              onChange={handleInputChange}
            />

            {/* Mobile Submit Button */}
            <div className="lg:hidden">
              <OrderSummary
                items={items}
                totalPrice={totalPrice}
                isSubmitting={isSubmitting || isFormattingAddress}
                isFormValid={isFormValid(formData)}
                formId="checkoutForm"
                isMobile
                selectedVouchers={formData.selectedVouchers}
                selectedDistrict={selectedDistrictName} // Pass the display name
                shippingFee={shippingFee} // Pass final shipping fee
                shippingError={shippingError} // Pass shipping error
                onCartChange={handleCartChange} // Pass cart change callback
              />
            </div>
          </form>
        </div>

        {/* Right Column - Order Summary */}
        <div className="hidden lg:block lg:col-span-2">
          <OrderSummary
            items={items}
            totalPrice={totalPrice}
            isSubmitting={isSubmitting || isFormattingAddress}
            isFormValid={isFormValid(formData)}
            formId="checkoutForm"
            selectedVouchers={formData.selectedVouchers}
            selectedDistrict={selectedDistrictName} // Pass the display name
            shippingFee={shippingFee} // Pass final shipping fee
            shippingError={shippingError} // Pass shipping error
            onCartChange={handleCartChange} // Pass cart change callback
          />
        </div>
      </div>
    </div>
  );
}
