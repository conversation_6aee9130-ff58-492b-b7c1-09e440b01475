# Tài liệu Cấu hình Django

## Tổng quan
Th<PERSON> mục này chứa các file cấu hình chính của Django project, bao gồm settings, routing, và các cấu hình WSGI/ASGI cho việc triển khai.

## Cấu trúc Project

### S<PERSON> đồ Cấu trúc
```mermaid
graph TD
    A[ecommerce/] --> B[settings.py]
    A --> C[urls.py]
    A --> D[wsgi.py]
    A --> E[asgi.py]
    
    B --> F[Database Config]
    B --> G[Authentication]
    B --> H[Static/Media]
    B --> I[Security]
    
    C --> J[API Routes]
    C --> K[Admin Routes]
    C --> L[Documentation]
```

## Chi tiết Cấu hình

### 1. Cấu hình Database
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
    }
}
```

### 2. Email Settings
```python
EMAIL_CONFIG = {
    'EMAIL_HOST': 'smtp.zoho.com',
    'EMAIL_PORT': 587,
    'EMAIL_USE_TLS': True,
    'EMAIL_HOST_USER': os.getenv('EMAIL_HOST_USER'),
    'EMAIL_HOST_PASSWORD': os.getenv('EMAIL_HOST_PASSWORD')
}
```

### 3. Security Settings
```python
CSRF_TRUSTED_ORIGINS = [
    'http://api.nguyenlieuphache3t.vn',
    'https://api.nguyenlieuphache3t.vn'
]

ALLOWED_HOSTS = [
    'api.nguyenlieuphache3t.vn',
    'localhost',
    '127.0.0.1'
]
```

## URL Routing

### 1. API Endpoints
```python
urlpatterns = [
    path('admin/', store_admin_site.urls),
    path('api/', include('store.urls')),
    path('api/token/', EmailTokenObtainPairView.as_view()),
    path('api/token/refresh/', TokenRefreshView.as_view()),
]
```

### 2. Documentation URLs
```python
path('swagger/', schema_view.with_ui('swagger')),
path('redoc/', schema_view.with_ui('redoc')),
```

## Môi trường và Biến môi trường

### 1. File .env mẫu
```ini
# Database
DB_NAME=ecommerce
DB_USER=postgres
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=5432

# Email
EMAIL_HOST_USER=your-email
EMAIL_HOST_PASSWORD=your-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Security
SECRET_KEY=your-secret-key
DEBUG=False
```

### 2. Environment-specific Settings
```python
if os.getenv('ENVIRONMENT') == 'LOCAL':
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
else:
    STATIC_ROOT = '/app/static'
```

## Rest Framework Settings

### 1. Authentication
```python
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    )
}
```

### 2. JWT Configuration
```python
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=365),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=365),
    'ALGORITHM': 'HS256'
}
```

## Static & Media Files

### 1. Cấu hình cơ bản
```python
STATIC_URL = '/static/'
STATIC_ROOT = '/app/static'
MEDIA_URL = '/media/'
MEDIA_ROOT = '/app/media'
```

### 2. Serving trong Production
```python
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

## Hướng dẫn Triển khai

### 1. Chuẩn bị môi trường
```bash
# Tạo và kích hoạt virtualenv
python -m venv venv
source venv/bin/activate

# Cài đặt dependencies
pip install -r requirements.txt
```

### 2. Cấu hình Database
```bash
# Tạo migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Tạo superuser
python manage.py createsuperuser
```

### 3. Collect Static Files
```bash
python manage.py collectstatic --noinput
```

## Cấu hình Production

### 1. Nginx Config
```nginx
server {
    listen 80;
    server_name api.nguyenlieuphache3t.vn;
    
    location /static/ {
        alias /app/static/;
    }
    
    location /media/ {
        alias /app/media/;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. Gunicorn Config
```bash
gunicorn ecommerce.wsgi:application \
    --bind 0.0.0.0:8000 \
    --workers 3 \
    --timeout 120
```

## Monitoring và Logging

### 1. Logging Configuration
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
        },
    },
}
```

### 2. Health Check Endpoint
```python
def health(request):
    return HttpResponse("OK")

urlpatterns = [
    path('health/', health),
]
```

## Best Practices

1. **Security**
   - Không lưu sensitive data trong code
   - Sử dụng environment variables
   - Enable HTTPS
   - Cấu hình CORS đúng cách

2. **Performance**
   - Sử dụng caching
   - Optimize static files
   - Configure database connection pooling

3. **Maintainability**
   - Tổ chức settings theo environment
   - Document các cấu hình quan trọng
   - Version control cho migrations
