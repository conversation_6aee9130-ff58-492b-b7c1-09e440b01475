# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <AUTHOR> <EMAIL>, 2020
# <PERSON>yo<PERSON>, Ha <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# Du<PERSON>, 2021
# <PERSON><PERSON><PERSON> / <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2014,2016
# <PERSON><PERSON><PERSON>, Ha <<EMAIL>>, 2016
# Jun<PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON>a <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2025
# minsung kang, 2015
# Seoeun(Sun☀️) Hong, 2023
# Woo-<PERSON>, <PERSON><PERSON> <<EMAIL>>, 2016
# se<PERSON><PERSON>, kim <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: Lee Dogeon <<EMAIL>>, 2025\n"
"Language-Team: Korean (http://app.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Personal info"
msgstr "개인정보"

msgid "Permissions"
msgstr "권한"

msgid "Important dates"
msgstr "중요한 일정"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "Primary key %(key)r인 개체 %(name)s이/가 존재하지 않습니다."

msgid "Conflicting form data submitted. Please try again."
msgstr "제출된 양식 데이터가 충돌합니다. 다시 시도해 보세요."

msgid "Password changed successfully."
msgstr "비밀번호가 변경되었습니다."

msgid "Password-based authentication was disabled."
msgstr "비밀번호 기반 인증이 비활성화 되었습니다."

#, python-format
msgid "Change password: %s"
msgstr "비밀번호 변경: %s"

#, python-format
msgid "Set password: %s"
msgstr "비밀번호 설정: %s"

msgid "Authentication and Authorization"
msgstr "인증 및 권한"

msgid "password"
msgstr "비밀번호"

msgid "last login"
msgstr "마지막 로그인"

msgid "Invalid password format or unknown hashing algorithm."
msgstr "잘못된 비밀번호 형식이거나 알 수 없는 해싱 알고리즘 입니다."

msgid "No password set."
msgstr "비밀번호가 설정되지 않습니다."

msgid "Reset password"
msgstr "비밀번호 초기화"

msgid "Set password"
msgstr "비밀번호 설정"

msgid "The two password fields didn’t match."
msgstr "비밀번호가 일치하지 않습니다."

msgid "Password"
msgstr "비밀번호"

msgid "Password confirmation"
msgstr "비밀번호 확인"

msgid "Enter the same password as before, for verification."
msgstr "확인을 위해 이전과 동일한 비밀번호를 입력하세요. "

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"사용자가 비밀번호를 사용하여 인증할 수 있는지 여부입니다. 비활성화 되어 있다"
"면, Single Sign-On 또는 LDAP와 같은 다른 백엔드를 사용하여 인증할 수 있습니"
"다."

msgid "Password-based authentication"
msgstr "비밀번호 기반 인증"

msgid "Enabled"
msgstr "활성화"

msgid "Disabled"
msgstr "비활성화"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"원시 비밀번호는 저장되지 않으므로, 사용자의 비밀번호를 확인할 수 있는 방법이 "
"없습니다."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"비밀번호를 설정하여 이 사용자에 대해 비밀번호 기반 인증을 활성화 합니다."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"올바른 %(username)s와/과 비밀번호를 입력하십시오. 두 필드 모두 대문자와 소문"
"자를 구별합니다."

msgid "This account is inactive."
msgstr "이 계정은 유효하지 않습니다."

msgid "Email"
msgstr "이메일"

msgid "New password"
msgstr "새 비밀번호"

msgid "New password confirmation"
msgstr "새 비밀번호 (확인)"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "기존 비밀번호를 잘못 입력하셨습니다. 다시 입력해 주세요."

msgid "Old password"
msgstr "기존 비밀번호"

msgid "algorithm"
msgstr "알고리즘"

msgid "iterations"
msgstr "반복"

msgid "salt"
msgstr "솔트"

msgid "hash"
msgstr "해시"

msgid "variety"
msgstr "종류"

msgid "version"
msgstr "버젼"

msgid "memory cost"
msgstr "메모리 비용"

msgid "time cost"
msgstr "시간비용"

msgid "parallelism"
msgstr "병행"

msgid "work factor"
msgstr "워크 팩터"

msgid "checksum"
msgstr "체크섬"

msgid "block size"
msgstr "블록 크기"

msgid "name"
msgstr "이름"

msgid "content type"
msgstr "콘텐츠 타입"

msgid "codename"
msgstr "코드명"

msgid "permission"
msgstr "허가"

msgid "permissions"
msgstr "허가"

msgid "group"
msgstr "그룹"

msgid "groups"
msgstr "그룹"

msgid "superuser status"
msgstr "최상위 사용자 권한"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "해당 사용자에게 모든 권한을 허가합니다."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"이 사용자가 속한 그룹. 사용자는 그룹에 부여된 모든 권한을 물려 받습니다."

msgid "user permissions"
msgstr "사용자 권한"

msgid "Specific permissions for this user."
msgstr "이 사용자를 위한 특정 권한."

msgid "username"
msgstr "사용자 이름"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr "150자 이하 문자, 숫자 그리고 @/./+/-/_만 가능합니다."

msgid "A user with that username already exists."
msgstr "해당 사용자 이름은 이미 존재합니다."

msgid "first name"
msgstr "이름"

msgid "last name"
msgstr "성"

msgid "email address"
msgstr "이메일 주소"

msgid "staff status"
msgstr "스태프 권한"

msgid "Designates whether the user can log into this admin site."
msgstr "사용자가 관리사이트에 로그인이 가능한지를 나타냅니다."

msgid "active"
msgstr "활성"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"이 사용자가 활성화되어 있는지를 나타냅니다. 계정을 삭제하는 대신 이것을 선택 "
"해제하세요."

msgid "date joined"
msgstr "등록일"

msgid "user"
msgstr "사용자"

msgid "users"
msgstr "사용자(들)"

#, python-format
msgid "This password is too short. It must contain at least %d character."
msgid_plural ""
"This password is too short. It must contain at least %d characters."
msgstr[0] ""

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "비밀번호는 최소 %(min_length)d자 이상이어야 합니다."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "비밀번호가 %(verbose_name)s와 너무 유사합니다."

msgid "Your password can’t be too similar to your other personal information."
msgstr "다른 개인 정보와 유사한 비밀번호는 사용할 수 없습니다."

msgid "This password is too common."
msgstr "비밀번호가 너무 일상적인 단어입니다."

msgid "Your password can’t be a commonly used password."
msgstr "통상적으로 자주 사용되는 비밀번호는 사용할 수 없습니다."

msgid "This password is entirely numeric."
msgstr "비밀번호가 전부 숫자로 되어 있습니다."

msgid "Your password can’t be entirely numeric."
msgstr "숫자로만 이루어진 비밀번호는 사용할 수 없습니다."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "%(site_name)s의 비밀번호 재설정"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"유효한 사용자명을 입력하세요. 영문자나 숫자, @/./+/-/_기호만 가능합니다."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"유효한 사용자 이름을 입력하세요. 이곳에는 문자, 숫자, @/./+/-/_만 가능합니다."

msgid "Logged out"
msgstr "로그아웃"

msgid "Password reset"
msgstr "비밀번호 초기화"

msgid "Password reset sent"
msgstr "새 비밀번호가 전송되었습니다."

msgid "Enter new password"
msgstr "새 비밀번호 입력"

msgid "Password reset unsuccessful"
msgstr "비밀번호 초기화를 실패하였습니다."

msgid "Password reset complete"
msgstr "비밀번호가 초기화 완료"

msgid "Password change"
msgstr "비밀번호 변경"

msgid "Password change successful"
msgstr "비밀번호를 변경하였습니다."
