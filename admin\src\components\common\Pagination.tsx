interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationProps) {
  const generatePageNumbers = () => {
    if (totalPages <= 10) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    let pages: (number | string)[] = [];

    // Always add first three pages if current page is beyond page 5
    if (currentPage > 5) {
      pages = [1, 2, 3, "..."];
    }

    // Add pages around current page
    const start = Math.max(currentPage > 5 ? currentPage - 2 : 1, 1);
    const end = Math.min(currentPage + 2, totalPages);

    // If we're not starting right after the first three pages, and we added first three pages
    if (currentPage > 5 && start > 4) {
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    } else {
      // If we're in first 5 pages, just add pages from 1 to current+2
      for (let i = 1; i <= end; i++) {
        pages.push(i);
      }
    }

    // Add last three pages with ellipsis if there's a gap
    if (end < totalPages - 3) {
      pages.push("...");
      pages.push(totalPages - 2);
      pages.push(totalPages - 1);
      pages.push(totalPages);
    } else if (end < totalPages) {
      // Fill in any remaining pages up to totalPages
      for (let i = end + 1; i <= totalPages; i++) {
        pages.push(i);
      }
    }

    // Remove duplicates while preserving order
    return Array.from(new Set(pages));
  };

  const pageNumbers = generatePageNumbers();

  return (
    <div className="flex justify-center gap-2 mt-4">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="px-3 py-1 rounded border hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Previous
      </button>
      {pageNumbers.map((page, index) =>
        page === "..." ? (
          <span key={`ellipsis-${index}`} className="px-3 py-1">
            ...
          </span>
        ) : (
          <button
            key={page}
            onClick={() => onPageChange(page as number)}
            className={`px-3 py-1 rounded border ${
              currentPage === page
                ? "bg-primary text-primary-foreground"
                : "hover:bg-muted"
            }`}
          >
            {page}
          </button>
        )
      )}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="px-3 py-1 rounded border hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Next
      </button>
    </div>
  );
}
