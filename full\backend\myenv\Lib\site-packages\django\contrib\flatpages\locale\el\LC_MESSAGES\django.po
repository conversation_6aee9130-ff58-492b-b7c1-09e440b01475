# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# Fot<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2016,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-08-04 06:27+0000\n"
"Last-Translator: Fot<PERSON> <<EMAIL>>\n"
"Language-Team: Greek (http://www.transifex.com/django/django/language/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Προχωρημένες επιλογές"

msgid "Flat Pages"
msgstr "Απλές Σελίδες"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Παράδειγμα: “/about/contact/“. Βεβαιωθείτε ότι περιέχει καθέτους στην αρχή "
"και το τέλος."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Η τιμή αυτή πρέπει να περιέχει μόνο γράμματα, αριθμούς, τελείες, παύλες, "
"κάτω παύλες, καθέτους ή περισπωμένες."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Παράδειγμα: “/about/contact/“. Βεβαιωθείτε ότι περιέχει κάθετο στην αρχή."

msgid "URL is missing a leading slash."
msgstr "Λείπει μια αρχική κάθετος από την διεύθυνση."

msgid "URL is missing a trailing slash."
msgstr "Λείπει μια τελική κάθετος από τη διεύθυνση."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""
"Υπάρχει ήδη Απλή σελίδα με διεύθυνση %(url)s για την ιστοσελίδα %(site)s"

msgid "title"
msgstr "τίτλος"

msgid "content"
msgstr "περιεχόμενο"

msgid "enable comments"
msgstr "ενεργοποίηση σχολίων"

msgid "template name"
msgstr "όνομα περιγράμματος"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Παράδειγμα: “flatpages/contact_page.html“. Αν δεν δηλωθεί, το σύστημα θα "
"χρησιμοποιήσει το “flatpages/default.html“."

msgid "registration required"
msgstr "απαιτείται εγγραφή"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Εάν επιλεγεί, μόνο συνδεδεμένοι χρήστες θα μπορούν να βλέπουν τη σελίδα."

msgid "sites"
msgstr "ιστότοποι"

msgid "flat page"
msgstr "απλή σελίδα"

msgid "flat pages"
msgstr "απλές σελίδες"
