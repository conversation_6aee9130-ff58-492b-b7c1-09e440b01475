import React, { useState } from "react";
import { Modal, Select, Radio, Space, Typography, Divider } from "antd";
import { PAYMENT_METHOD_OPTIONS, PAYMENT_STATUS_OPTIONS } from "../../types/order";

const { Text } = Typography;

interface PaymentMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (params: {
    payment_method: "cod" | "cash" | "bank_transfer";
    payment_status: "paid" | "unpaid";
  }) => void;
  title?: string;
}

export function PaymentMethodModal({
  isOpen,
  onClose,
  onConfirm,
  title = "Cập nhật phương thức thanh toán",
}: PaymentMethodModalProps) {
  const [paymentMethod, setPaymentMethod] = useState<"cod" | "cash" | "bank_transfer">("cash");
  const [paymentStatus, setPaymentStatus] = useState<"paid" | "unpaid">("paid");

  const handleConfirm = () => {
    onConfirm({
      payment_method: paymentMethod,
      payment_status: paymentStatus,
    });
    resetForm();
  };

  const handleCancel = () => {
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setPaymentMethod("cash");
    setPaymentStatus("paid");
  };

  return (
    <Modal
      title={title}
      open={isOpen}
      onOk={handleConfirm}
      onCancel={handleCancel}
      okText="Xác nhận"
      cancelText="Hủy"
    >
      <Space direction="vertical" style={{ width: "100%" }} size="large">
        <div>
          <Text strong>Phương thức thanh toán</Text>
          <Select
            value={paymentMethod}
            onChange={(value) => setPaymentMethod(value)}
            style={{ width: "100%", marginTop: 8 }}
            options={PAYMENT_METHOD_OPTIONS}
          />
        </div>

        <Divider style={{ margin: "12px 0" }} />

        <div>
          <Text strong>Trạng thái thanh toán</Text>
          <div style={{ marginTop: 8 }}>
            <Radio.Group
              value={paymentStatus}
              onChange={(e) => setPaymentStatus(e.target.value)}
            >
              {PAYMENT_STATUS_OPTIONS.map((option) => (
                <Radio key={option.value} value={option.value}>
                  {option.label}
                </Radio>
              ))}
            </Radio.Group>
          </div>
        </div>
      </Space>
    </Modal>
  );
}
