# Generated by Django 4.2.8 on 2024-01-11 08:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('explorer', '0013_querylog_error_querylog_success'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromptLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prompt', models.TextField(blank=True)),
                ('response', models.TextField(blank=True)),
                ('run_at', models.DateTimeField(auto_now_add=True)),
                ('duration', models.FloatField(blank=True, null=True)),
                ('model', models.CharField(blank=True, default='', max_length=128)),
                ('error', models.TextField(blank=True, null=True)),
                ('run_by_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
