import { useState } from "react";
import { User } from "../../types/auth";
import { api, endpoints } from "../../lib/api";

interface StaffSearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (staff: User) => void;
  staffRole?: "sales_admin" | "delivery_staff";
}

export function StaffSearchDialog({
  isOpen,
  onClose,
  onSelect,
  staffRole,
}: StaffSearchDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [searching, setSearching] = useState(false);
  const [staffMembers, setStaffMembers] = useState<User[]>([]);

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    try {
      setSearching(true);
      const response = await api.get("/users/", {
        params: {
          search: searchTerm,
          role: staffRole,
        },
      });
      setStaffMembers(response.data.results);
    } catch (error) {
      console.error("Không thể tìm kiếm nhân viên:", error);
    } finally {
      setSearching(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            Chọn {staffRole === "sales_admin" ? "nhân viên sale" : "nhân viên giao hàng"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Tìm kiếm theo tên hoặc email"
            className="flex-1 border rounded p-2"
          />
          <button
            onClick={handleSearch}
            disabled={searching}
            className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 disabled:opacity-50"
          >
            {searching ? "Đang tìm..." : "Tìm kiếm"}
          </button>
        </div>

        <div className="space-y-4">
          {staffMembers.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Không tìm thấy nhân viên phù hợp</p>
            </div>
          ) : (
            <div className="divide-y">
              {staffMembers.map((staff) => (
                <div
                  key={staff.id}
                  onClick={() => onSelect(staff)}
                  className="p-4 hover:bg-gray-50 cursor-pointer"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">
                        {staff.first_name} {staff.last_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {staff.email}
                      </div>
                      {staff.phone_number && (
                        <div className="text-sm text-gray-500">
                          {staff.phone_number}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => onSelect(staff)}
                      className="text-primary hover:text-primary/90"
                    >
                      Chọn
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
