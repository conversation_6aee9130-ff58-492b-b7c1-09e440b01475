# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011,2015
# <AUTHOR> <EMAIL>, 2021,2025
# <AUTHOR> <EMAIL>, 2015,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: Besnik Bleta <<EMAIL>>, 2021,2025\n"
"Language-Team: Albanian (http://app.transifex.com/django/django/language/"
"sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Dokumentim Administrimi"

msgid "Home"
msgstr "Hyrje"

msgid "Documentation"
msgstr "Dokumentim"

msgid "Bookmarklets"
msgstr "Bookmarklet-e"

msgid "Documentation bookmarklets"
msgstr "Bookmarklet-e Dokumentimi"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Që të instaloni bookmarklet-e, tërhiqeni lidhjen te paneli juaj i "
"faqerojtësve, ose djathtasklikojeni lidhjen dhe shtojeni te faqerojtësit "
"tuaj. Tani mund ta përzgjidhni bookmarklet-in prej cilësdo faqe te sajti."

msgid "Documentation for this page"
msgstr "Dokumentim për këtë faqe"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Ju kalon nga cilado faqe te dokumenti për skenën që prodhon atë faqe."

msgid "Tags"
msgstr "Etiketa"

msgid "List of all the template tags and their functions."
msgstr "Listë e krejt etiketave të gjedheve dhe funksionet e tyre."

msgid "Filters"
msgstr "Filtra"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtrat janë veprime që mund të zbatohen mbi ndryshoret në një gjedhe për të "
"ndryshuar përfundimet e saj."

msgid "Models"
msgstr "Modele"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modelet janë përshkrime të krejt objekteve në sistem dhe fushave "
"përshoqëruar me to. Çdo model ka një listë fushash të cilat mund të përdoren "
"si ndryshore gjedheje"

msgid "Views"
msgstr "Skena"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Çdo faqe në sajtin publik prodhohet nga ajo që quhet skenë. Skena përcakton "
"cila gjedhe përdoret për të prodhuar faqen dhe cilët objekte mund të kihen "
"për atë gjedhe."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Mjete që shfletuesi juaj të përdorë me shpejtësi funksione administrimi."

msgid "Please install docutils"
msgstr "Ju lutemi, instaloni docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Sistemi i dokumentimit për përgjegjësin lyp librarinë Python <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Ju lutemi, kërkojuni përgjegjësve të sistemit të instalojnë <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Fusha"

msgid "Field"
msgstr "Fushë"

msgid "Type"
msgstr "Lloj"

msgid "Description"
msgstr "Përshkrim"

msgid "Methods with arguments"
msgstr "Metoda me argumente"

msgid "Method"
msgstr "Metodë"

msgid "Arguments"
msgstr "Argumente"

msgid "Back to Model documentation"
msgstr "Mbrapsht te dokumentim Modeli"

msgid "Model documentation"
msgstr "Dokumentim modeli"

msgid "Model groups"
msgstr "Grupe modeli"

msgid "Templates"
msgstr "Gjedhe"

#, python-format
msgid "Template: %(name)s"
msgstr "Gjedhe: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Gjedhe: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Shteg kërkimi për gjedhen <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(s’ekziston)"

msgid "Back to Documentation"
msgstr "Mbrapsht te Dokumentimi"

msgid "Template filters"
msgstr "Filtra gjedhesh"

msgid "Template filter documentation"
msgstr "Dokumentim filtrash gjedheje"

msgid "Built-in filters"
msgstr "Filtra të brendshëm"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Për të përdorur këta filtra, vendosni te gjedhja juaj <code>%(code)s</code> "
"përpara se të përdorni filtrin."

msgid "Template tags"
msgstr "Etiketa gjedhesh"

msgid "Template tag documentation"
msgstr "Dokumentim etiketash gjedheje"

msgid "Built-in tags"
msgstr "Etiketa të brendshme"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Për të përdorur këto etiketa, vendosni te gjedhja juaj <code>%(code)s</code> "
"përpara se të përdorni etiketën."

#, python-format
msgid "View: %(name)s"
msgstr "Skenë: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Gjedhe:"

msgid "Back to View documentation"
msgstr "Mbrapsht te dokumentim Skene"

msgid "View documentation"
msgstr "Dokumentim skenash"

msgid "Jump to namespace"
msgstr "Kalo te emërhapësira"

msgid "Empty namespace"
msgstr "Emërhapësirë e zbrazët"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Skena me emërhapësirë %(name)s"

msgid "Views by empty namespace"
msgstr "Skena me emërhapësirë të zbrazët"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Funksion skene: <code>%(full_name)s</code>. Emër: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiketë:"

msgid "filter:"
msgstr "filtër:"

msgid "view:"
msgstr "skenë:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "S’u gjet dot aplikacioni %(app_label)r"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "S’u gjet modeli %(model_name)r në aplikacionin %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "objekti i afërt `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "objektet e afërta `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "krejt %s"

#, python-format
msgid "number of %s"
msgstr "numër i %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s s’duket se është objekt urlpattern"
