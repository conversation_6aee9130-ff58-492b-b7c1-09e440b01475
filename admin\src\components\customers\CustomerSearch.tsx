import { useState } from "react";
import { Input, Select, Button, Checkbox, Space } from "antd";
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons";
import { DateRangePickerWithPresets } from "../common/DateRangePickerWithPresets";

interface CustomerSearchProps {
  onSearch: (params: CustomerSearchParams) => void;
}

export interface CustomerSearchParams {
  query?: string;
  searchBy: "email" | "name" | "phone";
  dateFrom?: string;
  dateTo?: string;
  status?: "active" | "inactive" | "";
  hasOrders?: boolean;
}

export function CustomerSearch({ onSearch }: CustomerSearchProps) {
  const [searchParams, setSearchParams] = useState<CustomerSearchParams>({
    searchBy: "email",
  });

  const handleSearch = () => {
    onSearch(searchParams);
  };

  const handleReset = () => {
    const resetParams = { searchBy: "email" as const };
    setSearchParams(resetParams);
    onSearch(resetParams);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <div className="flex gap-2">
            <Input
              placeholder="Tìm kiếm khách hàng..."
              value={searchParams.query || ""}
              onChange={(e) =>
                setSearchParams({ ...searchParams, query: e.target.value })
              }
              className="flex-1"
              size="large"
              allowClear
              onPressEnter={handleSearch}
            />
            <Select
              value={searchParams.searchBy}
              onChange={(value) =>
                setSearchParams({
                  ...searchParams,
                  searchBy: value as CustomerSearchParams["searchBy"],
                })
              }
              className="w-[140px]"
              size="large"
              options={[
                { value: "email", label: "Email" },
                { value: "name", label: "Họ tên" },
                { value: "phone", label: "Số điện thoại" },
              ]}
            />
          </div>
        </div>

        <div className="min-w-[300px]">
          <DateRangePickerWithPresets
            size="large"
            value={[searchParams.dateFrom, searchParams.dateTo]}
            onChange={([from, to]) => {
              const newParams = {
                ...searchParams,
                dateFrom: from || undefined,
                dateTo: to || undefined,
              };
              setSearchParams(newParams);
              onSearch(newParams);
            }}
            className="w-full"
          />
        </div>

        <div className="flex items-center gap-4">
          <Space>
            <Checkbox
              checked={searchParams.hasOrders}
              onChange={(e) =>
                setSearchParams({
                  ...searchParams,
                  hasOrders: e.target.checked,
                })
              }
            >
              Có đơn hàng
            </Checkbox>
          </Space>
        </div>

        <div>
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              size="large"
              onClick={handleSearch}
            >
              Tìm kiếm
            </Button>
            <Button
              size="large"
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              Đặt lại
            </Button>
          </Space>
        </div>
      </div>
    </div>
  );
}
