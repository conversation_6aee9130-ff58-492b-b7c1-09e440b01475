import { useState, useEffect, useCallback } from "react";
import { Order, SHIPPING_UNIT_OPTIONS } from "../../types/order";
import { Select, DatePicker } from "antd";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";

interface ShippingInfoSectionProps {
  order: Order;
  onUpdateOrder: (data: Partial<Order>) => void;
  disabled?: boolean;
}

export function ShippingInfoSection({
  order,
  onUpdateOrder,
  disabled = false,
}: ShippingInfoSectionProps) {
  // Local state for debouncing
  const [shippingAddress, setShippingAddress] = useState(
    order.shipping_address
  );
  const [ward, setWard] = useState(order.ward || "");
  const [district, setDistrict] = useState(order.district || "");
  const [city, setCity] = useState(order.city || "");
  const [shippingUnit, setShippingUnit] = useState(order.shipping_unit);

  // Sync local state if order prop changes externally
  useEffect(() => {
    setShippingAddress(order.shipping_address);
    setWard(order.ward || "");
    setDistrict(order.district || "");
    setCity(order.city || "");
    setShippingUnit(order.shipping_unit);
  }, [
    order.shipping_address,
    order.ward,
    order.district,
    order.city,
    order.shipping_unit,
  ]);

  // Debounce effect for shipping address
  useEffect(() => {
    if (shippingAddress === order.shipping_address) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ shipping_address: shippingAddress });
    }, 500);

    return () => clearTimeout(handler);
  }, [shippingAddress, order.shipping_address, onUpdateOrder]);

  // Debounce effect for ward
  useEffect(() => {
    if (ward === (order.ward || "")) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ ward: ward || undefined });
    }, 500);

    return () => clearTimeout(handler);
  }, [ward, order.ward, onUpdateOrder]);

  // Debounce effect for district
  useEffect(() => {
    if (district === (order.district || "")) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ district: district || undefined });
    }, 500);

    return () => clearTimeout(handler);
  }, [district, order.district, onUpdateOrder]);

  // Debounce effect for city
  useEffect(() => {
    if (city === (order.city || "")) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ city: city || undefined });
    }, 500);

    return () => clearTimeout(handler);
  }, [city, order.city, onUpdateOrder]);

  // Debounce effect for shipping unit
  useEffect(() => {
    if (shippingUnit === order.shipping_unit) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ shipping_unit: shippingUnit });
    }, 500);

    return () => clearTimeout(handler);
  }, [shippingUnit, order.shipping_unit, onUpdateOrder]);

  // Local state for delivery date
  const [deliveryDate, setDeliveryDate] = useState<string | undefined>(
    order.delivery_date
  );

  // Debounce effect for delivery date
  useEffect(() => {
    if (deliveryDate === order.delivery_date) return;

    const handler = setTimeout(() => {
      onUpdateOrder({ delivery_date: deliveryDate });
    }, 500);

    return () => clearTimeout(handler);
  }, [deliveryDate, order.delivery_date, onUpdateOrder]);

  // Sync delivery date if order prop changes externally
  useEffect(() => {
    setDeliveryDate(order.delivery_date);
  }, [order.delivery_date]);

  // Memoize the shipping unit change handler to prevent unnecessary re-renders
  const handleShippingUnitChange = useCallback((value: any) => {
    setShippingUnit(value);
  }, []);

  // Memoize the delivery date change handler
  const handleDeliveryDateChange = useCallback((date: Dayjs | null) => {
    setDeliveryDate(date?.format("YYYY-MM-DD") || undefined);
  }, []);

  return (
    <div className="bg-white rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-3">Thông tin vận chuyển</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Đơn vị vận chuyển
          </label>
          <Select
            value={shippingUnit || undefined}
            onChange={handleShippingUnitChange}
            className="w-full"
            disabled={disabled}
            placeholder="Chọn đơn vị vận chuyển"
            options={SHIPPING_UNIT_OPTIONS as any}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Ngày giao hàng
          </label>
          <DatePicker
            value={deliveryDate ? dayjs(deliveryDate) : null}
            onChange={handleDeliveryDateChange}
            className="w-full"
            disabled={disabled}
            placeholder="Chọn ngày giao hàng"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            Địa chỉ chi tiết
          </label>
          <input
            type="text"
            value={shippingAddress}
            onChange={(e) => setShippingAddress(e.target.value)}
            className="w-full border rounded p-2 mb-2"
            placeholder="Số nhà, tên đường..."
            disabled={disabled}
          />

          <div className="grid grid-cols-3 gap-2">
            <div>
              <input
                type="text"
                value={ward}
                onChange={(e) => setWard(e.target.value)}
                className="w-full border rounded p-2"
                placeholder="Phường/Xã"
                disabled={disabled}
              />
            </div>
            <div>
              <input
                type="text"
                value={district}
                onChange={(e) => setDistrict(e.target.value)}
                className="w-full border rounded p-2"
                placeholder="Quận/Huyện"
                disabled={disabled}
              />
            </div>
            <div>
              <input
                type="text"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                className="w-full border rounded p-2"
                placeholder="Thành phố"
                disabled={disabled}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
