import {
  UserIcon,
  ClipboardIcon,
  BanknotesIcon,
  TruckIcon,
} from "@heroicons/react/24/outline";
import { OrderCardSection } from "./OrderCardSection";
import { Order } from "../../types/order";
import { formatCurrency } from "../../lib/utils";

interface OrderCardOrderInfoProps {
  order: Order;
}

function OrderInfoContent({ order }: OrderCardOrderInfoProps) {
  return (
    <div className="space-y-2">
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <UserIcon className="h-4 w-4" />
          NVBH:
        </div>
        <div className="font-medium">
          {order.sales_admin
            ? `${order.sales_admin.first_name} ${order.sales_admin.last_name}`
            : "—"}
        </div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <ClipboardIcon className="h-4 w-4" />
          Sản phẩm:
        </div>
        <div className="space-y-1">
          {order.items.map((item, i) => (
            <div key={i} className="font-medium">
              {item.product_name}{" "}
              {item.variant_name && `(${item.variant_name})`} x{item.quantity}
            </div>
          ))}
        </div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <BanknotesIcon className="h-4 w-4" />
          Giá tiền:
        </div>
        <div className="font-medium">{formatCurrency(order.total_price)}</div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <TruckIcon className="h-4 w-4" />
          Phí vận chuyển:
        </div>
        <div className="font-medium">{formatCurrency(order.shipping_fee)}</div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <BanknotesIcon className="h-4 w-4" />
          Tổng tiền:
        </div>
        <div className="font-medium text-lg">
          {formatCurrency(order.final_total)}
        </div>
      </div>
      <div>
        <div className="flex items-center gap-1 text-muted-foreground text-sm">
          <BanknotesIcon className="h-4 w-4" />
          Thanh toán:
        </div>
        <div className="font-medium">
          {order.payment_method === "cod"
            ? "COD"
            : order.payment_method === "cash"
            ? "Tiền mặt"
            : order.payment_method === "bank_transfer"
            ? "Chuyển khoản"
            : "—"}
        </div>
      </div>
    </div>
  );
}

interface Props extends OrderCardOrderInfoProps {
  isDesktop?: boolean;
}

export function OrderCardOrderInfo({ order, isDesktop }: Props) {
  if (isDesktop) {
    return <OrderInfoContent order={order} />;
  }

  return (
    <OrderCardSection title="THÔNG TIN ĐƠN HÀNG" defaultOpen={false}>
      <OrderInfoContent order={order} />
    </OrderCardSection>
  );
}
