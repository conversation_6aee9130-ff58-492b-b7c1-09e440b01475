{% extends "explorer/base.html" %}
{% load explorer_tags i18n %}

{% block sql_explorer_content %}
<div class="container">
    <div class="row">
        <div id="query_area">
            <h2>{% translate "Playground" %}</h2>
            <p>
                {% blocktranslate trimmed %}
                    The playground is for experimenting and writing ad-hoc queries. By default, nothing you do here will be saved.
                {% endblocktranslate %}
            </p>
            <form role="form" action="{% url 'explorer_playground' %}" method="post" id="editor" class="playground-form form-horizontal">{% csrf_token %}
                {% if error %}
                    <div class="alert alert-danger db-error">{{ error|escape }}</div>
                {% endif %}
                {{ form.non_field_errors }}
                {% if can_change %}
                    <div class="mb-3 form-floating">
                        {{ form.database_connection }}
                        <label for="id_database_connection" class="form-label">{% translate "Connection" %}</label>
                    </div>
                {% else %}
                    {# still need to submit the connection, just hide the UI element #}
                    <div class="d-none">
                      {{ form.database_connection }}
                    </div>
                {% endif %}
                <div class="row">
                    <div class="col">
                        <label for="id_sql" class="form-label">SQL</label>
                    </div>
                    <div class="col text-end">
                        {% if ql_id %}
                            <a href="{% url 'explorer_playground' %}?querylog_id={{ ql_id }}">
                                <i class="bi-link"></i>
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="row" id="sql_editor_container">
                    <textarea class="form-control" cols="40" id="id_sql" name="sql" rows="20">{{ query.sql }}</textarea>
                    <div id="schema_tooltip" class="d-none"></div>
                </div>

                <div class="mt-3 text-center">
                    <div class="position-relative float-end">
                        <small>
                            <a href="#" title="Format code (Cmd/ctrl+shift+f)" id="format_button">
                                <i class="bi-list-nested"></i>
                            </a>
                        </small>
                    </div>
                    <div class="btn-group" role="group">
                        <button type="submit" id="refresh_play_button"
                                class="btn btn-primary">{% translate 'Refresh' %}</button>
                        <button type="submit" id="create_button"
                                class="btn btn-outline-primary">{% translate 'Save As New' %}</button>
                        {% export_buttons query %}

                        <button type="button" class="btn btn-outline-primary" id="show_schema_button">
                            {% translate "Show Schema" %}
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="hide_schema_button"
                                style="display: none;">
                            {% translate "Hide Schema" %}
                        </button>
                    </div>
                </div>
                <input type="hidden" value="{% translate 'Playground Query' %}" name="title" />
                {% if assistant_enabled %}
                    {% include 'explorer/assistant.html' %}
                {% endif %}
            </form>
        </div>
        <div id="schema" style="display: none;">
            <iframe src="about:blank" height="828px" frameBorder="0" id="schema_frame"></iframe>
        </div>
    </div>
</div>
{% include 'explorer/preview_pane.html' %}

{% endblock %}
