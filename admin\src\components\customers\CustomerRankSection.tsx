import React from 'react';
import { Card, Progress, Typography, Space, Divider, Tooltip } from 'antd';
import { CustomerRankBadge } from './CustomerRankBadge';
import { CustomerService } from '@/services/customerService';
import { Customer, CustomerStats } from '@/types/customer';
import { formatCurrency } from '@/lib/utils';

const { Title, Text } = Typography;

interface CustomerRankSectionProps {
  customer: Customer;
  stats?: CustomerStats;
}

export const CustomerRankSection: React.FC<CustomerRankSectionProps> = ({
  customer,
  stats
}) => {
  // Use rank info from stats if available, otherwise fallback to customer profile
  const rankInfo = stats?.rank_info;
  const currentRank = rankInfo?.current_rank || customer.profile.rank || 'normal';
  const rankDisplayInfo = CustomerService.getRankInfo(currentRank);

  const formatThreshold = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(0)} triệu`;
    }
    return formatCurrency(amount);
  };

  // Use data from backend rank_info if available
  const totalSpent = rankInfo?.total_spent || stats?.total_spent || 0;
  const progressPercentage = rankInfo?.progress_percentage || 0;
  const nextRankThreshold = rankInfo?.next_rank_threshold || 0;
  const deliveredOrders = rankInfo?.delivered_orders || 0;

  let nextRankName = '';
  if (currentRank === 'normal') {
    nextRankName = 'Bạc';
  } else if (currentRank === 'silver') {
    nextRankName = 'Vàng';
  }

  const remainingAmount = Math.max(0, nextRankThreshold - totalSpent);

  return (
    <Card className="h-full">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Title level={4} className="mb-0">Hạng khách hàng</Title>
          <CustomerRankBadge rank={currentRank} size="default" />
        </div>

        <Divider className="my-3" />

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Text type="secondary">Hạng hiện tại:</Text>
            <Text strong>{rankInfo?.rank_display || rankDisplayInfo.label}</Text>
          </div>

          <div className="flex justify-between items-center">
            <Text type="secondary">Phần trăm giảm giá:</Text>
            <Text strong className="text-green-600">
              {rankDisplayInfo.discount}%
            </Text>
          </div>

          <div className="flex justify-between items-center">
            <Text type="secondary">Tổng chi tiêu:</Text>
            <Text strong>{formatCurrency(totalSpent)}</Text>
          </div>

          <div className="flex justify-between items-center">
            <Text type="secondary">Đơn hàng hoàn thành:</Text>
            <Text>{deliveredOrders} đơn</Text>
          </div>
        </div>

        {currentRank !== 'gold' && (
          <>
            <Divider className="my-3" />

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Text type="secondary">Tiến độ lên hạng {nextRankName}:</Text>
                <Text>{progressPercentage.toFixed(1)}%</Text>
              </div>

              <Tooltip
                title={`Cần thêm ${formatCurrency(remainingAmount)} để lên hạng ${nextRankName}`}
              >
                <Progress
                  percent={progressPercentage}
                  strokeColor={{
                    '0%': '#87d068',
                    '100%': '#108ee9',
                  }}
                  showInfo={false}
                />
              </Tooltip>

              <div className="text-center">
                <Text type="secondary" className="text-sm">
                  Cần thêm <Text strong className="text-blue-600">
                    {formatThreshold(remainingAmount)}
                  </Text> để lên hạng {nextRankName}
                </Text>
              </div>

              <div className="bg-blue-50 p-3 rounded text-center">
                <Text type="secondary" className="text-sm">
                  🎯 Mục tiêu: {formatThreshold(nextRankThreshold)} cho hạng {nextRankName}
                </Text>
              </div>
            </div>
          </>
        )}

        {currentRank === 'gold' && (
          <>
            <Divider className="my-3" />
            <div className="bg-yellow-50 p-3 rounded text-center">
              <Text className="text-yellow-700">
                🏆 Khách hàng VIP - Hạng cao nhất!
              </Text>
            </div>
          </>
        )}

        <Divider className="my-3" />

        <div className="space-y-2">
          <Text type="secondary" className="text-sm">
            💡 <strong>Lưu ý:</strong> Hạng được tính dựa trên tổng giá trị đơn hàng đã giao thành công
          </Text>

          {(rankDisplayInfo.discount) > 0 && (
            <Text type="secondary" className="text-sm">
              🎁 Khách hàng được giảm <strong>
                {rankDisplayInfo.discount}%
              </strong> cho các đơn hàng mới
            </Text>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CustomerRankSection;
