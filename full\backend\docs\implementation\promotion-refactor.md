# Promotion System Refactoring

## Overview
This document outlines the refactoring of the promotion system to simplify and unify the discount handling in our e-commerce platform. We are consolidating the Voucher and Promotion models into a single enhanced Promotion model, while removing unnecessary complexity from promotion constraints.

## Current State

```mermaid
classDiagram
    class Promotion {
        +String code
        +String description
        +Boolean is_active
        +Decimal value
        +Boolean is_percentage
        +DateTime created_at
        +DateTime updated_at
        +apply_to_cart(cart)
        +check_constraints(cart)
    }
    class PromotionConstraint {
        +String constraint_type
        +Decimal value
        +is_satisfied(cart)
    }
    class Voucher {
        +String code
        +String description
        +Boolean is_active
        +Decimal value
        +Boolean is_percentage
        +Int usage_limit
        +Int usage_count
        +Decimal min_order_amount
        +DateTime valid_from
        +DateTime valid_until
        +DateTime created_at
        +DateTime updated_at
        +calculate_discount(order_total)
    }
    class VoucherUsage {
        +DateTime used_at
        +Decimal discount_amount
    }
    class CartPromotion {
        +DateTime applied_at
    }
    
    Promotion "1" -- "*" PromotionConstraint
    Promotion "1" -- "*" CartPromotion
    Voucher "1" -- "*" VoucherUsage
```

## New Design

```mermaid
classDiagram
    class Promotion {
        +String code
        +String description
        +Boolean is_active
        +Decimal value
        +Boolean is_percentage
        +Decimal min_purchase_amount
        +Int usage_limit
        +Int usage_count
        +DateTime valid_from
        +DateTime valid_until
        +DateTime created_at
        +DateTime updated_at
        +calculate_discount(amount)
        +is_valid()
    }
    class PromotionUsage {
        +DateTime used_at
        +Decimal discount_amount
    }
    class CartPromotion {
        +DateTime applied_at
    }
    
    Promotion "1" -- "*" PromotionUsage
    Promotion "1" -- "*" CartPromotion
```

## Changes Overview

1. Model Changes:
   - Enhanced Promotion model with additional fields from Voucher
   - Removed PromotionConstraint model
   - Removed Voucher model
   - Renamed VoucherUsage to PromotionUsage

2. Key Improvements:
   - Unified discount calculation logic
   - Simplified constraint handling
   - Consistent usage tracking
   - Better code maintainability

## Migration Steps

1. Database Migration:
   - Add new fields to Promotion model
   - Create PromotionUsage model
   - Migrate existing Voucher data to Promotion
   - Remove Voucher and PromotionConstraint models

2. Code Updates:
   - Update cart discount calculation
   - Update views and serializers
   - Update admin interface
   - Update tests

## Benefits

1. **Simplified Architecture**: Single source of truth for all discounts
2. **Reduced Complexity**: Removed separate constraint model
3. **Better Maintainability**: Less code duplication
4. **Consistent Behavior**: Unified discount calculation
5. **Improved Performance**: Fewer database queries needed

## Implementation Timeline

1. Documentation & Planning (Current)
2. Database Migration Development
3. Code Updates Implementation
4. Testing & Validation
5. Deployment

## API Changes

The existing promotion endpoints will be maintained, but voucher endpoints will be consolidated into the promotion endpoints.

### Example Usage

```python
# Creating a promotion
promotion = Promotion.objects.create(
    code="SAVE20",
    value=20,
    is_percentage=True,
    min_purchase_amount=100,
    usage_limit=100,
    valid_from=timezone.now(),
    valid_until=timezone.now() + timedelta(days=30)
)

# Calculating discount
discount = promotion.calculate_discount(cart.total_price)
