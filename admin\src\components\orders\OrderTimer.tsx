import { useState, useEffect } from "react";
import { Order } from "../../types/order";
import {
  isOutsideWorkingHours,
  calculateElapsedTime,
  formatElapsedTime,
  isOrderOverdue
} from "../../utils/orderTimerUtils";

interface OrderTimerProps {
  order: Order;
}

export function OrderTimer({ order }: OrderTimerProps) {
  const [elapsedTime, setElapsedTime] = useState<string>("");
  const [isOverdue, setIsOverdue] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);

  useEffect(() => {
    // Nếu không có thời gian xác nhận, không cần tính timer
    if (!order.confirmation_time) {
      return;
    }

    // Nếu đơn hàng đã hoàn thành và có thời gian hoàn thành
    if (order.status === "delivered" && order.completion_time) {
      // Hiển thị thời gian hoàn thành
      const completionTime = new Date(order.completion_time);

      // Đơn hàng đã hoàn thành nên timer không còn tạm dừng
      setIsPaused(false);
      setIsOverdue(false);

      // Format thời gian hoàn thành
      const formattedTime = `${completionTime.getHours().toString().padStart(2, '0')}:${completionTime.getMinutes().toString().padStart(2, '0')}`;
      setElapsedTime(formattedTime);
      return;
    }

    // Nếu đơn hàng đang xử lý hoặc đang giao
    if (order.status === "processing" || order.status === "shipped") {
      // Tính thời gian từ lúc xác nhận đến hiện tại
      const calculateTimer = () => {
        const now = new Date();
        const confirmationTime = new Date(order.confirmation_time!);

        // Log thời gian để debug
        // console.log("Current time:", now);

        // Kiểm tra xem timer có đang tạm dừng không (ngoài giờ làm việc)
        const outsideWorkingHours = isOutsideWorkingHours();
        setIsPaused(outsideWorkingHours);

        // Tính thời gian đã trôi qua (chỉ tính trong giờ làm việc)
        const elapsedMs = calculateElapsedTime(confirmationTime, now);

        // Nếu ngoài giờ làm việc, hiển thị "0 phút" và đánh dấu là tạm dừng
        if (outsideWorkingHours) {
          setElapsedTime("0 phút");
          setIsOverdue(false);
        } else {
          setElapsedTime(formatElapsedTime(elapsedMs));
          // Sử dụng hàm isOrderOverdue để kiểm tra quá hạn
          setIsOverdue(isOrderOverdue(confirmationTime));
        }
      };

      // Tính toán ban đầu
      calculateTimer();

      // Cập nhật mỗi phút
      const intervalId = setInterval(calculateTimer, 60000);
      return () => clearInterval(intervalId);
    }
  }, [order.confirmation_time, order.completion_time, order.status]);



  return (
    <div className={`
      ${isOverdue ? "text-red-500 font-bold" :
        order.status === "delivered" ? "text-black" :
        isPaused ? "text-gray-500" : "text-green-500"}
    `}>
      {elapsedTime}
      {/* {isPaused && order.status !== "delivered" && (
        <span className="ml-2 text-xs text-gray-500">(tạm dừng)</span>
      )} */}
    </div>
  );
}
