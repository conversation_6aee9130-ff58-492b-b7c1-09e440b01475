# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Telugu (http://www.transifex.com/django/django/language/te/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: te\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr ""

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "పట్టము"

msgid "content"
msgstr "సూచన "

msgid "enable comments"
msgstr ""

msgid "template name"
msgstr ""

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"ఉదాహరణ: 'flatpages/contact_page.html'.ఇది ఇవ్వకపోతే సిస్టం  'flatpages/default."
"html' ని వాడుకుంటడి"

msgid "registration required"
msgstr "నమొదు చేయటము అవసరం"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "ఇది చెక్ చేసి ఉంటే కేవలం లాగ్గడ్ ఇన్ యూజర్లు పేజి చూడలేస్తారు"

msgid "sites"
msgstr ""

msgid "flat page"
msgstr ""

msgid "flat pages"
msgstr ""
