{% extends "explorer/base.html" %}
{% load i18n %}

{% block sql_explorer_content %}
    <div class="container">
        <h3>{% blocktranslate with pagenum=page_obj.number %}Recent Query Logs - Page {{pagenum}}{% endblocktranslate %}</h3>
        <table class="table table-striped query-list">
            <thead>
                <tr>
                    <th>{% translate "Run At" %}</th>
                    <th>{% translate "Run By" %}</th>
                    <th>{% translate "Database Connection" %}</th>
                    <th>{% translate "Duration" %}</th>
                    <th class="sql">SQL</th>
                    <th>{% translate "Query ID" %}</th>
                    <th>{% translate "Playground" %}</th>
                </tr>
            </thead>
            <tbody>
                {% for object in recent_logs %}
                    <tr>
                        <td>{{ object.run_at|date:"SHORT_DATETIME_FORMAT" }}</td>
                        <td>{{ object.run_by_user }}</td>
                        <td>{{ object.database_connection }}</td>
                        <td>{{ object.duration|floatformat:2 }}ms</td>
                        <td class="log-sql">{{ object.sql }}</td>
                        <td>
                            {% if object.query_id %}
                                <a href="{% url "query_detail" object.query_id %}">
                                    {% blocktranslate trimmed with query_id=object.query_id %}
                                        Query {{ query_id }}
                                    {% endblocktranslate %}
                                </a>
                            {% elif object.is_playground %}
                                {% translate "Playground" %}
                            {% else %}
                                --
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url "explorer_playground" %}?querylog_id={{ object.id }}">
                                {% translate "Open" %}
                            </a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        {% if is_paginated %}
            <div class="pagination">
                <span>
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}"><i class="bi-arrow-left-square"></i></a>
                    {% endif %}
                    <span class="page-current">
                        {% blocktranslate trimmed with pnum=page_obj.number anum=page_obj.paginator.num_pages %}
                            Page {{ pnum }} of {{ anum }}
                        {% endblocktranslate %}
                    </span>
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}"><i class="bi-arrow-right-square"></i></a>
                    {% endif %}
                </span>
            </div>
        {% endif %}
    </div>
{% endblock %}
