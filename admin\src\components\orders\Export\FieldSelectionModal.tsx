import React from 'react';
import { <PERSON><PERSON>, Checkbox, But<PERSON>, Divider } from 'antd';
import { AVAILABLE_MULTI_ORDER_EXPORT_FIELDS } from '@/constants/constants';

interface FieldSelectionModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  selectedFields: string[];
  onFieldsChange: (fields: string[]) => void;
  isExporting: boolean;
}

const FieldSelectionModal: React.FC<FieldSelectionModalProps> = ({
  visible,
  onOk,
  onCancel,
  selectedFields,
  onFieldsChange,
  isExporting
}) => {
  const totalFields = AVAILABLE_MULTI_ORDER_EXPORT_FIELDS.length;
  const selectedCount = selectedFields.length;
  const isAllSelected = selectedCount === totalFields;

  const handleSelectAll = () => {
    if (isAllSelected) {
      onFieldsChange([]);
    } else {
      onFieldsChange(AVAILABLE_MULTI_ORDER_EXPORT_FIELDS.map(field => field.value));
    }
  };

  return (
    <Modal
      title="Chọn các trường để xuất ra Excel"
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      okText="Xuất Excel"
      cancelText="Hủy"
      width={700}
      confirmLoading={isExporting}
    >
      <div className="mb-4">
        <p className="mb-3">Vui lòng chọn các cột bạn muốn bao gồm trong file Excel:</p>

        {/* Summary and Select All Section */}
        <div className="flex justify-between items-center mb-3 p-3 bg-gray-50 rounded">
          <div className="text-sm text-gray-600">
            <span className="font-medium text-blue-600">{selectedCount}</span> / {totalFields} trường đã chọn
          </div>
          <Button
            type={isAllSelected ? "default" : "primary"}
            size="small"
            onClick={handleSelectAll}
          >
            {isAllSelected ? "Bỏ chọn tất cả" : "Chọn tất cả"}
          </Button>
        </div>

        <Divider className="my-3" />
      </div>

      <Checkbox.Group
        className="w-full"
        value={selectedFields}
        onChange={(checkedValues) => onFieldsChange(checkedValues as string[])}
      >
        <div className="grid grid-cols-2 gap-x-6 gap-y-2">
          {AVAILABLE_MULTI_ORDER_EXPORT_FIELDS.map(field => (
            <Checkbox key={field.value} value={field.value} className="mb-2">
              {field.label}
            </Checkbox>
          ))}
        </div>
      </Checkbox.Group>
    </Modal>
  );
};

export default FieldSelectionModal;