{% extends "explorer/base.html" %}

{% block sql_explorer_content %}
<div class="container">
    <h3>Table Annotations</h3>
    <p>Write some notes about your tables to help the AI Assistant do its job. Relevant annotations will be automatically injected into AI assistant requests.</p>
    <p>Good annotations may describe the purposes of columns that are not obvious from their name alone, common joins to other tables, or the semantic meaning of enum values.</p>
    <div class="mt-3">
        <a href="{% url 'table_description_create' %}" class="btn btn-primary mb-3">Create Annotation</a>
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>Connection</th>
                    <th>Table Name</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for table_description in table_descriptions %}
                    <tr>
                        <td>{{ table_description.database_connection }}</td>
                        <td>{{ table_description.table_name }}</td>
                        <td>{{ table_description.description|truncatewords:20 }}</td>
                        <td>
                            <a href="{% url 'table_description_update' table_description.pk %}" class="px-2"><i class="bi-pencil-square"></i></a>
                            <a href="{% url 'table_description_delete' table_description.pk %}"><i class="bi-trash"></i></a>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="4" class="text-center">No table descriptions available.</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
