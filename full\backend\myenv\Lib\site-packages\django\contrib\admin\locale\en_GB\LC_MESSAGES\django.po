# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2019
# jon_at<PERSON>on <<EMAIL>>, 2011-2012
# <PERSON> <<EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2019-04-05 10:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/django/"
"django/language/en_GB/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: en_GB\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "Successfully deleted %(count)d %(items)s."

#, python-format
msgid "Cannot delete %(name)s"
msgstr "Cannot delete %(name)s"

msgid "Are you sure?"
msgstr "Are you sure?"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Delete selected %(verbose_name_plural)s"

msgid "Administration"
msgstr "Administration"

msgid "All"
msgstr "All"

msgid "Yes"
msgstr "Yes"

msgid "No"
msgstr "No"

msgid "Unknown"
msgstr "Unknown"

msgid "Any date"
msgstr "Any date"

msgid "Today"
msgstr "Today"

msgid "Past 7 days"
msgstr "Past 7 days"

msgid "This month"
msgstr "This month"

msgid "This year"
msgstr "This year"

msgid "No date"
msgstr "No date"

msgid "Has date"
msgstr "Has date"

#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."

msgid "Action:"
msgstr "Action:"

#, python-format
msgid "Add another %(verbose_name)s"
msgstr "Add another %(verbose_name)s"

msgid "Remove"
msgstr "Remove"

msgid "Addition"
msgstr "Addition"

msgid "Change"
msgstr "Change"

msgid "Deletion"
msgstr "Deletion"

msgid "action time"
msgstr "action time"

msgid "user"
msgstr "user"

msgid "content type"
msgstr "content type"

msgid "object id"
msgstr "object id"

#. Translators: 'repr' means representation
#. (https://docs.python.org/library/functions.html#repr)
msgid "object repr"
msgstr "object repr"

msgid "action flag"
msgstr "action flag"

msgid "change message"
msgstr "change message"

msgid "log entry"
msgstr "log entry"

msgid "log entries"
msgstr "log entries"

#, python-format
msgid "Added \"%(object)s\"."
msgstr "Added \"%(object)s\"."

#, python-format
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr "Changed \"%(object)s\" - %(changes)s"

#, python-format
msgid "Deleted \"%(object)s.\""
msgstr "Deleted \"%(object)s.\""

msgid "LogEntry Object"
msgstr "LogEntry Object"

#, python-brace-format
msgid "Added {name} \"{object}\"."
msgstr "Added {name} \"{object}\"."

msgid "Added."
msgstr "Added."

msgid "and"
msgstr "and"

#, python-brace-format
msgid "Changed {fields} for {name} \"{object}\"."
msgstr ""

#, python-brace-format
msgid "Changed {fields}."
msgstr ""

#, python-brace-format
msgid "Deleted {name} \"{object}\"."
msgstr ""

msgid "No fields changed."
msgstr "No fields changed."

msgid "None"
msgstr "None"

msgid ""
"Hold down \"Control\", or \"Command\" on a Mac, to select more than one."
msgstr ""

#, python-brace-format
msgid "The {name} \"{obj}\" was added successfully."
msgstr ""

msgid "You may edit it again below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was added successfully. You may add another {name} "
"below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was changed successfully. You may edit it again below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was added successfully. You may edit it again below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was changed successfully. You may add another {name} "
"below."
msgstr ""

#, python-brace-format
msgid "The {name} \"{obj}\" was changed successfully."
msgstr ""

msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."

msgid "No action selected."
msgstr "No action selected."

#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr "The %(name)s \"%(obj)s\" was deleted successfully."

#, python-format
msgid "%(name)s with ID \"%(key)s\" doesn't exist. Perhaps it was deleted?"
msgstr ""

#, python-format
msgid "Add %s"
msgstr "Add %s"

#, python-format
msgid "Change %s"
msgstr "Change %s"

#, python-format
msgid "View %s"
msgstr ""

msgid "Database error"
msgstr "Database error"

#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] "%(count)s %(name)s was changed successfully."
msgstr[1] "%(count)s %(name)s were changed successfully."

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s selected"
msgstr[1] "All %(total_count)s selected"

#, python-format
msgid "0 of %(cnt)s selected"
msgstr "0 of %(cnt)s selected"

#, python-format
msgid "Change history: %s"
msgstr "Change history: %s"

#. Translators: Model verbose name and instance representation,
#. suitable to be an item in a list.
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr ""

#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""

msgid "Django site admin"
msgstr "Django site admin"

msgid "Django administration"
msgstr "Django administration"

msgid "Site administration"
msgstr "Site administration"

msgid "Log in"
msgstr "Log in"

#, python-format
msgid "%(app)s administration"
msgstr ""

msgid "Page not found"
msgstr "Page not found"

msgid "We're sorry, but the requested page could not be found."
msgstr "We're sorry, but the requested page could not be found."

msgid "Home"
msgstr "Home"

msgid "Server error"
msgstr "Server error"

msgid "Server error (500)"
msgstr "Server error (500)"

msgid "Server Error <em>(500)</em>"
msgstr "Server Error <em>(500)</em>"

msgid ""
"There's been an error. It's been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""

msgid "Run the selected action"
msgstr "Run the selected action"

msgid "Go"
msgstr "Go"

msgid "Click here to select the objects across all pages"
msgstr "Click here to select the objects across all pages"

#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr "Select all %(total_count)s %(module_name)s"

msgid "Clear selection"
msgstr "Clear selection"

msgid ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."
msgstr ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."

msgid "Enter a username and password."
msgstr "Enter a username and password."

msgid "Change password"
msgstr "Change password"

msgid "Please correct the error below."
msgstr ""

msgid "Please correct the errors below."
msgstr ""

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr "Enter a new password for the user <strong>%(username)s</strong>."

msgid "Welcome,"
msgstr "Welcome,"

msgid "View site"
msgstr ""

msgid "Documentation"
msgstr "Documentation"

msgid "Log out"
msgstr "Log out"

#, python-format
msgid "Add %(name)s"
msgstr "Add %(name)s"

msgid "History"
msgstr "History"

msgid "View on site"
msgstr "View on site"

msgid "Filter"
msgstr "Filter"

msgid "Remove from sorting"
msgstr "Remove from sorting"

#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr "Sorting priority: %(priority_number)s"

msgid "Toggle sorting"
msgstr "Toggle sorting"

msgid "Delete"
msgstr "Delete"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"

#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"

msgid "Objects"
msgstr ""

msgid "Yes, I'm sure"
msgstr "Yes, I'm sure"

msgid "No, take me back"
msgstr ""

msgid "Delete multiple objects"
msgstr "Delete multiple objects"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"

#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"

msgid "View"
msgstr ""

msgid "Delete?"
msgstr "Delete?"

#, python-format
msgid " By %(filter_title)s "
msgstr " By %(filter_title)s "

msgid "Summary"
msgstr ""

#, python-format
msgid "Models in the %(name)s application"
msgstr ""

msgid "Add"
msgstr "Add"

msgid "You don't have permission to view or edit anything."
msgstr ""

msgid "Recent actions"
msgstr ""

msgid "My actions"
msgstr ""

msgid "None available"
msgstr "None available"

msgid "Unknown content"
msgstr "Unknown content"

msgid ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."

#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

msgid "Forgotten your password or username?"
msgstr "Forgotten your password or username?"

msgid "Date/time"
msgstr "Date/time"

msgid "User"
msgstr "User"

msgid "Action"
msgstr "Action"

msgid ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."
msgstr ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."

msgid "Show all"
msgstr "Show all"

msgid "Save"
msgstr "Save"

msgid "Popup closing…"
msgstr ""

msgid "Search"
msgstr "Search"

#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] "%(counter)s result"
msgstr[1] "%(counter)s results"

#, python-format
msgid "%(full_result_count)s total"
msgstr "%(full_result_count)s total"

msgid "Save as new"
msgstr "Save as new"

msgid "Save and add another"
msgstr "Save and add another"

msgid "Save and continue editing"
msgstr "Save and continue editing"

msgid "Save and view"
msgstr ""

msgid "Close"
msgstr ""

#, python-format
msgid "Change selected %(model)s"
msgstr ""

#, python-format
msgid "Add another %(model)s"
msgstr ""

#, python-format
msgid "Delete selected %(model)s"
msgstr ""

msgid "Thanks for spending some quality time with the Web site today."
msgstr "Thanks for spending some quality time with the Web site today."

msgid "Log in again"
msgstr "Log in again"

msgid "Password change"
msgstr "Password change"

msgid "Your password was changed."
msgstr "Your password was changed."

msgid ""
"Please enter your old password, for security's sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""
"Please enter your old password, for security's sake, and then enter your new "
"password twice so we can verify you typed it in correctly."

msgid "Change my password"
msgstr "Change my password"

msgid "Password reset"
msgstr "Password reset"

msgid "Your password has been set.  You may go ahead and log in now."
msgstr "Your password has been set.  You may go ahead and log in now."

msgid "Password reset confirmation"
msgstr "Password reset confirmation"

msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."

msgid "New password:"
msgstr "New password:"

msgid "Confirm password:"
msgstr "Confirm password:"

msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."

msgid ""
"We've emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""

msgid ""
"If you don't receive an email, please make sure you've entered the address "
"you registered with, and check your spam folder."
msgstr ""

#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""

msgid "Please go to the following page and choose a new password:"
msgstr "Please go to the following page and choose a new password:"

msgid "Your username, in case you've forgotten:"
msgstr "Your username, in case you've forgotten:"

msgid "Thanks for using our site!"
msgstr "Thanks for using our site!"

#, python-format
msgid "The %(site_name)s team"
msgstr "The %(site_name)s team"

msgid ""
"Forgotten your password? Enter your email address below, and we'll email "
"instructions for setting a new one."
msgstr ""

msgid "Email address:"
msgstr ""

msgid "Reset my password"
msgstr "Reset my password"

msgid "All dates"
msgstr "All dates"

#, python-format
msgid "Select %s"
msgstr "Select %s"

#, python-format
msgid "Select %s to change"
msgstr "Select %s to change"

#, python-format
msgid "Select %s to view"
msgstr ""

msgid "Date:"
msgstr "Date:"

msgid "Time:"
msgstr "Time:"

msgid "Lookup"
msgstr "Lookup"

msgid "Currently:"
msgstr ""

msgid "Change:"
msgstr ""
