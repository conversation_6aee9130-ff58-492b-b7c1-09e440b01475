import{g as ve}from"./_commonjsHelpers.5.3.js";var z,I,$=0;function E(e){return e>=48&&e<=57}function L(e,t){for(var f=(e+="").length,n=(t+="").length,u=0,l=0;u<f&&l<n;){var h=e.charCodeAt(u),i=t.charCodeAt(l);if(E(h)){if(!E(i))return h-i;for(var s=u,o=l;h===48&&++s<f;)h=e.charCodeAt(s);for(;i===48&&++o<n;)i=t.charCodeAt(o);for(var a=s,c=o;a<f&&E(e.charCodeAt(a));)++a;for(;c<n&&E(t.charCodeAt(c));)++c;var r=a-s-c+o;if(r)return r;for(;s<a;)if(r=e.charCodeAt(s++)-t.charCodeAt(o++),r)return r;u=a,l=c;continue}if(h!==i)return h<$&&i<$&&I[h]!==-1&&I[i]!==-1?I[h]-I[i]:h-i;++u,++l}return u>=f&&l<n&&f>=n?-1:l>=n&&u<f&&n>=f?1:f-n}L.caseInsensitive=L.i=function(e,t){return L((""+e).toLowerCase(),(""+t).toLowerCase())};Object.defineProperties(L,{alphabet:{get:function(){return z},set:function(e){z=e,I=[];var t=0;if(z)for(;t<z.length;t++)I[z.charCodeAt(t)]=t;for($=I.length,t=0;t<$;t++)I[t]===void 0&&(I[t]=-1)}}});var ge=L,pe=function(e,t,f){return f?e.getElementsByClassName(t)[0]:e.getElementsByClassName(t)},me=function(e,t,f){return t="."+t,f?e.querySelector(t):e.querySelectorAll(t)},ye=function(e,t,f){for(var n=[],u="*",l=e.getElementsByTagName(u),h=l.length,i=new RegExp("(^|\\s)"+t+"(\\s|$)"),s=0,o=0;s<h;s++)if(i.test(l[s].className)){if(f)return l[s];n[o]=l[s],o++}return n},ne=function(){return function(e,t,f,n){return n=n||{},n.test&&n.getElementsByClassName||!n.test&&document.getElementsByClassName?pe(e,t,f):n.test&&n.querySelector||!n.test&&document.querySelector?me(e,t,f):ye(e,t,f)}}(),ae=function(t){for(var f=Array.prototype.slice.call(arguments,1),n=0,u;u=f[n];n++)if(u)for(var l in u)t[l]=u[l];return t},Ce=[].indexOf,ie=function(e,t){if(Ce)return e.indexOf(t);for(var f=0,n=e.length;f<n;++f)if(e[f]===t)return f;return-1},O={},se=function(t){if(typeof t=="undefined")return[];if(t===null)return[null];if(t===window)return[window];if(typeof t=="string")return[t];if(be(t))return t;if(typeof t.length!="number")return[t];if(typeof t=="function"&&t instanceof Function)return[t];for(var f=[],n=0,u=t.length;n<u;n++)(Object.prototype.hasOwnProperty.call(t,n)||n in t)&&f.push(t[n]);return f.length?f:[]};function be(e){return Object.prototype.toString.call(e)==="[object Array]"}var ue=window.addEventListener?"addEventListener":"attachEvent",we=window.removeEventListener?"removeEventListener":"detachEvent",fe=ue!=="addEventListener"?"on":"",oe=se;O.bind=function(e,t,f,n){e=oe(e);for(var u=0,l=e.length;u<l;u++)e[u][ue](fe+t,f,n||!1)};O.unbind=function(e,t,f,n){e=oe(e);for(var u=0,l=e.length;u<l;u++)e[u][we](fe+t,f,n||!1)};O.debounce=function(e,t,f){var n;return t?function(){var u=this,l=arguments,h=function(){n=null,f||e.apply(u,l)},i=f&&!n;clearTimeout(n),n=setTimeout(h,t),i&&e.apply(u,l)}:e};var le=function(e){return e=e===void 0?"":e,e=e===null?"":e,e=e.toString(),e},H=ie,Se=/\s+/,he=function(e){return new q(e)};function q(e){if(!e||!e.nodeType)throw new Error("A DOM element reference is required");this.el=e,this.list=e.classList}q.prototype.add=function(e){if(this.list)return this.list.add(e),this;var t=this.array(),f=H(t,e);return~f||t.push(e),this.el.className=t.join(" "),this};q.prototype.remove=function(e){if(this.list)return this.list.remove(e),this;var t=this.array(),f=H(t,e);return~f&&t.splice(f,1),this.el.className=t.join(" "),this};q.prototype.toggle=function(e,t){return this.list?(typeof t!="undefined"?t!==this.list.toggle(e,t)&&this.list.toggle(e):this.list.toggle(e),this):(typeof t!="undefined"?t?this.add(e):this.remove(e):this.has(e)?this.remove(e):this.add(e),this)};q.prototype.array=function(){var e=this.el.getAttribute("class")||"",t=e.replace(/^\s+|\s+$/g,""),f=t.split(Se);return f[0]===""&&f.shift(),f};q.prototype.has=q.prototype.contains=function(e){return this.list?this.list.contains(e):!!~H(this.array(),e)};var Ae=function(e,t){var f=e.getAttribute&&e.getAttribute(t)||null;if(!f)for(var n=e.attributes,u=n.length,l=0;l<u;l++)n[l]!==void 0&&n[l].nodeName===t&&(f=n[l].nodeValue);return f},T,K;function de(){return K||(K=1,T=function(e){return function(t,f,n){var u=this;this._values={},this.found=!1,this.filtered=!1;var l=function(h,i,s){if(i===void 0)s?u.values(h,s):u.values(h);else{u.elm=i;var o=e.templater.get(u,h);u.values(o)}};this.values=function(h,i){if(h!==void 0){for(var s in h)u._values[s]=h[s];i!==!0&&e.templater.set(u,u.values())}else return u._values},this.show=function(){e.templater.show(u)},this.hide=function(){e.templater.hide(u)},this.matching=function(){return e.filtered&&e.searched&&u.found&&u.filtered||e.filtered&&!e.searched&&u.filtered||!e.filtered&&e.searched&&u.found||!e.filtered&&!e.searched},this.visible=function(){return!!(u.elm&&u.elm.parentNode==e.list)},l(t,f,n)}}),T}var M,Q;function xe(){return Q||(Q=1,M=function(e){var t=function(f,n,u){var l=f.splice(0,50);u=u||[],u=u.concat(e.add(l)),f.length>0?setTimeout(function(){t(f,n,u)},1):(e.update(),n(u))};return t}),M}var R,U;function Ie(){if(U)return R;U=1;var e=he,t=O,f=ce();return R=function(n){var u=!1,l=function(i,s){if(n.page<1){n.listContainer.style.display="none",u=!0;return}else u&&(n.listContainer.style.display="block");var o,a=n.matchingItems.length,c=n.i,r=n.page,g=Math.ceil(a/r),C=Math.ceil(c/r),y=s.innerWindow||2,m=s.left||s.outerWindow||0,d=s.right||s.outerWindow||0;d=g-d,i.clear();for(var v=1;v<=g;v++){var p=C===v?"active":"";h.number(v,m,d,C,y)?(o=i.add({page:v,dotted:!1})[0],p&&e(o.elm).add(p),o.elm.firstChild.setAttribute("data-i",v),o.elm.firstChild.setAttribute("data-page",r)):h.dotted(i,v,m,d,C,y,i.size())&&(o=i.add({page:"...",dotted:!0})[0],e(o.elm).add("disabled"))}},h={number:function(i,s,o,a,c){return this.left(i,s)||this.right(i,o)||this.innerWindow(i,a,c)},left:function(i,s){return i<=s},right:function(i,s){return i>s},innerWindow:function(i,s,o){return i>=s-o&&i<=s+o},dotted:function(i,s,o,a,c,r,g){return this.dottedLeft(i,s,o,a,c,r)||this.dottedRight(i,s,o,a,c,r,g)},dottedLeft:function(i,s,o,a,c,r){return s==o+1&&!this.innerWindow(s,c,r)&&!this.right(s,a)},dottedRight:function(i,s,o,a,c,r,g){return i.items[g-1].values().dotted?!1:s==a&&!this.innerWindow(s,c,r)&&!this.right(s,a)}};return function(i){var s=new f(n.listContainer.id,{listClass:i.paginationClass||"pagination",item:i.item||"<li><a class='page' href='#'></a></li>",valueNames:["page","dotted"],searchClass:"pagination-search-that-is-not-supposed-to-exist",sortClass:"pagination-sort-that-is-not-supposed-to-exist"});t.bind(s.listContainer,"click",function(o){var a=o.target||o.srcElement,c=n.utils.getAttribute(a,"data-page"),r=n.utils.getAttribute(a,"data-i");r&&n.show((r-1)*c+1,c)}),n.on("updated",function(){l(s,i)}),l(s,i)}},R}var _,X;function qe(){return X||(X=1,_=function(e){var t=de()(e),f=function(l){for(var h=l.childNodes,i=[],s=0,o=h.length;s<o;s++)h[s].data===void 0&&i.push(h[s]);return i},n=function(l,h){for(var i=0,s=l.length;i<s;i++)e.items.push(new t(h,l[i]))},u=function(l,h){var i=l.splice(0,50);n(i,h),l.length>0?setTimeout(function(){u(l,h)},1):(e.update(),e.trigger("parseComplete"))};return e.handlers.parseComplete=e.handlers.parseComplete||[],function(){var l=f(e.list),h=e.valueNames;e.indexAsync?u(l,h):n(l,h)}}),_}var F,Y;function Oe(){if(Y)return F;Y=1;var e=function(t){var f,n=this,u=function(){var a;if(typeof t.item=="function"){f=function(c){var r=t.item(c);return i(r)};return}if(typeof t.item=="string"?t.item.indexOf("<")===-1?a=document.getElementById(t.item):a=i(t.item):a=h(),!a)throw new Error("The list needs to have at least one item on init otherwise you'll have to add a template.");a=l(a,t.valueNames),f=function(){return a.cloneNode(!0)}},l=function(a,c){var r=a.cloneNode(!0);r.removeAttribute("id");for(var g=0,C=c.length;g<C;g++){var y=void 0,m=c[g];if(m.data)for(var d=0,v=m.data.length;d<v;d++)r.setAttribute("data-"+m.data[d],"");else m.attr&&m.name?(y=t.utils.getByClass(r,m.name,!0),y&&y.setAttribute(m.attr,"")):(y=t.utils.getByClass(r,m,!0),y&&(y.innerHTML=""))}return r},h=function(){for(var a=t.list.childNodes,c=0,r=a.length;c<r;c++)if(a[c].data===void 0)return a[c].cloneNode(!0)},i=function(a){if(typeof a=="string"){if(/<tr[\s>]/g.exec(a)){var c=document.createElement("tbody");return c.innerHTML=a,c.firstElementChild}else if(a.indexOf("<")!==-1){var r=document.createElement("div");return r.innerHTML=a,r.firstElementChild}}},s=function(a){for(var c=0,r=t.valueNames.length;c<r;c++){var g=t.valueNames[c];if(g.data){for(var C=g.data,y=0,m=C.length;y<m;y++)if(C[y]===a)return{data:a}}else{if(g.attr&&g.name&&g.name==a)return g;if(g===a)return a}}},o=function(a,c,r){var g=void 0,C=s(c);C&&(C.data?a.elm.setAttribute("data-"+C.data,r):C.attr&&C.name?(g=t.utils.getByClass(a.elm,C.name,!0),g&&g.setAttribute(C.attr,r)):(g=t.utils.getByClass(a.elm,C,!0),g&&(g.innerHTML=r)))};this.get=function(a,c){n.create(a);for(var r={},g=0,C=c.length;g<C;g++){var y=void 0,m=c[g];if(m.data)for(var d=0,v=m.data.length;d<v;d++)r[m.data[d]]=t.utils.getAttribute(a.elm,"data-"+m.data[d]);else m.attr&&m.name?(y=t.utils.getByClass(a.elm,m.name,!0),r[m.name]=y?t.utils.getAttribute(y,m.attr):""):(y=t.utils.getByClass(a.elm,m,!0),r[m]=y?y.innerHTML:"")}return r},this.set=function(a,c){if(!n.create(a))for(var r in c)c.hasOwnProperty(r)&&o(a,r,c[r])},this.create=function(a){return a.elm!==void 0?!1:(a.elm=f(a.values()),n.set(a,a.values()),!0)},this.remove=function(a){a.elm.parentNode===t.list&&t.list.removeChild(a.elm)},this.show=function(a){n.create(a),t.list.appendChild(a.elm)},this.hide=function(a){a.elm!==void 0&&a.elm.parentNode===t.list&&t.list.removeChild(a.elm)},this.clear=function(){if(t.list.hasChildNodes())for(;t.list.childNodes.length>=1;)t.list.removeChild(t.list.firstChild)},u()};return F=function(t){return new e(t)},F}var P,Z;function ze(){return Z||(Z=1,P=function(e){var t,f,n,u={resetList:function(){e.i=1,e.templater.clear(),n=void 0},setOptions:function(i){i.length==2&&i[1]instanceof Array?t=i[1]:i.length==2&&typeof i[1]=="function"?(t=void 0,n=i[1]):i.length==3?(t=i[1],n=i[2]):t=void 0},setColumns:function(){e.items.length!==0&&t===void 0&&(t=e.searchColumns===void 0?u.toArray(e.items[0].values()):e.searchColumns)},setSearchString:function(i){i=e.utils.toString(i).toLowerCase(),i=i.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&"),f=i},toArray:function(i){var s=[];for(var o in i)s.push(o);return s}},l={list:function(){for(var i=[],s,o=f;(s=o.match(/"([^"]+)"/))!==null;)i.push(s[1]),o=o.substring(0,s.index)+o.substring(s.index+s[0].length);o=o.trim(),o.length&&(i=i.concat(o.split(/\s+/)));for(var a=0,c=e.items.length;a<c;a++){var r=e.items[a];if(r.found=!1,!!i.length){for(var g=0,C=i.length;g<C;g++){for(var y=!1,m=0,d=t.length;m<d;m++){var v=r.values(),p=t[m];if(v.hasOwnProperty(p)&&v[p]!==void 0&&v[p]!==null){var b=typeof v[p]!="string"?v[p].toString():v[p];if(b.toLowerCase().indexOf(i[g])!==-1){y=!0;break}}}if(!y)break}r.found=y}}},reset:function(){e.reset.search(),e.searched=!1}},h=function(i){return e.trigger("searchStart"),u.resetList(),u.setSearchString(i),u.setOptions(arguments),u.setColumns(),f===""?l.reset():(e.searched=!0,n?n(f,t):l.list()),e.update(),e.trigger("searchComplete"),e.visibleItems};return e.handlers.searchStart=e.handlers.searchStart||[],e.handlers.searchComplete=e.handlers.searchComplete||[],e.utils.events.bind(e.utils.getByClass(e.listContainer,e.searchClass),"keyup",e.utils.events.debounce(function(i){var s=i.target||i.srcElement,o=s.value===""&&!e.searched;o||h(s.value)},e.searchDelay)),e.utils.events.bind(e.utils.getByClass(e.listContainer,e.searchClass),"input",function(i){var s=i.target||i.srcElement;s.value===""&&h("")}),h}),P}var B,V;function Le(){return V||(V=1,B=function(e){return e.handlers.filterStart=e.handlers.filterStart||[],e.handlers.filterComplete=e.handlers.filterComplete||[],function(t){if(e.trigger("filterStart"),e.i=1,e.reset.filter(),t===void 0)e.filtered=!1;else{e.filtered=!0;for(var f=e.items,n=0,u=f.length;n<u;n++){var l=f[n];t(l)?l.filtered=!0:l.filtered=!1}}return e.update(),e.trigger("filterComplete"),e.visibleItems}}),B}var D,j;function Ee(){return j||(j=1,D=function(e){var t={els:void 0,clear:function(){for(var n=0,u=t.els.length;n<u;n++)e.utils.classes(t.els[n]).remove("asc"),e.utils.classes(t.els[n]).remove("desc")},getOrder:function(n){var u=e.utils.getAttribute(n,"data-order");return u=="asc"||u=="desc"?u:e.utils.classes(n).has("desc")?"asc":e.utils.classes(n).has("asc")?"desc":"asc"},getInSensitive:function(n,u){var l=e.utils.getAttribute(n,"data-insensitive");l==="false"?u.insensitive=!1:u.insensitive=!0},setOrder:function(n){for(var u=0,l=t.els.length;u<l;u++){var h=t.els[u];if(e.utils.getAttribute(h,"data-sort")===n.valueName){var i=e.utils.getAttribute(h,"data-order");i=="asc"||i=="desc"?i==n.order&&e.utils.classes(h).add(n.order):e.utils.classes(h).add(n.order)}}}},f=function(){e.trigger("sortStart");var n={},u=arguments[0].currentTarget||arguments[0].srcElement||void 0;u?(n.valueName=e.utils.getAttribute(u,"data-sort"),t.getInSensitive(u,n),n.order=t.getOrder(u)):(n=arguments[1]||n,n.valueName=arguments[0],n.order=n.order||"asc",n.insensitive=typeof n.insensitive=="undefined"?!0:n.insensitive),t.clear(),t.setOrder(n);var l=n.sortFunction||e.sortFunction||null,h=n.order==="desc"?-1:1,i;l?i=function(s,o){return l(s,o,n)*h}:i=function(s,o){var a=e.utils.naturalSort;return a.alphabet=e.alphabet||n.alphabet||void 0,!a.alphabet&&n.insensitive&&(a=e.utils.naturalSort.caseInsensitive),a(s.values()[n.valueName],o.values()[n.valueName])*h},e.items.sort(i),e.update(),e.trigger("sortComplete")};return e.handlers.sortStart=e.handlers.sortStart||[],e.handlers.sortComplete=e.handlers.sortComplete||[],t.els=e.utils.getByClass(e.listContainer,e.sortClass),e.utils.events.bind(t.els,"click",f),e.on("searchStart",t.clear),e.on("filterStart",t.clear),f}),D}var W,ee;function $e(){return ee||(ee=1,W=function(e,t,f){var n=f.location||0,u=f.distance||100,l=f.threshold||.4;if(t===e)return!0;if(t.length>32)return!1;var h=n,i=function(){var x={},S;for(S=0;S<t.length;S++)x[t.charAt(S)]=0;for(S=0;S<t.length;S++)x[t.charAt(S)]|=1<<t.length-S-1;return x}();function s(x,S){var J=x/t.length,G=Math.abs(h-S);return u?J+G/u:G?1:J}var o=l,a=e.indexOf(t,h);a!=-1&&(o=Math.min(s(0,a),o),a=e.lastIndexOf(t,h+t.length),a!=-1&&(o=Math.min(s(0,a),o)));var c=1<<t.length-1;a=-1;for(var r,g,C=t.length+e.length,y,m=0;m<t.length;m++){for(r=0,g=C;r<g;)s(m,h+g)<=o?r=g:C=g,g=Math.floor((C-r)/2+r);C=g;var d=Math.max(1,h-g+1),v=Math.min(h+g,e.length)+t.length,p=Array(v+2);p[v+1]=(1<<m)-1;for(var b=v;b>=d;b--){var w=i[e.charAt(b-1)];if(m===0?p[b]=(p[b+1]<<1|1)&w:p[b]=(p[b+1]<<1|1)&w|((y[b+1]|y[b])<<1|1)|y[b+1],p[b]&c){var A=s(m,b-1);if(A<=o)if(o=A,a=b-1,a>h)d=Math.max(1,2*h-a);else break}}if(s(m+1,h)>o)break;y=p}return!(a<0)}),W}var N,te;function Te(){if(te)return N;te=1;var e=O,t=ae,f=le,n=ne,u=$e();return N=function(l,h){h=h||{},h=t({location:0,distance:100,threshold:.4,multiSearch:!0,searchClass:"fuzzy-search"},h);var i={search:function(s,o){for(var a=h.multiSearch?s.replace(/ +$/,"").split(/ +/):[s],c=0,r=l.items.length;c<r;c++)i.item(l.items[c],o,a)},item:function(s,o,a){for(var c=!0,r=0;r<a.length;r++){for(var g=!1,C=0,y=o.length;C<y;C++)i.values(s.values(),o[C],a[r])&&(g=!0);g||(c=!1)}s.found=c},values:function(s,o,a){if(s.hasOwnProperty(o)){var c=f(s[o]).toLowerCase();if(u(c,a,h))return!0}return!1}};return e.bind(n(l.listContainer,h.searchClass),"keyup",l.utils.events.debounce(function(s){var o=s.target||s.srcElement;l.search(o.value,i.search)},l.searchDelay)),function(s,o){l.search(s,o,i.search)}},N}var k,re;function ce(){if(re)return k;re=1;var e=ge,t=ne,f=ae,n=ie,u=O,l=le,h=he,i=Ae,s=se;return k=function(o,a,c){var r=this,g,C=de()(r),y=xe()(r),m=Ie()(r);g={start:function(){r.listClass="list",r.searchClass="search",r.sortClass="sort",r.page=1e4,r.i=1,r.items=[],r.visibleItems=[],r.matchingItems=[],r.searched=!1,r.filtered=!1,r.searchColumns=void 0,r.searchDelay=0,r.handlers={updated:[]},r.valueNames=[],r.utils={getByClass:t,extend:f,indexOf:n,events:u,toString:l,naturalSort:e,classes:h,getAttribute:i,toArray:s},r.utils.extend(r,a),r.listContainer=typeof o=="string"?document.getElementById(o):o,r.listContainer&&(r.list=t(r.listContainer,r.listClass,!0),r.parse=qe()(r),r.templater=Oe()(r),r.search=ze()(r),r.filter=Le()(r),r.sort=Ee()(r),r.fuzzySearch=Te()(r,a.fuzzySearch),this.handlers(),this.items(),this.pagination(),r.update())},handlers:function(){for(var d in r.handlers)r[d]&&r.handlers.hasOwnProperty(d)&&r.on(d,r[d])},items:function(){r.parse(r.list),c!==void 0&&r.add(c)},pagination:function(){if(a.pagination!==void 0){a.pagination===!0&&(a.pagination=[{}]),a.pagination[0]===void 0&&(a.pagination=[a.pagination]);for(var d=0,v=a.pagination.length;d<v;d++)m(a.pagination[d])}}},this.reIndex=function(){r.items=[],r.visibleItems=[],r.matchingItems=[],r.searched=!1,r.filtered=!1,r.parse(r.list)},this.toJSON=function(){for(var d=[],v=0,p=r.items.length;v<p;v++)d.push(r.items[v].values());return d},this.add=function(d,v){if(d.length!==0){if(v){y(d.slice(0),v);return}var p=[],b=!1;d[0]===void 0&&(d=[d]);for(var w=0,A=d.length;w<A;w++){var x=null;b=r.items.length>r.page,x=new C(d[w],void 0,b),r.items.push(x),p.push(x)}return r.update(),p}},this.show=function(d,v){return this.i=d,this.page=v,r.update(),r},this.remove=function(d,v,p){for(var b=0,w=0,A=r.items.length;w<A;w++)r.items[w].values()[d]==v&&(r.templater.remove(r.items[w],p),r.items.splice(w,1),A--,w--,b++);return r.update(),b},this.get=function(d,v){for(var p=[],b=0,w=r.items.length;b<w;b++){var A=r.items[b];A.values()[d]==v&&p.push(A)}return p},this.size=function(){return r.items.length},this.clear=function(){return r.templater.clear(),r.items=[],r},this.on=function(d,v){return r.handlers[d].push(v),r},this.off=function(d,v){var p=r.handlers[d],b=n(p,v);return b>-1&&p.splice(b,1),r},this.trigger=function(d){for(var v=r.handlers[d].length;v--;)r.handlers[d][v](r);return r},this.reset={filter:function(){for(var d=r.items,v=d.length;v--;)d[v].filtered=!1;return r},search:function(){for(var d=r.items,v=d.length;v--;)d[v].found=!1;return r}},this.update=function(){var d=r.items,v=d.length;r.visibleItems=[],r.matchingItems=[],r.templater.clear();for(var p=0;p<v;p++)d[p].matching()&&r.matchingItems.length+1>=r.i&&r.visibleItems.length<r.page?(d[p].show(),r.visibleItems.push(d[p]),r.matchingItems.push(d[p])):(d[p].matching()&&r.matchingItems.push(d[p]),d[p].hide());return r.trigger("updated"),r},g.start()},k}var Me=ce();const _e=ve(Me);export{_e as L};
