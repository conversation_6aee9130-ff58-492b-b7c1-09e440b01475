## [Unreleased]

### Added

- Added staff registration page with automatic username generation and role selection
- Enhanced staff list page with Ant Design components and table
- Added "Thêm nhân viên" button for direct access to registration
- Created reusable StaffTable component for better maintainability
- Implemented comprehensive password validation:
  - Minimum length of 8 characters
  - Prevention of common/easily guessed passwords
  - No numeric-only passwords
  - No similarity with user attributes (username, email, name)
- Added consistent role names across the application:
  - Matched backend ROLE_CHOICES
  - Updated role display in staff list and creation form

### Changed

- Restricted product creation and editing to users with sales_manager role
- Added permission check for product management actions
- Added read-only view for product details for non-sales_manager users
- Improved user feedback for unauthorized product actions
- Enhanced sales_manager permissions to allow updating order status to any state in order confirmation workflow
- Updated CreateOrderPaymentMethod component to use Ant Design components
  - Replaced native select elements with antd Select
  - Replaced checkbox with antd Checkbox
  - Updated layout using antd Typography
