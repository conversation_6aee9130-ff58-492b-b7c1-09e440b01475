import { Order } from "../../types/order";
import { OrderCardHeader } from "./OrderCardHeader";
import { OrderCardCustomerInfo } from "./OrderCardCustomerInfo";
import { OrderCardOrderInfo } from "./OrderCardOrderInfo";
import { OrderCardShippingInfo } from "./OrderCardShippingInfo";
import { OrderCardFooter } from "./OrderCardFooter";

interface OrderCardProps {
  order: Order;
  onUpdateStatus: (orderId: number, status: Order["status"]) => void;
  onViewDetails: (orderId: number) => void;
  isDesktop?: boolean;
  isFirst?: boolean;
}

export function OrderCard({ 
  order, 
  onUpdateStatus, 
  onViewDetails,
  isDesktop = false,
  isFirst = false 
}: OrderCardProps) {
  return (
    <div className={`bg-background border overflow-hidden ${
      isDesktop 
        ? "grid grid-cols-[1fr,2fr,2fr,1.5fr,1fr] items-start gap-6 px-6 py-4" + 
          (isFirst ? " rounded-t-lg border-b-0" : " border-t-0 last:rounded-b-lg")
        : "rounded-lg divide-y"
    }`}>
      <OrderCardHeader 
        order={order}
        onUpdateStatus={onUpdateStatus}
        isDesktop={isDesktop}
      />

      <OrderCardCustomerInfo 
        order={order}
        isDesktop={isDesktop}
      />

      <OrderCardOrderInfo 
        order={order}
        isDesktop={isDesktop}
      />

      <OrderCardShippingInfo 
        order={order}
        onUpdateStatus={onUpdateStatus}
        isDesktop={isDesktop}
      />

      <OrderCardFooter 
        notes={order.notes}
        onViewDetails={() => onViewDetails(order.id)}
        isDesktop={isDesktop}
      />
    </div>
  );
}
