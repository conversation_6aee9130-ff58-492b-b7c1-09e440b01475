import React from 'react';
import { <PERSON><PERSON>, Checkbox, <PERSON><PERSON>, Divider } from 'antd';
import { AVAILABLE_SINGLE_ORDER_EXPORT_FIELDS } from '@/constants/constants';

interface SingleOrderFieldSelectionModalProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  selectedFields: string[];
  onFieldsChange: (fields: string[]) => void;
  isExporting: boolean;
}

const SingleOrderFieldSelectionModal: React.FC<SingleOrderFieldSelectionModalProps> = ({
  visible,
  onOk,
  onCancel,
  selectedFields,
  onFieldsChange,
  isExporting
}) => {
  const totalFields = AVAILABLE_SINGLE_ORDER_EXPORT_FIELDS.length;
  const selectedCount = selectedFields.length;
  const isAllSelected = selectedCount === totalFields;

  const handleSelectAll = () => {
    if (isAllSelected) {
      onFieldsChange([]);
    } else {
      onFieldsChange(AVAILABLE_SINGLE_ORDER_EXPORT_FIELDS.map(field => field.value));
    }
  };

  return (
    <Modal
      title="Chọn thông tin để xuất"
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      okText="Xuất Excel"
      cancelText="Hủy"
      width={900}
      confirmLoading={isExporting}
    >
      <div className="mb-4">
        <p className="mb-3">Vui lòng chọn các thông tin bạn muốn bao gồm trong file Excel:</p>

        {/* Summary and Select All Section */}
        <div className="flex justify-between items-center mb-3 p-3 bg-gray-50 rounded">
          <div className="text-sm text-gray-600">
            <span className="font-medium text-blue-600">{selectedCount}</span> / {totalFields} trường đã chọn
          </div>
          <Button
            type={isAllSelected ? "default" : "primary"}
            size="small"
            onClick={handleSelectAll}
          >
            {isAllSelected ? "Bỏ chọn tất cả" : "Chọn tất cả"}
          </Button>
        </div>

        <Divider className="my-3" />
      </div>

      <Checkbox.Group
        className="w-full"
        value={selectedFields}
        onChange={(checkedValues) => onFieldsChange(checkedValues as string[])}
      >
        <div className="grid grid-cols-3 gap-x-4 gap-y-2">
          {AVAILABLE_SINGLE_ORDER_EXPORT_FIELDS.map(field => (
            <Checkbox key={field.value} value={field.value} className="mb-2">
              {field.label}
            </Checkbox>
          ))}
        </div>
      </Checkbox.Group>
    </Modal>
  );
};

export default SingleOrderFieldSelectionModal;
