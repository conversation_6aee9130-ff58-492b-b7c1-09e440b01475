import React, { useState } from "react";
import { Card, Button, Typography, Modal, Space, Tag } from "antd";
import { ShopOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

export type ShowroomOrderType =
  | "normal_order"
  | "warehouse_to_showroom"
  | "normal_revenue"
  | "combined_orders";

interface ShowroomOrderSectionProps {
  isShowroom: boolean;
  onShowroomChange: (value: boolean, skipItemHandling?: boolean) => void;
  onShowroomOrderTypeSelect?: (type: ShowroomOrderType) => void;
  selectedOrderType?: ShowroomOrderType | null;
}

const ShowroomOrderSection: React.FC<ShowroomOrderSectionProps> = ({
  isShowroom,
  onShowroomChange,
  onShowroomOrderTypeSelect,
  selectedOrderType,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleShowroomButtonClick = () => {
    setIsModalOpen(true);
  };

  const handleOptionSelect = (type: ShowroomOrderType) => {
    setIsModalOpen(false);

    // Update form state based on option type
    switch (type) {
      case "normal_order":
        // Default: Normal order (is_showroom = false) - skip item handling
        onShowroomChange(false, true);
        break;
      case "warehouse_to_showroom":
        // Option 1: Existing showroom flow (is_showroom = true, price = 0)
        onShowroomChange(true);
        break;
      case "normal_revenue":
        // Option 2: Normal revenue order (is_showroom = false) - skip item handling
        onShowroomChange(false, true);
        break;
      case "combined_orders":
        // Option 3: Create 2 orders (is_showroom = false) - skip item handling
        onShowroomChange(false, true);
        break;
    }

    if (onShowroomOrderTypeSelect) {
      onShowroomOrderTypeSelect(type);
    }
  };

  const getOrderTypeDisplay = () => {
    if (!selectedOrderType) {
      return {
        text: "Chưa chọn loại đơn hàng",
        color: "default",
        icon: null,
      };
    }

    switch (selectedOrderType) {
      case "normal_order":
        return {
          text: "Đơn hàng bình thường",
          color: "cyan",
          icon: "📋",
        };
      case "warehouse_to_showroom":
        return {
          text: "Option 1: Nhập hàng từ kho về showroom (0 đồng)",
          color: "blue",
          icon: "📦",
        };
      case "normal_revenue":
        return {
          text: "Option 2: Showroom đã đủ hàng, tạo đơn doanh thu bình thường",
          color: "green",
          icon: "💰",
        };
      case "combined_orders":
        return {
          text: "Option 3: Showroom không đủ hàng, tạo 2 đơn hàng",
          color: "orange",
          icon: "🔄",
        };
      default:
        return {
          text: "Đơn hàng bình thường",
          color: "default",
          icon: null,
        };
    }
  };

  return (
    <Card style={{ marginTop: "16px" }}>
      <Title level={4}>Loại đơn hàng</Title>
      <Space direction="vertical" style={{ width: "100%" }}>
        {(() => {
          const display = getOrderTypeDisplay();
          return (
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              {display.icon && <span>{display.icon}</span>}
              <Tag color={display.color as any} style={{ margin: 0 }}>
                {display.text}
              </Tag>
            </div>
          );
        })()}
        <Button
          type="primary"
          icon={<ShopOutlined />}
          onClick={handleShowroomButtonClick}
          style={{ width: "100%" }}
        >
          Đơn của showroom
        </Button>
      </Space>

      <Modal
        title="Chọn loại đơn hàng"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={600}
      >
        <Space direction="vertical" style={{ width: "100%" }} size="large">
          <Card
            hoverable
            onClick={() => handleOptionSelect("normal_order")}
            style={{
              cursor: "pointer",
              border:
                selectedOrderType === "normal_order"
                  ? "2px solid #1890ff"
                  : undefined,
            }}
          >
            <Title level={5}>📋 Đơn hàng bình thường (Mặc định)</Title>
            <Text type="secondary">
              Tạo đơn hàng bán hàng bình thường cho khách hàng với giá đầy đủ.
              Đây là loại đơn hàng thông thường nhất.
            </Text>
          </Card>

          <Card
            hoverable
            onClick={() => handleOptionSelect("warehouse_to_showroom")}
            style={{
              cursor: "pointer",
              border:
                selectedOrderType === "warehouse_to_showroom"
                  ? "2px solid #1890ff"
                  : undefined,
            }}
          >
            <Title level={5}>
              📦 Option 1: Nhập hàng từ kho về showroom (0 đồng)
            </Title>
            <Text type="secondary">
              Tạo đơn nhập hàng từ kho về showroom với giá 0 đồng. Sử dụng cho
              việc bổ sung hàng hóa cho showroom.
            </Text>
          </Card>

          <Card
            hoverable
            onClick={() => handleOptionSelect("normal_revenue")}
            style={{
              cursor: "pointer",
              border:
                selectedOrderType === "normal_revenue"
                  ? "2px solid #1890ff"
                  : undefined,
            }}
          >
            <Title level={5}>
              💰 Option 2: Showroom đã đủ hàng, tạo đơn doanh thu bình thường
            </Title>
            <Text type="secondary">
              Showroom đã có đủ hàng, tạo đơn bán hàng bình thường cho khách
              hàng với giá đầy đủ.
            </Text>
          </Card>

          <Card
            hoverable
            onClick={() => handleOptionSelect("combined_orders")}
            style={{
              cursor: "pointer",
              border:
                selectedOrderType === "combined_orders"
                  ? "2px solid #1890ff"
                  : undefined,
            }}
          >
            <Title level={5}>
              🔄 Option 3: Showroom không đủ hàng, đặt hàng từ kho về showroom
              và bán cho khách hàng
            </Title>
            <Text type="secondary">
              Tạo 2 đơn hàng: Đầu tiên từ kho về showroom, sau đó từ showroom
              tới khách hàng. Cả 2 đơn sẽ có nội dung giống nhau.
            </Text>
          </Card>
        </Space>
      </Modal>
    </Card>
  );
};

export default ShowroomOrderSection;
