import { Staff } from "./staff";

export type ShowroomStatus = "transfer_to_warehouse" | "transfer_to_showroom" | "cancel_damaged" | "showroom_order" | null;

export interface OrderItem {
  id: number;
  product: number;
  product_name: string;
  product_code?: string;
  variant?: number;
  variant_name?: string;
  quantity: number;
  price: number;
  total_price: number;
  product_weight?: number;
}

export interface Order {
  id: number;
  user: {
    id: number;
    email: string;
    username: string;
    full_name: string;
    rank: "gold" | "silver" | "normal";
  };
  // status: "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "returned" | "refunded";
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "returned";
  status_display: string;
  total_price: number;
  final_total: number;
  shipping_address: string;
  ward?: string;
  district?: string;
  city?: string;
  phone_number: string;
  email: string;
  created_at: string;
  updated_at: string;
  confirmation_time?: string; // Thời gian xác nhận đơn hàng
  completion_time?: string; // Thời gian hoàn thành đơn hàng
  sales_admin?: Staff;
  delivery_staff?: Staff;
  delivery_date?: string;
  delivery_time?: string;
  shipping_unit?: "company_vehicle" | "motorbike" | "grab" | "transport_partner" | "shipping_partner";
  shipping_fee: number;
  payment_method?: "cod" | "cash" | "bank_transfer";
  payment_status: "paid" | "unpaid";
  company_payment_received: boolean;
  discount: number;
  tax: number;
  notes: string;
  items: OrderItem[];
  is_showroom: boolean;
  showroom_status: ShowroomStatus;
  showroom_status_display?: string;
}

export const SHIPPING_UNIT_OPTIONS = [
  { value: "company_vehicle", label: "Xe Cty" },
  { value: "motorbike", label: "Xe máy" },
  { value: "grab", label: "Grab" },
  { value: "transport_partner", label: "Chành xe" },
  { value: "shipping_partner", label: "ĐVVC" },
] as const;

export const PAYMENT_METHOD_OPTIONS = [
  { value: "cod", label: "COD" },
  { value: "cash", label: "Tiền mặt" },
  { value: "bank_transfer", label: "Chuyển Khoản" },
] as const;

export const SHOWROOM_STATUS_OPTIONS = [
  { value: "transfer_to_warehouse", label: "Điều chuyển hàng từ SR về Kho" },
  { value: "transfer_to_showroom", label: "Điều chuyển hàng từ Kho về SR" },
  { value: "cancel_damaged", label: "Huỷ hàng hư hỏng của SR" },
  { value: "showroom_order", label: "Đặt hàng của SR" },
] as const;

export const PAYMENT_STATUS_OPTIONS = [
  { value: "paid", label: "Đã thanh toán" },
  { value: "unpaid", label: "Chưa thanh toán" },
] as const;

export const STATUS_OPTIONS = [
  { value: "pending", label: "Chờ xác nhận" },
  { value: "processing", label: "Đang xử lý" },
  { value: "shipped", label: "Đang giao hàng" },
  { value: "delivered", label: "Hoàn thành" },
  { value: "cancelled", label: "Hủy bỏ" },
  { value: "returned", label: "Đã trả hàng" },
  // { value: "refunded", label: "Đã hoàn tiền" },
] as const;
