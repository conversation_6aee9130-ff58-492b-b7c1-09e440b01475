# This file is distributed under the same license as the Django package.
#
# Translators:
# Bayarkhuu Bataa, 2014
# Bayarkhuu Bataa, 2013
# Turmunkh Batkhuyag, 2023
# Zorig, 2013
# Zorig, 2019
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2023-12-24 18:40+0000\n"
"Last-Translator: Turmunkh Batkhuyag, 2023\n"
"Language-Team: Mongolian (http://app.transifex.com/django/django/language/"
"mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr "Хүнчлэх"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{} дэх"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{} дэх"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{} дэх"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{} дахь"

#. Translators: Ordinal format when value ends with 3, e.g. 83rd, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{} дахь"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{} дэх"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{} дахь"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{} дахь"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{} дахь"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{} дахь"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{} дэх"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s сая"
msgstr[1] "%(value)s сая"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s тэрбум"
msgstr[1] "%(value)s тэрбум"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s наяд"
msgstr[1] "%(value)s их наяд"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s тунамал"
msgstr[1] "%(value)s тунамал"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s их ингүүмэл "
msgstr[1] "%(value)s их ингүүмэл "

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s ялгаруулагч"
msgstr[1] "%(value)s ялгаруулагч"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s их өөр дээр "
msgstr[1] "%(value)s их өөр дээр "

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s өөр дээр "
msgstr[1] "%(value)s өөр дээр "

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s их шалтгааны үзэгдэл"
msgstr[1] "%(value)s их шалтгааны үзэгдэл"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s эрхэт"
msgstr[1] "%(value)s эрхэт"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"

msgid "one"
msgstr "нэг"

msgid "two"
msgstr "хоёр"

msgid "three"
msgstr "гурав"

msgid "four"
msgstr "дөрөв"

msgid "five"
msgstr "тав"

msgid "six"
msgstr "зургаа"

msgid "seven"
msgstr "долоо"

msgid "eight"
msgstr "найм"

msgid "nine"
msgstr "ес"

msgid "today"
msgstr "өнөөдөр"

msgid "tomorrow"
msgstr "маргааш"

msgid "yesterday"
msgstr "өчигдөр"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s ѳмнѳ"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "1 цагийн өмнө"
msgstr[1] "%(count)s цагийн өмнө"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "1 минутын өмнө"
msgstr[1] "%(count)s минутын өмнө"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "1 секундын өмнө"
msgstr[1] "%(count)s секундын өмнө"

msgid "now"
msgstr "одоо"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "1 секундын дараа"
msgstr[1] "%(count)s секундын дараа"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "1 минутын дараа"
msgstr[1] "%(count)s минутын дараа"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "1 цагийн дараа"
msgstr[1] "%(count)s цагийн дараа"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "одооноос %(delta)s "

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d жил"
msgstr[1] "%(num)d жил"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d сар"
msgstr[1] "%(num)d сар"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d долоо хоног"
msgstr[1] "%(num)d долоо хоног"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d өдөр"
msgstr[1] "%(num)d өдөр"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d цаг"
msgstr[1] "%(num)d цаг"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d минут"
msgstr[1] "%(num)d минут"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d жил"
msgstr[1] "%(num)d жил"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d сар"
msgstr[1] "%(num)d сар"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d долоо хоног"
msgstr[1] "%(num)d долоо хоног"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d өдөр"
msgstr[1] "%(num)d өдөр"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d цаг"
msgstr[1] "%(num)d цаг"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d минут"
msgstr[1] "%(num)d минут"
