@import "../../../node_modules/bootstrap/scss/functions";

$orange: rgb(255, 80, 1);
$blue: rgb(3, 68, 220);
$green: rgb(127, 176, 105);
$primary: $blue;
$dark: rgb(1, 32, 63);
$dark-lightened: rgba(1, 32, 63, 0.75);
$secondary: $orange;
$warning: $orange;
$danger: $orange;
$success: $green;
$info: rgb(106, 141, 146);
$code-color: $info;
$font-size-base: .8rem;

.btn-secondary, .btn-info {
    --bs-btn-color: white !important;
}

.table-active {
    --bs-table-bg-state: rgb(127, 176, 105) !important;
}

$card-border-radius: 0;
$card-spacer-x: 0;
.card-header {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

.card {
    border-top: 0 !important;
}


