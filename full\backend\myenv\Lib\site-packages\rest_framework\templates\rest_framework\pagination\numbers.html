<ul class="pagination">
  {% if previous_url %}
    <li>
      <a href="{{ previous_url }}" aria-label="Previous">
        <span aria-hidden="true">&laquo;</span>
      </a>
    </li>
  {% else %}
    <li class="disabled">
      <a href="#" aria-label="Previous">
        <span aria-hidden="true">&laquo;</span>
      </a>
    </li>
  {% endif %}

  {% for page_link in page_links %}
    {% if page_link.is_break %}
      <li class="disabled">
        <a href="#"><span aria-hidden="true">&hellip;</span></a>
      </li>
    {% else %}
      {% if page_link.is_active %}
        <li class="active">
          <a href="{{ page_link.url }}">{{ page_link.number }}</a>
        </li>
      {% else %}
        <li>
          <a href="{{ page_link.url }}">{{ page_link.number }}</a>
        </li>
      {% endif %}
    {% endif %}
  {% endfor %}

  {% if next_url %}
    <li>
      <a href="{{ next_url }}" aria-label="Next">
        <span aria-hidden="true">&raquo;</span>
      </a>
    </li>
  {% else %}
    <li class="disabled">
      <a href="#" aria-label="Next">
        <span aria-hidden="true">&raquo;</span>
      </a>
    </li>
  {% endif %}
</ul>
