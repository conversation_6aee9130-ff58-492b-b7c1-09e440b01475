import { <PERSON>, Row, Col, Typography, Statistic } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Tooltip } from "recharts";
import { useEffect, useState } from "react";
import dayjs from "dayjs";
import { apiCall, endpoints } from "@/lib/api";

import { DateRangePickerWithPresets } from "../common/DateRangePickerWithPresets";
const { Title } = Typography;
const COLORS = ["#52c41a", "#1677ff"];

export interface CustomerRevenueData {
  start_date: string;
  end_date: string;
  new_customers: {
    count: number;
    total_revenue: number;
  };
  returning_customers: {
    count: number;
    total_revenue: number;
  };
}

export function CustomerRevenuePieChart() {
  const [data, setData] = useState<CustomerRevenueData | null>(null);

  const fetchData = async (startDate: string, endDate: string) => {
    try {
      const response = await apiCall<CustomerRevenueData>(
        "GET",
        endpoints.reports.compareCustomerRevenue +
          `?start_date=${startDate}&end_date=${endDate}`
      );
      setData(response);
    } catch (error) {
      console.error("Error fetching revenue data:", error);
    }
  };

  useEffect(() => {
    const today = dayjs();
    const startDate = today.subtract(30, "day").format("YYYY-MM-DD");
    const endDate = today.format("YYYY-MM-DD");
    fetchData(startDate, endDate);
  }, []);

  const pieData = data
    ? [
        {
          name: "Khách hàng mới",
          value: data.new_customers.total_revenue,
        },
        {
          name: "Khách hàng cũ",
          value: data.returning_customers.total_revenue,
        },
      ]
    : [];

  return (
    <Card>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={8}>
          <Statistic
            title="Tổng Doanh Thu"
            value={
              data
                ? data.new_customers.total_revenue +
                  data.returning_customers.total_revenue
                : 0
            }
            precision={0}
            formatter={(value) =>
              new Intl.NumberFormat("vi-VN", {
                maximumFractionDigits: 0,
              }).format(Number(value)) + " ₫"
            }
          />
        </Col>
        <Col xs={24} md={16}>
          <Row justify="end">
            <DateRangePickerWithPresets
              className="justify-end"
              onChange={(dates) => {
                if (dates[0] && dates[1]) {
                  fetchData(dates[0], dates[1]);
                }
              }}
            />
          </Row>
        </Col>
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Row gutter={[0, 16]}>
                <Col span={24}>
                  <Statistic
                    title="Tổng Khách Hàng"
                    value={
                      data
                        ? data.new_customers.count +
                          data.returning_customers.count
                        : 0
                    }
                    formatter={(value) =>
                      new Intl.NumberFormat("vi-VN", {
                        maximumFractionDigits: 0,
                      }).format(Number(value))
                    }
                  />
                </Col>
                {data && (
                  <>
                    <Col span={24} style={{ marginBottom: 8 }}>
                      <Typography.Text type="secondary">
                        Thời gian: {dayjs(data.start_date).format("DD/MM/YYYY")}{" "}
                        - {dayjs(data.end_date).format("DD/MM/YYYY")}
                      </Typography.Text>
                    </Col>
                    <Col span={24}>
                      <Title level={5}>Chi tiết khách hàng:</Title>
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 12,
                        }}
                      >
                        <div>
                          <span
                            style={{
                              display: "inline-block",
                              width: 12,
                              height: 12,
                              backgroundColor: COLORS[0],
                              marginRight: 8,
                            }}
                          ></span>
                          <span style={{ fontSize: "1.1em" }}>
                            Khách hàng mới: {data.new_customers.count} (
                            {(
                              (data.new_customers.count /
                                (data.new_customers.count +
                                  data.returning_customers.count)) *
                              100
                            ).toFixed(1)}
                            %)
                          </span>
                        </div>
                        <div>
                          <span
                            style={{
                              display: "inline-block",
                              width: 12,
                              height: 12,
                              backgroundColor: COLORS[1],
                              marginRight: 8,
                            }}
                          ></span>
                          <span style={{ fontSize: "1.1em" }}>
                            Khách hàng cũ: {data.returning_customers.count} (
                            {(
                              (data.returning_customers.count /
                                (data.new_customers.count +
                                  data.returning_customers.count)) *
                              100
                            ).toFixed(1)}
                            %)
                          </span>
                        </div>
                      </div>
                    </Col>
                  </>
                )}
              </Row>
            </Col>
            <Col xs={24} md={16}>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={150}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) =>
                      new Intl.NumberFormat("vi-VN", {
                        maximumFractionDigits: 0,
                      }).format(Number(value)) + " ₫"
                    }
                  />
                </PieChart>
              </ResponsiveContainer>
            </Col>
          </Row>
        </Col>
      </Row>
    </Card>
  );
}
