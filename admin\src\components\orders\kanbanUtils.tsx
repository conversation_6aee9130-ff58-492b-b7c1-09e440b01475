import {
  CircleEllipsis,
  Package,
  Truck,
  Receipt,
  Ban,
  User,
} from "lucide-react";
import { Order } from "../../types/order";
import React from "react";

// Custom status types for the Kanban board
export type KanbanStatus =
  | "pending"
  | "processing"
  | "shipped_staff"
  | "shipped_other"
  | "delivered"
  | "cancelled";

// Map Kanban status to API status
export const mapKanbanToApiStatus = (status: KanbanStatus): Order["status"] => {
  if (status === "shipped_staff" || status === "shipped_other") {
    return "shipped";
  }
  return status as Order["status"];
};

// Map API status to Kanban status (default mapping)
export const mapApiToKanbanStatus = (order: Order): KanbanStatus => {
  if (order.status === "shipped") {
    if (order.delivery_staff) {
      return "shipped_staff";
    } else {
      // Default to "shipped_other" for any order without delivery staff
      // This includes orders with shipping_unit other than motorbike
      // and orders with no shipping_unit selected
      return "shipped_other";
    }
  }
  return order.status as KanbanStatus;
};

// Filter orders by Kanban status
export const filterOrdersByKanbanStatus = (
  orders: Order[],
  status: KanbanStatus
): Order[] => {
  switch (status) {
    case "shipped_staff":
      return orders.filter(
        (order) => order.status === "shipped" && order.delivery_staff
      );
    case "shipped_other":
      return orders.filter(
        (order) => order.status === "shipped" && !order.delivery_staff
      );
    default:
      return orders.filter((order) => order.status === status);
  }
};

// Kanban status options
export const KANBAN_STATUS_OPTIONS = [
  {
    value: "pending",
    label: "Chờ xác nhận",
    color: "#faad14",
    icon: CircleEllipsis,
  },
  { value: "processing", label: "Đang xử lý", color: "#1677ff", icon: Package },
  {
    value: "shipped_staff",
    label: "Đang giao hàng - Nhân viên",
    color: "#722ed1",
    icon: User,
  },
  {
    value: "shipped_other",
    label: "Đang giao hàng - Đơn vị khác",
    color: "#eb2f96",
    icon: Truck,
  },
  { value: "delivered", label: "Hoàn thành", color: "#52c41a", icon: Receipt },
  { value: "cancelled", label: "Hủy bỏ", color: "#ff4d4f", icon: Ban },
] as const;

// Get status icon
export const getKanbanStatusIcon = (status: KanbanStatus) => {
  const statusOption = KANBAN_STATUS_OPTIONS.find(
    (option) => option.value === status
  );
  if (!statusOption) return null;

  const Icon = statusOption.icon;
  return <Icon size={20} />;
};

// Get status color
export const getKanbanStatusColor = (status: KanbanStatus) => {
  const statusOption = KANBAN_STATUS_OPTIONS.find(
    (option) => option.value === status
  );
  return statusOption?.color || "#f5f5f5";
};

// Count orders by Kanban status
export const countOrdersByKanbanStatus = (
  orders: Order[]
): Record<KanbanStatus, number> => {
  const counts = {
    pending: 0,
    processing: 0,
    shipped_staff: 0,
    shipped_other: 0,
    delivered: 0,
    cancelled: 0,
  };

  orders.forEach((order) => {
    const kanbanStatus = mapApiToKanbanStatus(order);
    counts[kanbanStatus]++;
  });

  return counts;
};
