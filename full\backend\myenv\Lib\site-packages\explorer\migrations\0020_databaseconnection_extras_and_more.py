# Generated by Django 5.0.4 on 2024-07-08 01:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('explorer', '0019_alter_databaseconnection_engine'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='databaseconnection',
            name='extras',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='databaseconnection',
            name='engine',
            field=models.CharField(choices=[('django.db.backends.sqlite3', 'SQLite3'), ('django.db.backends.postgresql', 'PostgreSQL'), ('django.db.backends.mysql', 'MySQL'), ('django.db.backends.oracle', 'Oracle'), ('django.db.backends.mysql', 'MariaDB'), ('django_cockroachdb', 'CockroachDB'), ('mssql', 'SQL Server (mssql-django)'), ('django_snowflake', 'Snowflake')], max_length=255),
        ),
    ]
