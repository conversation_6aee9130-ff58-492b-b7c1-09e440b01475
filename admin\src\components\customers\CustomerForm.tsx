import { CreateCustomerData } from "../../types/customer";

interface CustomerFormProps {
  formData: CreateCustomerData;
  onChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  loading?: boolean;
  onSubmit: (e: React.FormEvent) => void;
  submitLabel?: string;
}

export function CustomerForm({
  formData,
  onChange,
  loading = false,
  onSubmit,
  submitLabel = "Tạo khách hàng",
}: CustomerFormProps) {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Tên</label>
          <input
            type="text"
            name="first_name"
            required
            value={formData.first_name}
            onChange={onChange}
            className="w-full border rounded p-2"
            placeholder="Nhập tên"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Họ</label>
          <input
            type="text"
            name="last_name"
            value={formData.last_name}
            onChange={onChange}
            className="w-full border rounded p-2"
            placeholder="Nhập họ"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Email</label>
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={onChange}
          className="w-full border rounded p-2"
          placeholder="Nhập địa chỉ email"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Số điện thoại</label>
        <input
          type="tel"
          name="phone_number"
          required
          value={formData.phone_number}
          onChange={onChange}
          className="w-full border rounded p-2"
          placeholder="Nhập số điện thoại"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">
          Địa chỉ giao hàng
        </label>
        <textarea
          name="shipping_address"
          required
          value={formData.shipping_address}
          onChange={onChange}
          rows={3}
          className="w-full border rounded p-2"
          placeholder="Nhập địa chỉ giao hàng"
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Phường</label>
          <input
            type="text"
            name="ward"
            required
            value={formData.ward}
            onChange={onChange}
            className="w-full border rounded p-2"
            placeholder="Nhập tên phường"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Quận</label>
          <input
            type="text"
            name="district"
            required
            value={formData.district}
            onChange={onChange}
            className="w-full border rounded p-2"
            placeholder="Nhập tên quận"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Thành phố</label>
          <input
            type="text"
            name="city"
            required
            value={formData.city}
            onChange={onChange}
            className="w-full border rounded p-2"
            placeholder="Nhập tên thành phố"
          />
        </div>
      </div>

      <div className="flex justify-end pt-4">
        <button
          type="submit"
          disabled={loading}
          className="bg-primary text-white px-6 py-2 rounded hover:bg-primary/90 disabled:opacity-50"
        >
          {loading ? "Đang xử lý..." : submitLabel}
        </button>
      </div>
    </form>
  );
}
