# This file is distributed under the same license as the Django package.
#
# Translators:
# Machaku, 2013-2014
# Millicent  <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-12-04 18:32+0000\n"
"Last-Translator: Millicent  <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Swahili (http://app.transifex.com/django/django/language/"
"sw/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sw\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Maelekezo upya"

msgid "site"
msgstr "Tovuti"

msgid "redirect from"
msgstr "Imeelekezwa kutoka"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Hii inapaswa kuwa njia kamili, isiyojumuisha jina la uwanja. Mfano: '/events/"
"search/"

msgid "redirect to"
msgstr "elekeza kwenda"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Hii inaweza kuwa njia kamili (kama ilivyoelezwa hapo juu) au URL kamili "
"ikiwa na mwanzo na mpango kama vile 'https://'."

msgid "redirect"
msgstr "maelekezo upya"

msgid "redirects"
msgstr "maelekezo upya"
