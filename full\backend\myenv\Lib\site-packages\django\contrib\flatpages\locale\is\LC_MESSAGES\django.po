# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2011-2012
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-20 05:07+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Icelandic (http://www.transifex.com/django/django/language/"
"is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

msgid "Advanced options"
msgstr "Ítarlegar stillingar"

msgid "Flat Pages"
msgstr "Flatskrár"

msgid "URL"
msgstr "Veffang"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr "Dæmi: '/about/contact/'. Passaðu að hafa skástrik fremst og aftast."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Þessi reitur má aðeins innihalda bókstafi (ekki broddstafi), tölustafi og "
"táknin . / - _ og ~."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Dæmi: '/about/contact'. Passaðu að hafa skástrik fremst."

msgid "URL is missing a leading slash."
msgstr "Skástrik vantar fremst í slóð"

msgid "URL is missing a trailing slash."
msgstr "Skástrik vantar aftast í slóð"

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "'Flatpage' með slóðina %(url)s er þegar til fyrir síðuna %(site)s"

msgid "title"
msgstr "titill"

msgid "content"
msgstr "innihald"

msgid "enable comments"
msgstr "virkja athugasemdir"

msgid "template name"
msgstr "nafn sniðmáts"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Dæmi: 'flatpages/contact_page.html'. Ef ekkert er gefið upp mun kerfið nota "
"'flatpages/default.html'."

msgid "registration required"
msgstr "skráning nauðsynleg"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Ef þetta er valið geta eingöngu innskráðir notendur séð síðuna."

msgid "sites"
msgstr "vefir"

msgid "flat page"
msgstr "flatskrá"

msgid "flat pages"
msgstr "flatskrár"
