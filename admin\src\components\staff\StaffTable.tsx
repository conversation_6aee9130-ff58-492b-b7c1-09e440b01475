import React from "react";
import { Table, Tag } from "antd";
import type { ColumnsType } from "antd/es/table";
import { useNavigate } from "react-router-dom";

const roleDisplayMap: { [key: string]: string } = {
  sales_admin: "<PERSON>h<PERSON> viên bán hàng",
  sales_manager: "<PERSON>uản lý",
  bod: "BOD",
  delivery_staff: "Nhân viên giao hàng",
  warehouse_staff: "Nhân viên kho",
  accounting_staff: "<PERSON>h<PERSON> viên kế toán",
  accountant: "Accountant",
  customer: "Customer",
};

interface StaffListItem {
  id: number;
  email: string;
  full_name: string;
  phone_number?: string;
  date_joined: string;
  last_login?: string;
  is_active: boolean;
  profile: {
    role: string;
  };
}

interface StaffTableProps {
  data: StaffListItem[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number) => void;
  };
}

const StaffTable: React.FC<StaffTableProps> = ({
  data,
  loading,
  pagination,
}) => {
  const navigate = useNavigate();

  const columns: ColumnsType<StaffListItem> = [
    {
      title: "Nhân Viên",
      key: "staff",
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.full_name}</div>
          <div className="text-sm text-gray-500">{record.email}</div>
          {record.phone_number && (
            <div className="text-sm text-gray-500">{record.phone_number}</div>
          )}
        </div>
      ),
    },
    {
      title: "Chức vụ",
      dataIndex: ["profile", "role"],
      key: "role",
      render: (role: string) => <span>{roleDisplayMap[role] || "N/A"}</span>,
    },
    {
      title: "Ngày tham gia",
      key: "dates",
      render: (_, record) => (
        <div>
          <div>{new Date(record.date_joined).toLocaleDateString("vi-VN")}</div>
          {record.last_login && (
            <div className="text-sm text-gray-500">
              Đăng nhập lần cuối:{" "}
              {new Date(record.last_login).toLocaleDateString("vi-VN")}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Trạng thái",
      key: "status",
      render: (_, record) => (
        <Tag color={record.is_active ? "success" : "error"}>
          {record.is_active ? "Hoạt động" : "Ngừng hoạt động"}
        </Tag>
      ),
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      render: (_, record) => (
        <a
          onClick={() => navigate(`/staff/${record.id}`)}
          className="text-blue-600 hover:text-blue-800 cursor-pointer"
        >
          Xem chi tiết
        </a>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowClassName={(_, index) => (index % 2 === 0 ? "bg-white" : "bg-gray-50")}
      pagination={{
        ...pagination,
        align: "center",
        showTotal: (total) => `Tổng số ${total} nhân viên`,
      }}
    />
  );
};

export default StaffTable;
