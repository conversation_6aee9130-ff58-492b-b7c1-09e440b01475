# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2012,2014-2015
# Ta<PERSON>ya <PERSON> <<EMAIL>>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-02-07 16:07+0000\n"
"Last-Translator: Ta<PERSON>ya <PERSON> <<EMAIL>>\n"
"Language-Team: Japanese (http://www.transifex.com/django/django/language/"
"ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Advanced options"
msgstr "詳細設定"

msgid "Flat Pages"
msgstr "フラットページ"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"例: “/about/contact/”. 先頭と最後にスラッシュがあるか確認してください。"

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"この値は文字、数字、ドット、アンダースコア、ダッシュ、スラッシュかチルダのみ"
"でなければいけません。"

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "例: “/about/contact”. 先頭にスラッシュがあるか確認してください。"

msgid "URL is missing a leading slash."
msgstr "URLの先頭はスラッシュが必要です。"

msgid "URL is missing a trailing slash."
msgstr "URLの末尾はスラッシュが必要です。"

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "URL %(url)s のフラットページは %(site)s のサイトに既に存在しています。"

msgid "title"
msgstr "タイトル"

msgid "content"
msgstr "内容"

msgid "enable comments"
msgstr "コメントを有効にする"

msgid "template name"
msgstr "テンプレート名"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"例: “flatpages/contact_page.html”. 指定しなければ、デフォルト値の “flatpages/"
"default.html” を使います。"

msgid "registration required"
msgstr "登録が必要です"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "チェックした場合、ログインしたユーザーだけがページを参照できます。"

msgid "sites"
msgstr "サイト"

msgid "flat page"
msgstr "フラットページ"

msgid "flat pages"
msgstr "フラットページ"
