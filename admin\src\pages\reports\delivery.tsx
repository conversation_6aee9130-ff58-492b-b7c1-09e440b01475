import React, { useState, useEffect, useCallback } from "react";
import { Table, DatePicker, Card, Spin, Typography } from "antd";
import { Truck, Package } from "lucide-react";
import { apiCall, endpoints } from "@/lib/api";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
interface DeliveryRevenueItem {
  name: string;
  revenue: number;
  orders: number;
  type: 'delivery_staff' | 'shipping_method' | 'total';
  key?: string;
  isTotal?: boolean;
}

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const DeliveryRevenueReport: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<{
    deliveryStaff: DeliveryRevenueItem[];
    shippingMethods: DeliveryRevenueItem[];
  }>({ deliveryStaff: [], shippingMethods: [] });

  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().startOf("month"),
    dayjs().endOf("day"),
  ]);

  const fetchData = useCallback(async () => {
    if (!dateRange || dateRange.length !== 2) return;

    setLoading(true);
    setError(null);

    try {
      const [startDate, endDate] = dateRange;
      const params = {
        dateFrom: startDate.format("YYYY-MM-DD"),
        dateTo: endDate.format("YYYY-MM-DD"),
      };

      const response = await apiCall<{ delivery_revenue: DeliveryRevenueItem[] }>(
        "GET",
        `${endpoints.reports.deliveryRevenue}?${new URLSearchParams(
          params as any
        ).toString()}`
      );

      if (response && Array.isArray(response.delivery_revenue)) {
        const deliveryStaff = response.delivery_revenue.filter(
          (item) => item.type === "delivery_staff"
        );
        const shippingMethods = response.delivery_revenue.filter(
          (item) => item.type === "shipping_method"
        );

        setData({ deliveryStaff, shippingMethods });
      } else {
        setError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching delivery revenue data:", err);
      setError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const columns = [
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: DeliveryRevenueItem) => (
        <div className="flex items-center">
          {record.type === "delivery_staff" ? (
            <Truck className="mr-2 h-4 w-4 text-blue-500" />
          ) : (
            <Package className="mr-2 h-4 w-4 text-green-500" />
          )}
          <span className={record.isTotal ? 'font-semibold' : ''}>{text}</span>
        </div>
      ),
    },
    {
      title: "Doanh thu",
      dataIndex: "revenue",
      key: "revenue",
      render: (value: number, record: any) => (
        <span className={`font-medium ${record.isTotal ? 'text-blue-600 font-semibold' : ''}`}>
          {new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: "VND",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }).format(value)}
        </span>
      ),
    },
    {
      title: "Số đơn hàng",
      dataIndex: "orders",
      key: "orders",
      render: (value: number, record: any) => (
        <span className={`font-medium ${record.isTotal ? 'text-blue-600 font-semibold' : ''}`}>
          {value}
        </span>
      ),
    },
  ];

  // Calculate totals for delivery staff
  const deliveryStaffTotal = data.deliveryStaff.reduce(
    (acc, curr) => ({
      revenue: acc.revenue + (curr.revenue || 0),
      orders: acc.orders + (curr.orders || 0),
    }),
    { revenue: 0, orders: 0 }
  );

  // Calculate totals for shipping methods
  const shippingMethodsTotal = data.shippingMethods.reduce(
    (acc, curr) => ({
      revenue: acc.revenue + (curr.revenue || 0),
      orders: acc.orders + (curr.orders || 0),
    }),
    { revenue: 0, orders: 0 }
  );

  // Add total rows to the data
  const deliveryStaffWithTotal: (DeliveryRevenueItem & { key: string })[] = [
    ...data.deliveryStaff.map(item => ({
      ...item,
      key: `delivery-${item.name}`,
      isTotal: false
    })),
    {
      name: "Tổng cộng",
      revenue: deliveryStaffTotal.revenue,
      orders: deliveryStaffTotal.orders,
      isTotal: true,
      key: 'delivery-total',
      type: 'total' as const
    }
  ];

  const shippingMethodsWithTotal: (DeliveryRevenueItem & { key: string })[] = [
    ...data.shippingMethods.map(item => ({
      ...item,
      key: `shipping-${item.name}`,
      isTotal: false
    })),
    {
      name: "Tổng cộng",
      revenue: shippingMethodsTotal.revenue,
      orders: shippingMethodsTotal.orders,
      isTotal: true,
      key: 'shipping-total',
      type: 'total' as const
    }
  ];

  const handleDateChange = (dates: any) => {
    if (dates && dates[0] && dates[1]) {
      setDateRange([dates[0], dates[1]]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <Title level={3} className="mb-1">
            Báo cáo doanh thu giao hàng
          </Title>
          <Text type="secondary">
            Phân tích hiệu suất giao hàng theo nhân viên và phương thức vận chuyển
          </Text>
        </div>
        <RangePicker
          value={dateRange as any}
          onChange={handleDateChange}
          format="DD/MM/YYYY"
          className="w-full md:w-auto"
        />
      </div>

      {error && (
        <div className="text-red-500 bg-red-50 p-4 rounded-md">{error}</div>
      )}

      <Spin spinning={loading}>
        <div className="grid gap-6">
          <Card
            title={
              <div className="flex items-center">
                <Truck className="mr-2 h-5 w-5 text-blue-500" />
                <span>Nhân viên giao hàng</span>
              </div>
            }
            className="shadow-sm"
          >
            <Table
              columns={columns}
              dataSource={deliveryStaffWithTotal}
              rowKey="key"
              pagination={false}
              rowClassName={(record: any) => record.isTotal ? 'bg-blue-50' : ''}
              locale={{
                emptyText: "Không có dữ liệu nhân viên giao hàng",
              }}
            />
          </Card>

          <Card
            title={
              <div className="flex items-center">
                <Package className="mr-2 h-5 w-5 text-green-500" />
                <span>Phương thức vận chuyển</span>
              </div>
            }
            className="shadow-sm"
          >
            <Table
              columns={columns}
              dataSource={shippingMethodsWithTotal}
              rowKey="key"
              pagination={false}
              rowClassName={(record: any) => record.isTotal ? 'bg-blue-50' : ''}
              locale={{
                emptyText: "Không có dữ liệu phương thức vận chuyển",
              }}
            />
          </Card>
        </div>
      </Spin>
    </div>
  );
};

export default DeliveryRevenueReport;
