import { <PERSON>, <PERSON><PERSON>, Tag } from "antd";
import type { TableProps } from "antd";
import { CheckCircle2, XCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { CustomerListItem } from "../../types/customer";
import { formatCurrency } from "../../lib/utils";

interface CustomerTableProps {
  data: CustomerListItem[];
  loading?: boolean;
  onToggleStatus?: (customerId: number, isActive: boolean) => void;
}

export function CustomerTable({ data, loading, onToggleStatus }: CustomerTableProps) {
  const navigate = useNavigate();

  const columns: TableProps<CustomerListItem>["columns"] = [
    {
      title: "Kh<PERSON>ch hàng",
      key: "customer",
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.full_name}</div>
          <div className="text-sm text-muted-foreground">
            {record.email}
          </div>
          {record.phone_number && (
            <div className="text-sm text-muted-foreground">
              {record.phone_number}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Ngày tham gia",
      key: "date_joined",
      render: (_, record) => (
        <div>
          <div>
            {new Date(record.date_joined).toLocaleDateString("vi-VN")}
          </div>
          {record.last_login && (
            <div className="text-sm text-muted-foreground">
              Đăng nhập lần cuối:{" "}
              {new Date(record.last_login).toLocaleDateString("vi-VN")}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Đơn hàng",
      dataIndex: "total_orders",
      key: "total_orders",
      align: "center",
    },
    {
      title: "Tổng chi tiêu",
      key: "total_spent",
      render: (_, record) => formatCurrency(record.total_spent),
    },
    {
      title: "Trạng thái hoạt động",
      key: "is_active",
      render: (_, record) => (
        <div
          className={`flex items-center ${
            record.is_active ? "text-green-600" : "text-red-600"
          }`}
        >
          {record.is_active ? (
            <>
              <CheckCircle2 className="w-4 h-4 mr-1" />
              <span>Còn hoạt động</span>
            </>
          ) : (
            <>
              <XCircle className="w-4 h-4 mr-1" />
              <span>Ngưng hoạt động</span>
            </>
          )}
        </div>
      ),
    },
    {
      title: "Thao tác",
      key: "action",
      align: "center",
      render: (_, record) => (
        <Button
          type="link"
          className="text-primary hover:text-primary/90"
          onClick={() => navigate(`/customers/${record.id}`)}
        >
          Xem chi tiết
        </Button>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      pagination={false}
      rowClassName={(_, index) => (index % 2 === 0 ? "bg-white" : "bg-gray-50")}
    />
  );
}
