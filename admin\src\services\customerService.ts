import { api, endpoints } from "../lib/api";

export interface CustomerRankInfo {
  total_spent: number;
  current_rank: 'normal' | 'silver' | 'gold';
  rank_display: string;
  discount_percentage?: number;
  next_rank_threshold?: number;
  progress_percentage: number;
  total_orders: number;
  delivered_orders: number;
}

export interface CustomerStats {
  total_orders: number;
  total_spent: number;
  role: string;
  last_order?: string;
  rank_info: CustomerRankInfo;
}

export interface RankDiscount {
  rank: 'normal' | 'silver' | 'gold';
  discount_percentage?: number;
  threshold: number;
}

export class CustomerService {
  // Rank thresholds and discounts
  static readonly RANK_THRESHOLDS = {
    silver: 100000000,   // 100 million VND
    gold: 200000000,     // 200 million VND
  };

  static readonly RANK_DISCOUNTS = {
    normal: 0,
    silver: 2,  // 2%
    gold: 3,    // 3%
  };

  /**
   * Get customer statistics including rank information
   */
  static async getCustomerStats(customerId: number): Promise<CustomerStats> {
    const response = await api.get(endpoints.customers.stats(customerId));
    return response.data;
  }

  /**
   * Calculate rank discount amount for a given subtotal and rank
   */
  static calculateRankDiscount(subtotal: number, rank: 'normal' | 'silver' | 'gold'): number {
    const discountPercentage = this.RANK_DISCOUNTS[rank];
    return Math.round(subtotal * (discountPercentage / 100));
  }

  /**
   * Get rank information for display
   */
  static getRankInfo(rank: 'normal' | 'silver' | 'gold') {
    const rankLabels = {
      normal: 'Thường',
      silver: 'Bạc',
      gold: 'Vàng',
    };

    const rankColors = {
      normal: '#8c8c8c',
      silver: '#c0c0c0',
      gold: '#ffd700',
    };

    const rankIcons = {
      normal: '👤',
      silver: '🥈',
      gold: '🥇',
    };

    return {
      label: rankLabels[rank],
      color: rankColors[rank],
      icon: rankIcons[rank],
      discount: this.RANK_DISCOUNTS[rank],
    };
  }

  /**
   * Determine rank based on total spent
   */
  static determineRank(totalSpent: number): 'normal' | 'silver' | 'gold' {
    if (totalSpent >= this.RANK_THRESHOLDS.gold) {
      return 'gold';
    } else if (totalSpent >= this.RANK_THRESHOLDS.silver) {
      return 'silver';
    } else {
      return 'normal';
    }
  }

  /**
   * Calculate progress to next rank
   */
  static calculateRankProgress(totalSpent: number, currentRank: 'normal' | 'silver' | 'gold') {
    let nextThreshold: number | null = null;
    let progress = 0;

    if (currentRank === 'normal') {
      nextThreshold = this.RANK_THRESHOLDS.silver;
      progress = Math.min(100, (totalSpent / nextThreshold) * 100);
    } else if (currentRank === 'silver') {
      nextThreshold = this.RANK_THRESHOLDS.gold;
      progress = Math.min(100, (totalSpent / nextThreshold) * 100);
    } else {
      // Gold rank - no next rank
      progress = 100;
    }

    return {
      nextThreshold,
      progress: Math.round(progress),
    };
  }

  /**
   * Get all rank discounts for reference
   */
  static getAllRankDiscounts(): RankDiscount[] {
    return [
      {
        rank: 'normal',
        discount_percentage: this.RANK_DISCOUNTS.normal,
        threshold: 0,
      },
      {
        rank: 'silver',
        discount_percentage: this.RANK_DISCOUNTS.silver,
        threshold: this.RANK_THRESHOLDS.silver,
      },
      {
        rank: 'gold',
        discount_percentage: this.RANK_DISCOUNTS.gold,
        threshold: this.RANK_THRESHOLDS.gold,
      },
    ];
  }
}
