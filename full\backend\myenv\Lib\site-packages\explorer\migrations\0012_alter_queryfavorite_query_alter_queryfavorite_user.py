# Generated by Django 4.1.7 on 2023-02-27 08:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("explorer", "0011_query_favorites"),
    ]

    operations = [
        migrations.AlterField(
            model_name="queryfavorite",
            name="query",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="favorites",
                to="explorer.query",
            ),
        ),
        migrations.AlterField(
            model_name="queryfavorite",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="favorites",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
