const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./query-list.5.3.js","./index.5.3.js","./_commonjsHelpers.5.3.js","./csrf.5.3.js","./favorites.5.3.js","./explorer.5.3.js","./choices.5.3.js","./schema.5.3.js","./uploads.5.3.js","./tableDescription.5.3.js"])))=>i.map(i=>d[i]);
var gi=Object.defineProperty,Ei=Object.defineProperties;var vi=Object.getOwnPropertyDescriptors;var On=Object.getOwnPropertySymbols;var bi=Object.prototype.hasOwnProperty,Ai=Object.prototype.propertyIsEnumerable;var Cn=(n,t,e)=>t in n?gi(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,L=(n,t)=>{for(var e in t||(t={}))bi.call(t,e)&&Cn(n,e,t[e]);if(On)for(var e of On(t))Ai.call(t,e)&&Cn(n,e,t[e]);return n},ne=(n,t)=>Ei(n,vi(t));var Y=(n,t,e)=>new Promise((s,i)=>{var r=c=>{try{a(e.next(c))}catch(d){i(d)}},o=c=>{try{a(e.throw(c))}catch(d){i(d)}},a=c=>c.done?s(c.value):Promise.resolve(c.value).then(r,o);a((e=e.apply(n,t)).next())});const Ti="modulepreload",yi=function(n,t){return new URL(n,t).href},Nn={},z=function(t,e,s){let i=Promise.resolve();if(e&&e.length>0){const o=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),c=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));i=Promise.allSettled(e.map(d=>{if(d=yi(d,s),d in Nn)return;Nn[d]=!0;const u=d.endsWith(".css"),_=u?'[rel="stylesheet"]':"";if(!!s)for(let E=o.length-1;E>=0;E--){const p=o[E];if(p.href===d&&(!u||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${d}"]${_}`))return;const h=document.createElement("link");if(h.rel=u?"stylesheet":Ti,u||(h.as="script"),h.crossOrigin="",h.href=d,c&&h.setAttribute("nonce",c),document.head.appendChild(h),u)return new Promise((E,p)=>{h.addEventListener("load",E),h.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${d}`)))})}))}function r(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return i.then(o=>{for(const a of o||[])a.status==="rejected"&&r(a.reason);return t().catch(r)})};var I="top",M="bottom",k="right",P="left",ge="auto",xt=[I,M,k,P],gt="start",St="end",ds="clippingParents",Qe="viewport",wt="popper",hs="reference",Ke=xt.reduce(function(n,t){return n.concat([t+"-"+gt,t+"-"+St])},[]),Ze=[].concat(xt,[ge]).reduce(function(n,t){return n.concat([t,t+"-"+gt,t+"-"+St])},[]),fs="beforeRead",ps="read",_s="afterRead",ms="beforeMain",gs="main",Es="afterMain",vs="beforeWrite",bs="write",As="afterWrite",Ts=[fs,ps,_s,ms,gs,Es,vs,bs,As];function X(n){return n?(n.nodeName||"").toLowerCase():null}function V(n){if(n==null)return window;if(n.toString()!=="[object Window]"){var t=n.ownerDocument;return t&&t.defaultView||window}return n}function Et(n){var t=V(n).Element;return n instanceof t||n instanceof Element}function H(n){var t=V(n).HTMLElement;return n instanceof t||n instanceof HTMLElement}function Je(n){if(typeof ShadowRoot=="undefined")return!1;var t=V(n).ShadowRoot;return n instanceof t||n instanceof ShadowRoot}function wi(n){var t=n.state;Object.keys(t.elements).forEach(function(e){var s=t.styles[e]||{},i=t.attributes[e]||{},r=t.elements[e];!H(r)||!X(r)||(Object.assign(r.style,s),Object.keys(i).forEach(function(o){var a=i[o];a===!1?r.removeAttribute(o):r.setAttribute(o,a===!0?"":a)}))})}function Oi(n){var t=n.state,e={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,e.popper),t.styles=e,t.elements.arrow&&Object.assign(t.elements.arrow.style,e.arrow),function(){Object.keys(t.elements).forEach(function(s){var i=t.elements[s],r=t.attributes[s]||{},o=Object.keys(t.styles.hasOwnProperty(s)?t.styles[s]:e[s]),a=o.reduce(function(c,d){return c[d]="",c},{});!H(i)||!X(i)||(Object.assign(i.style,a),Object.keys(r).forEach(function(c){i.removeAttribute(c)}))})}}const tn={name:"applyStyles",enabled:!0,phase:"write",fn:wi,effect:Oi,requires:["computeStyles"]};function G(n){return n.split("-")[0]}var mt=Math.max,fe=Math.min,Dt=Math.round;function Ye(){var n=navigator.userAgentData;return n!=null&&n.brands&&Array.isArray(n.brands)?n.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function ys(){return!/^((?!chrome|android).)*safari/i.test(Ye())}function Lt(n,t,e){t===void 0&&(t=!1),e===void 0&&(e=!1);var s=n.getBoundingClientRect(),i=1,r=1;t&&H(n)&&(i=n.offsetWidth>0&&Dt(s.width)/n.offsetWidth||1,r=n.offsetHeight>0&&Dt(s.height)/n.offsetHeight||1);var o=Et(n)?V(n):window,a=o.visualViewport,c=!ys()&&e,d=(s.left+(c&&a?a.offsetLeft:0))/i,u=(s.top+(c&&a?a.offsetTop:0))/r,_=s.width/i,m=s.height/r;return{width:_,height:m,top:u,right:d+_,bottom:u+m,left:d,x:d,y:u}}function en(n){var t=Lt(n),e=n.offsetWidth,s=n.offsetHeight;return Math.abs(t.width-e)<=1&&(e=t.width),Math.abs(t.height-s)<=1&&(s=t.height),{x:n.offsetLeft,y:n.offsetTop,width:e,height:s}}function ws(n,t){var e=t.getRootNode&&t.getRootNode();if(n.contains(t))return!0;if(e&&Je(e)){var s=t;do{if(s&&n.isSameNode(s))return!0;s=s.parentNode||s.host}while(s)}return!1}function J(n){return V(n).getComputedStyle(n)}function Ci(n){return["table","td","th"].indexOf(X(n))>=0}function ot(n){return((Et(n)?n.ownerDocument:n.document)||window.document).documentElement}function Ee(n){return X(n)==="html"?n:n.assignedSlot||n.parentNode||(Je(n)?n.host:null)||ot(n)}function Sn(n){return!H(n)||J(n).position==="fixed"?null:n.offsetParent}function Ni(n){var t=/firefox/i.test(Ye()),e=/Trident/i.test(Ye());if(e&&H(n)){var s=J(n);if(s.position==="fixed")return null}var i=Ee(n);for(Je(i)&&(i=i.host);H(i)&&["html","body"].indexOf(X(i))<0;){var r=J(i);if(r.transform!=="none"||r.perspective!=="none"||r.contain==="paint"||["transform","perspective"].indexOf(r.willChange)!==-1||t&&r.willChange==="filter"||t&&r.filter&&r.filter!=="none")return i;i=i.parentNode}return null}function Ut(n){for(var t=V(n),e=Sn(n);e&&Ci(e)&&J(e).position==="static";)e=Sn(e);return e&&(X(e)==="html"||X(e)==="body"&&J(e).position==="static")?t:e||Ni(n)||t}function nn(n){return["top","bottom"].indexOf(n)>=0?"x":"y"}function Ft(n,t,e){return mt(n,fe(t,e))}function Si(n,t,e){var s=Ft(n,t,e);return s>e?e:s}function Os(){return{top:0,right:0,bottom:0,left:0}}function Cs(n){return Object.assign({},Os(),n)}function Ns(n,t){return t.reduce(function(e,s){return e[s]=n,e},{})}var Di=function(t,e){return t=typeof t=="function"?t(Object.assign({},e.rects,{placement:e.placement})):t,Cs(typeof t!="number"?t:Ns(t,xt))};function Li(n){var t,e=n.state,s=n.name,i=n.options,r=e.elements.arrow,o=e.modifiersData.popperOffsets,a=G(e.placement),c=nn(a),d=[P,k].indexOf(a)>=0,u=d?"height":"width";if(!(!r||!o)){var _=Di(i.padding,e),m=en(r),h=c==="y"?I:P,E=c==="y"?M:k,p=e.rects.reference[u]+e.rects.reference[c]-o[c]-e.rects.popper[u],v=o[c]-e.rects.reference[c],T=Ut(r),w=T?c==="y"?T.clientHeight||0:T.clientWidth||0:0,O=p/2-v/2,g=_[h],b=w-m[u]-_[E],A=w/2-m[u]/2+O,y=Ft(g,A,b),S=c;e.modifiersData[s]=(t={},t[S]=y,t.centerOffset=y-A,t)}}function $i(n){var t=n.state,e=n.options,s=e.element,i=s===void 0?"[data-popper-arrow]":s;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||ws(t.elements.popper,i)&&(t.elements.arrow=i))}const Ss={name:"arrow",enabled:!0,phase:"main",fn:Li,effect:$i,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function $t(n){return n.split("-")[1]}var Ii={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Pi(n,t){var e=n.x,s=n.y,i=t.devicePixelRatio||1;return{x:Dt(e*i)/i||0,y:Dt(s*i)/i||0}}function Dn(n){var t,e=n.popper,s=n.popperRect,i=n.placement,r=n.variation,o=n.offsets,a=n.position,c=n.gpuAcceleration,d=n.adaptive,u=n.roundOffsets,_=n.isFixed,m=o.x,h=m===void 0?0:m,E=o.y,p=E===void 0?0:E,v=typeof u=="function"?u({x:h,y:p}):{x:h,y:p};h=v.x,p=v.y;var T=o.hasOwnProperty("x"),w=o.hasOwnProperty("y"),O=P,g=I,b=window;if(d){var A=Ut(e),y="clientHeight",S="clientWidth";if(A===V(e)&&(A=ot(e),J(A).position!=="static"&&a==="absolute"&&(y="scrollHeight",S="scrollWidth")),A=A,i===I||(i===P||i===k)&&r===St){g=M;var N=_&&A===b&&b.visualViewport?b.visualViewport.height:A[y];p-=N-s.height,p*=c?1:-1}if(i===P||(i===I||i===M)&&r===St){O=k;var C=_&&A===b&&b.visualViewport?b.visualViewport.width:A[S];h-=C-s.width,h*=c?1:-1}}var D=Object.assign({position:a},d&&Ii),F=u===!0?Pi({x:h,y:p},V(e)):{x:h,y:p};if(h=F.x,p=F.y,c){var $;return Object.assign({},D,($={},$[g]=w?"0":"",$[O]=T?"0":"",$.transform=(b.devicePixelRatio||1)<=1?"translate("+h+"px, "+p+"px)":"translate3d("+h+"px, "+p+"px, 0)",$))}return Object.assign({},D,(t={},t[g]=w?p+"px":"",t[O]=T?h+"px":"",t.transform="",t))}function Ri(n){var t=n.state,e=n.options,s=e.gpuAcceleration,i=s===void 0?!0:s,r=e.adaptive,o=r===void 0?!0:r,a=e.roundOffsets,c=a===void 0?!0:a,d={placement:G(t.placement),variation:$t(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Dn(Object.assign({},d,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Dn(Object.assign({},d,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const sn={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ri,data:{}};var se={passive:!0};function xi(n){var t=n.state,e=n.instance,s=n.options,i=s.scroll,r=i===void 0?!0:i,o=s.resize,a=o===void 0?!0:o,c=V(t.elements.popper),d=[].concat(t.scrollParents.reference,t.scrollParents.popper);return r&&d.forEach(function(u){u.addEventListener("scroll",e.update,se)}),a&&c.addEventListener("resize",e.update,se),function(){r&&d.forEach(function(u){u.removeEventListener("scroll",e.update,se)}),a&&c.removeEventListener("resize",e.update,se)}}const rn={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:xi,data:{}};var Mi={left:"right",right:"left",bottom:"top",top:"bottom"};function ue(n){return n.replace(/left|right|bottom|top/g,function(t){return Mi[t]})}var ki={start:"end",end:"start"};function Ln(n){return n.replace(/start|end/g,function(t){return ki[t]})}function on(n){var t=V(n),e=t.pageXOffset,s=t.pageYOffset;return{scrollLeft:e,scrollTop:s}}function an(n){return Lt(ot(n)).left+on(n).scrollLeft}function Vi(n,t){var e=V(n),s=ot(n),i=e.visualViewport,r=s.clientWidth,o=s.clientHeight,a=0,c=0;if(i){r=i.width,o=i.height;var d=ys();(d||!d&&t==="fixed")&&(a=i.offsetLeft,c=i.offsetTop)}return{width:r,height:o,x:a+an(n),y:c}}function Hi(n){var t,e=ot(n),s=on(n),i=(t=n.ownerDocument)==null?void 0:t.body,r=mt(e.scrollWidth,e.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=mt(e.scrollHeight,e.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-s.scrollLeft+an(n),c=-s.scrollTop;return J(i||e).direction==="rtl"&&(a+=mt(e.clientWidth,i?i.clientWidth:0)-r),{width:r,height:o,x:a,y:c}}function cn(n){var t=J(n),e=t.overflow,s=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(e+i+s)}function Ds(n){return["html","body","#document"].indexOf(X(n))>=0?n.ownerDocument.body:H(n)&&cn(n)?n:Ds(Ee(n))}function Kt(n,t){var e;t===void 0&&(t=[]);var s=Ds(n),i=s===((e=n.ownerDocument)==null?void 0:e.body),r=V(s),o=i?[r].concat(r.visualViewport||[],cn(s)?s:[]):s,a=t.concat(o);return i?a:a.concat(Kt(Ee(o)))}function Ue(n){return Object.assign({},n,{left:n.x,top:n.y,right:n.x+n.width,bottom:n.y+n.height})}function Wi(n,t){var e=Lt(n,!1,t==="fixed");return e.top=e.top+n.clientTop,e.left=e.left+n.clientLeft,e.bottom=e.top+n.clientHeight,e.right=e.left+n.clientWidth,e.width=n.clientWidth,e.height=n.clientHeight,e.x=e.left,e.y=e.top,e}function $n(n,t,e){return t===Qe?Ue(Vi(n,e)):Et(t)?Wi(t,e):Ue(Hi(ot(n)))}function Bi(n){var t=Kt(Ee(n)),e=["absolute","fixed"].indexOf(J(n).position)>=0,s=e&&H(n)?Ut(n):n;return Et(s)?t.filter(function(i){return Et(i)&&ws(i,s)&&X(i)!=="body"}):[]}function ji(n,t,e,s){var i=t==="clippingParents"?Bi(n):[].concat(t),r=[].concat(i,[e]),o=r[0],a=r.reduce(function(c,d){var u=$n(n,d,s);return c.top=mt(u.top,c.top),c.right=fe(u.right,c.right),c.bottom=fe(u.bottom,c.bottom),c.left=mt(u.left,c.left),c},$n(n,o,s));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function Ls(n){var t=n.reference,e=n.element,s=n.placement,i=s?G(s):null,r=s?$t(s):null,o=t.x+t.width/2-e.width/2,a=t.y+t.height/2-e.height/2,c;switch(i){case I:c={x:o,y:t.y-e.height};break;case M:c={x:o,y:t.y+t.height};break;case k:c={x:t.x+t.width,y:a};break;case P:c={x:t.x-e.width,y:a};break;default:c={x:t.x,y:t.y}}var d=i?nn(i):null;if(d!=null){var u=d==="y"?"height":"width";switch(r){case gt:c[d]=c[d]-(t[u]/2-e[u]/2);break;case St:c[d]=c[d]+(t[u]/2-e[u]/2);break}}return c}function It(n,t){t===void 0&&(t={});var e=t,s=e.placement,i=s===void 0?n.placement:s,r=e.strategy,o=r===void 0?n.strategy:r,a=e.boundary,c=a===void 0?ds:a,d=e.rootBoundary,u=d===void 0?Qe:d,_=e.elementContext,m=_===void 0?wt:_,h=e.altBoundary,E=h===void 0?!1:h,p=e.padding,v=p===void 0?0:p,T=Cs(typeof v!="number"?v:Ns(v,xt)),w=m===wt?hs:wt,O=n.rects.popper,g=n.elements[E?w:m],b=ji(Et(g)?g:g.contextElement||ot(n.elements.popper),c,u,o),A=Lt(n.elements.reference),y=Ls({reference:A,element:O,strategy:"absolute",placement:i}),S=Ue(Object.assign({},O,y)),N=m===wt?S:A,C={top:b.top-N.top+T.top,bottom:N.bottom-b.bottom+T.bottom,left:b.left-N.left+T.left,right:N.right-b.right+T.right},D=n.modifiersData.offset;if(m===wt&&D){var F=D[i];Object.keys(C).forEach(function($){var lt=[k,M].indexOf($)>=0?1:-1,ut=[I,M].indexOf($)>=0?"y":"x";C[$]+=F[ut]*lt})}return C}function Fi(n,t){t===void 0&&(t={});var e=t,s=e.placement,i=e.boundary,r=e.rootBoundary,o=e.padding,a=e.flipVariations,c=e.allowedAutoPlacements,d=c===void 0?Ze:c,u=$t(s),_=u?a?Ke:Ke.filter(function(E){return $t(E)===u}):xt,m=_.filter(function(E){return d.indexOf(E)>=0});m.length===0&&(m=_);var h=m.reduce(function(E,p){return E[p]=It(n,{placement:p,boundary:i,rootBoundary:r,padding:o})[G(p)],E},{});return Object.keys(h).sort(function(E,p){return h[E]-h[p]})}function Ki(n){if(G(n)===ge)return[];var t=ue(n);return[Ln(n),t,Ln(t)]}function Yi(n){var t=n.state,e=n.options,s=n.name;if(!t.modifiersData[s]._skip){for(var i=e.mainAxis,r=i===void 0?!0:i,o=e.altAxis,a=o===void 0?!0:o,c=e.fallbackPlacements,d=e.padding,u=e.boundary,_=e.rootBoundary,m=e.altBoundary,h=e.flipVariations,E=h===void 0?!0:h,p=e.allowedAutoPlacements,v=t.options.placement,T=G(v),w=T===v,O=c||(w||!E?[ue(v)]:Ki(v)),g=[v].concat(O).reduce(function(At,et){return At.concat(G(et)===ge?Fi(t,{placement:et,boundary:u,rootBoundary:_,padding:d,flipVariations:E,allowedAutoPlacements:p}):et)},[]),b=t.rects.reference,A=t.rects.popper,y=new Map,S=!0,N=g[0],C=0;C<g.length;C++){var D=g[C],F=G(D),$=$t(D)===gt,lt=[I,M].indexOf(F)>=0,ut=lt?"width":"height",x=It(t,{placement:D,boundary:u,rootBoundary:_,altBoundary:m,padding:d}),K=lt?$?k:P:$?M:I;b[ut]>A[ut]&&(K=ue(K));var Qt=ue(K),dt=[];if(r&&dt.push(x[F]<=0),a&&dt.push(x[K]<=0,x[Qt]<=0),dt.every(function(At){return At})){N=D,S=!1;break}y.set(D,dt)}if(S)for(var Zt=E?3:1,Oe=function(et){var Wt=g.find(function(te){var ht=y.get(te);if(ht)return ht.slice(0,et).every(function(Ce){return Ce})});if(Wt)return N=Wt,"break"},Ht=Zt;Ht>0;Ht--){var Jt=Oe(Ht);if(Jt==="break")break}t.placement!==N&&(t.modifiersData[s]._skip=!0,t.placement=N,t.reset=!0)}}const $s={name:"flip",enabled:!0,phase:"main",fn:Yi,requiresIfExists:["offset"],data:{_skip:!1}};function In(n,t,e){return e===void 0&&(e={x:0,y:0}),{top:n.top-t.height-e.y,right:n.right-t.width+e.x,bottom:n.bottom-t.height+e.y,left:n.left-t.width-e.x}}function Pn(n){return[I,k,M,P].some(function(t){return n[t]>=0})}function Ui(n){var t=n.state,e=n.name,s=t.rects.reference,i=t.rects.popper,r=t.modifiersData.preventOverflow,o=It(t,{elementContext:"reference"}),a=It(t,{altBoundary:!0}),c=In(o,s),d=In(a,i,r),u=Pn(c),_=Pn(d);t.modifiersData[e]={referenceClippingOffsets:c,popperEscapeOffsets:d,isReferenceHidden:u,hasPopperEscaped:_},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":_})}const Is={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ui};function zi(n,t,e){var s=G(n),i=[P,I].indexOf(s)>=0?-1:1,r=typeof e=="function"?e(Object.assign({},t,{placement:n})):e,o=r[0],a=r[1];return o=o||0,a=(a||0)*i,[P,k].indexOf(s)>=0?{x:a,y:o}:{x:o,y:a}}function Gi(n){var t=n.state,e=n.options,s=n.name,i=e.offset,r=i===void 0?[0,0]:i,o=Ze.reduce(function(u,_){return u[_]=zi(_,t.rects,r),u},{}),a=o[t.placement],c=a.x,d=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=d),t.modifiersData[s]=o}const Ps={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Gi};function qi(n){var t=n.state,e=n.name;t.modifiersData[e]=Ls({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const ln={name:"popperOffsets",enabled:!0,phase:"read",fn:qi,data:{}};function Xi(n){return n==="x"?"y":"x"}function Qi(n){var t=n.state,e=n.options,s=n.name,i=e.mainAxis,r=i===void 0?!0:i,o=e.altAxis,a=o===void 0?!1:o,c=e.boundary,d=e.rootBoundary,u=e.altBoundary,_=e.padding,m=e.tether,h=m===void 0?!0:m,E=e.tetherOffset,p=E===void 0?0:E,v=It(t,{boundary:c,rootBoundary:d,padding:_,altBoundary:u}),T=G(t.placement),w=$t(t.placement),O=!w,g=nn(T),b=Xi(g),A=t.modifiersData.popperOffsets,y=t.rects.reference,S=t.rects.popper,N=typeof p=="function"?p(Object.assign({},t.rects,{placement:t.placement})):p,C=typeof N=="number"?{mainAxis:N,altAxis:N}:Object.assign({mainAxis:0,altAxis:0},N),D=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,F={x:0,y:0};if(A){if(r){var $,lt=g==="y"?I:P,ut=g==="y"?M:k,x=g==="y"?"height":"width",K=A[g],Qt=K+v[lt],dt=K-v[ut],Zt=h?-S[x]/2:0,Oe=w===gt?y[x]:S[x],Ht=w===gt?-S[x]:-y[x],Jt=t.elements.arrow,At=h&&Jt?en(Jt):{width:0,height:0},et=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Os(),Wt=et[lt],te=et[ut],ht=Ft(0,y[x],At[x]),Ce=O?y[x]/2-Zt-ht-Wt-C.mainAxis:Oe-ht-Wt-C.mainAxis,di=O?-y[x]/2+Zt+ht+te+C.mainAxis:Ht+ht+te+C.mainAxis,Ne=t.elements.arrow&&Ut(t.elements.arrow),hi=Ne?g==="y"?Ne.clientTop||0:Ne.clientLeft||0:0,mn=($=D==null?void 0:D[g])!=null?$:0,fi=K+Ce-mn-hi,pi=K+di-mn,gn=Ft(h?fe(Qt,fi):Qt,K,h?mt(dt,pi):dt);A[g]=gn,F[g]=gn-K}if(a){var En,_i=g==="x"?I:P,mi=g==="x"?M:k,ft=A[b],ee=b==="y"?"height":"width",vn=ft+v[_i],bn=ft-v[mi],Se=[I,P].indexOf(T)!==-1,An=(En=D==null?void 0:D[b])!=null?En:0,Tn=Se?vn:ft-y[ee]-S[ee]-An+C.altAxis,yn=Se?ft+y[ee]+S[ee]-An-C.altAxis:bn,wn=h&&Se?Si(Tn,ft,yn):Ft(h?Tn:vn,ft,h?yn:bn);A[b]=wn,F[b]=wn-ft}t.modifiersData[s]=F}}const Rs={name:"preventOverflow",enabled:!0,phase:"main",fn:Qi,requiresIfExists:["offset"]};function Zi(n){return{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop}}function Ji(n){return n===V(n)||!H(n)?on(n):Zi(n)}function tr(n){var t=n.getBoundingClientRect(),e=Dt(t.width)/n.offsetWidth||1,s=Dt(t.height)/n.offsetHeight||1;return e!==1||s!==1}function er(n,t,e){e===void 0&&(e=!1);var s=H(t),i=H(t)&&tr(t),r=ot(t),o=Lt(n,i,e),a={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(s||!s&&!e)&&((X(t)!=="body"||cn(r))&&(a=Ji(t)),H(t)?(c=Lt(t,!0),c.x+=t.clientLeft,c.y+=t.clientTop):r&&(c.x=an(r))),{x:o.left+a.scrollLeft-c.x,y:o.top+a.scrollTop-c.y,width:o.width,height:o.height}}function nr(n){var t=new Map,e=new Set,s=[];n.forEach(function(r){t.set(r.name,r)});function i(r){e.add(r.name);var o=[].concat(r.requires||[],r.requiresIfExists||[]);o.forEach(function(a){if(!e.has(a)){var c=t.get(a);c&&i(c)}}),s.push(r)}return n.forEach(function(r){e.has(r.name)||i(r)}),s}function sr(n){var t=nr(n);return Ts.reduce(function(e,s){return e.concat(t.filter(function(i){return i.phase===s}))},[])}function ir(n){var t;return function(){return t||(t=new Promise(function(e){Promise.resolve().then(function(){t=void 0,e(n())})})),t}}function rr(n){var t=n.reduce(function(e,s){var i=e[s.name];return e[s.name]=i?Object.assign({},i,s,{options:Object.assign({},i.options,s.options),data:Object.assign({},i.data,s.data)}):s,e},{});return Object.keys(t).map(function(e){return t[e]})}var Rn={placement:"bottom",modifiers:[],strategy:"absolute"};function xn(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];return!t.some(function(s){return!(s&&typeof s.getBoundingClientRect=="function")})}function ve(n){n===void 0&&(n={});var t=n,e=t.defaultModifiers,s=e===void 0?[]:e,i=t.defaultOptions,r=i===void 0?Rn:i;return function(a,c,d){d===void 0&&(d=r);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Rn,r),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},_=[],m=!1,h={state:u,setOptions:function(T){var w=typeof T=="function"?T(u.options):T;p(),u.options=Object.assign({},r,u.options,w),u.scrollParents={reference:Et(a)?Kt(a):a.contextElement?Kt(a.contextElement):[],popper:Kt(c)};var O=sr(rr([].concat(s,u.options.modifiers)));return u.orderedModifiers=O.filter(function(g){return g.enabled}),E(),h.update()},forceUpdate:function(){if(!m){var T=u.elements,w=T.reference,O=T.popper;if(xn(w,O)){u.rects={reference:er(w,Ut(O),u.options.strategy==="fixed"),popper:en(O)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(C){return u.modifiersData[C.name]=Object.assign({},C.data)});for(var g=0;g<u.orderedModifiers.length;g++){if(u.reset===!0){u.reset=!1,g=-1;continue}var b=u.orderedModifiers[g],A=b.fn,y=b.options,S=y===void 0?{}:y,N=b.name;typeof A=="function"&&(u=A({state:u,options:S,name:N,instance:h})||u)}}}},update:ir(function(){return new Promise(function(v){h.forceUpdate(),v(u)})}),destroy:function(){p(),m=!0}};if(!xn(a,c))return h;h.setOptions(d).then(function(v){!m&&d.onFirstUpdate&&d.onFirstUpdate(v)});function E(){u.orderedModifiers.forEach(function(v){var T=v.name,w=v.options,O=w===void 0?{}:w,g=v.effect;if(typeof g=="function"){var b=g({state:u,name:T,instance:h,options:O}),A=function(){};_.push(b||A)}})}function p(){_.forEach(function(v){return v()}),_=[]}return h}}var or=ve(),ar=[rn,ln,sn,tn],cr=ve({defaultModifiers:ar}),lr=[rn,ln,sn,tn,Ps,$s,Rs,Ss,Is],un=ve({defaultModifiers:lr});const xs=Object.freeze(Object.defineProperty({__proto__:null,afterMain:Es,afterRead:_s,afterWrite:As,applyStyles:tn,arrow:Ss,auto:ge,basePlacements:xt,beforeMain:ms,beforeRead:fs,beforeWrite:vs,bottom:M,clippingParents:ds,computeStyles:sn,createPopper:un,createPopperBase:or,createPopperLite:cr,detectOverflow:It,end:St,eventListeners:rn,flip:$s,hide:Is,left:P,main:gs,modifierPhases:Ts,offset:Ps,placements:Ze,popper:wt,popperGenerator:ve,popperOffsets:ln,preventOverflow:Rs,read:ps,reference:hs,right:k,start:gt,top:I,variationPlacements:Ke,viewport:Qe,write:bs},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.3.2 (https://getbootstrap.com/)
  * Copyright 2011-2023 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const nt=new Map,De={set(n,t,e){nt.has(n)||nt.set(n,new Map);const s=nt.get(n);if(!s.has(t)&&s.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`);return}s.set(t,e)},get(n,t){return nt.has(n)&&nt.get(n).get(t)||null},remove(n,t){if(!nt.has(n))return;const e=nt.get(n);e.delete(t),e.size===0&&nt.delete(n)}},ur=1e6,dr=1e3,ze="transitionend",Ms=n=>(n&&window.CSS&&window.CSS.escape&&(n=n.replace(/#([^\s"#']+)/g,(t,e)=>`#${CSS.escape(e)}`)),n),hr=n=>n==null?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase(),fr=n=>{do n+=Math.floor(Math.random()*ur);while(document.getElementById(n));return n},pr=n=>{if(!n)return 0;let{transitionDuration:t,transitionDelay:e}=window.getComputedStyle(n);const s=Number.parseFloat(t),i=Number.parseFloat(e);return!s&&!i?0:(t=t.split(",")[0],e=e.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(e))*dr)},ks=n=>{n.dispatchEvent(new Event(ze))},Q=n=>!n||typeof n!="object"?!1:(typeof n.jquery!="undefined"&&(n=n[0]),typeof n.nodeType!="undefined"),st=n=>Q(n)?n.jquery?n[0]:n:typeof n=="string"&&n.length>0?document.querySelector(Ms(n)):null,Mt=n=>{if(!Q(n)||n.getClientRects().length===0)return!1;const t=getComputedStyle(n).getPropertyValue("visibility")==="visible",e=n.closest("details:not([open])");if(!e)return t;if(e!==n){const s=n.closest("summary");if(s&&s.parentNode!==e||s===null)return!1}return t},it=n=>!n||n.nodeType!==Node.ELEMENT_NODE||n.classList.contains("disabled")?!0:typeof n.disabled!="undefined"?n.disabled:n.hasAttribute("disabled")&&n.getAttribute("disabled")!=="false",Vs=n=>{if(!document.documentElement.attachShadow)return null;if(typeof n.getRootNode=="function"){const t=n.getRootNode();return t instanceof ShadowRoot?t:null}return n instanceof ShadowRoot?n:n.parentNode?Vs(n.parentNode):null},pe=()=>{},zt=n=>{n.offsetHeight},Hs=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Le=[],_r=n=>{document.readyState==="loading"?(Le.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of Le)t()}),Le.push(n)):n()},W=()=>document.documentElement.dir==="rtl",j=n=>{_r(()=>{const t=Hs();if(t){const e=n.NAME,s=t.fn[e];t.fn[e]=n.jQueryInterface,t.fn[e].Constructor=n,t.fn[e].noConflict=()=>(t.fn[e]=s,n.jQueryInterface)}})},R=(n,t=[],e=n)=>typeof n=="function"?n(...t):e,Ws=(n,t,e=!0)=>{if(!e){R(n);return}const i=pr(t)+5;let r=!1;const o=({target:a})=>{a===t&&(r=!0,t.removeEventListener(ze,o),R(n))};t.addEventListener(ze,o),setTimeout(()=>{r||ks(t)},i)},dn=(n,t,e,s)=>{const i=n.length;let r=n.indexOf(t);return r===-1?!e&&s?n[i-1]:n[0]:(r+=e?1:-1,s&&(r=(r+i)%i),n[Math.max(0,Math.min(r,i-1))])},mr=/[^.]*(?=\..*)\.|.*/,gr=/\..*/,Er=/::\d+$/,$e={};let Mn=1;const Bs={mouseenter:"mouseover",mouseleave:"mouseout"},vr=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function js(n,t){return t&&`${t}::${Mn++}`||n.uidEvent||Mn++}function Fs(n){const t=js(n);return n.uidEvent=t,$e[t]=$e[t]||{},$e[t]}function br(n,t){return function e(s){return hn(s,{delegateTarget:n}),e.oneOff&&l.off(n,s.type,t),t.apply(n,[s])}}function Ar(n,t,e){return function s(i){const r=n.querySelectorAll(t);for(let{target:o}=i;o&&o!==this;o=o.parentNode)for(const a of r)if(a===o)return hn(i,{delegateTarget:o}),s.oneOff&&l.off(n,i.type,t,e),e.apply(o,[i])}}function Ks(n,t,e=null){return Object.values(n).find(s=>s.callable===t&&s.delegationSelector===e)}function Ys(n,t,e){const s=typeof t=="string",i=s?e:t||e;let r=Us(n);return vr.has(r)||(r=n),[s,i,r]}function kn(n,t,e,s,i){if(typeof t!="string"||!n)return;let[r,o,a]=Ys(t,e,s);t in Bs&&(o=(E=>function(p){if(!p.relatedTarget||p.relatedTarget!==p.delegateTarget&&!p.delegateTarget.contains(p.relatedTarget))return E.call(this,p)})(o));const c=Fs(n),d=c[a]||(c[a]={}),u=Ks(d,o,r?e:null);if(u){u.oneOff=u.oneOff&&i;return}const _=js(o,t.replace(mr,"")),m=r?Ar(n,e,o):br(n,o);m.delegationSelector=r?e:null,m.callable=o,m.oneOff=i,m.uidEvent=_,d[_]=m,n.addEventListener(a,m,r)}function Ge(n,t,e,s,i){const r=Ks(t[e],s,i);r&&(n.removeEventListener(e,r,!!i),delete t[e][r.uidEvent])}function Tr(n,t,e,s){const i=t[e]||{};for(const[r,o]of Object.entries(i))r.includes(s)&&Ge(n,t,e,o.callable,o.delegationSelector)}function Us(n){return n=n.replace(gr,""),Bs[n]||n}const l={on(n,t,e,s){kn(n,t,e,s,!1)},one(n,t,e,s){kn(n,t,e,s,!0)},off(n,t,e,s){if(typeof t!="string"||!n)return;const[i,r,o]=Ys(t,e,s),a=o!==t,c=Fs(n),d=c[o]||{},u=t.startsWith(".");if(typeof r!="undefined"){if(!Object.keys(d).length)return;Ge(n,c,o,r,i?e:null);return}if(u)for(const _ of Object.keys(c))Tr(n,c,_,t.slice(1));for(const[_,m]of Object.entries(d)){const h=_.replace(Er,"");(!a||t.includes(h))&&Ge(n,c,o,m.callable,m.delegationSelector)}},trigger(n,t,e){if(typeof t!="string"||!n)return null;const s=Hs(),i=Us(t),r=t!==i;let o=null,a=!0,c=!0,d=!1;r&&s&&(o=s.Event(t,e),s(n).trigger(o),a=!o.isPropagationStopped(),c=!o.isImmediatePropagationStopped(),d=o.isDefaultPrevented());const u=hn(new Event(t,{bubbles:a,cancelable:!0}),e);return d&&u.preventDefault(),c&&n.dispatchEvent(u),u.defaultPrevented&&o&&o.preventDefault(),u}};function hn(n,t={}){for(const[e,s]of Object.entries(t))try{n[e]=s}catch(i){Object.defineProperty(n,e,{configurable:!0,get(){return s}})}return n}function Vn(n){if(n==="true")return!0;if(n==="false")return!1;if(n===Number(n).toString())return Number(n);if(n===""||n==="null")return null;if(typeof n!="string")return n;try{return JSON.parse(decodeURIComponent(n))}catch(t){return n}}function Ie(n){return n.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const Z={setDataAttribute(n,t,e){n.setAttribute(`data-bs-${Ie(t)}`,e)},removeDataAttribute(n,t){n.removeAttribute(`data-bs-${Ie(t)}`)},getDataAttributes(n){if(!n)return{};const t={},e=Object.keys(n.dataset).filter(s=>s.startsWith("bs")&&!s.startsWith("bsConfig"));for(const s of e){let i=s.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),t[i]=Vn(n.dataset[s])}return t},getDataAttribute(n,t){return Vn(n.getAttribute(`data-bs-${Ie(t)}`))}};class Gt{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const s=Q(e)?Z.getDataAttribute(e,"config"):{};return L(L(L(L({},this.constructor.Default),typeof s=="object"?s:{}),Q(e)?Z.getDataAttributes(e):{}),typeof t=="object"?t:{})}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[s,i]of Object.entries(e)){const r=t[s],o=Q(r)?"element":hr(r);if(!new RegExp(i).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${o}" but expected type "${i}".`)}}}const yr="5.3.2";class U extends Gt{constructor(t,e){super(),t=st(t),t&&(this._element=t,this._config=this._getConfig(e),De.set(this._element,this.constructor.DATA_KEY,this))}dispose(){De.remove(this._element,this.constructor.DATA_KEY),l.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,s=!0){Ws(t,e,s)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return De.get(st(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,typeof e=="object"?e:null)}static get VERSION(){return yr}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Pe=n=>{let t=n.getAttribute("data-bs-target");if(!t||t==="#"){let e=n.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),t=e&&e!=="#"?Ms(e.trim()):null}return t},f={find(n,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,n))},findOne(n,t=document.documentElement){return Element.prototype.querySelector.call(t,n)},children(n,t){return[].concat(...n.children).filter(e=>e.matches(t))},parents(n,t){const e=[];let s=n.parentNode.closest(t);for(;s;)e.push(s),s=s.parentNode.closest(t);return e},prev(n,t){let e=n.previousElementSibling;for(;e;){if(e.matches(t))return[e];e=e.previousElementSibling}return[]},next(n,t){let e=n.nextElementSibling;for(;e;){if(e.matches(t))return[e];e=e.nextElementSibling}return[]},focusableChildren(n){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,n).filter(e=>!it(e)&&Mt(e))},getSelectorFromElement(n){const t=Pe(n);return t&&f.findOne(t)?t:null},getElementFromSelector(n){const t=Pe(n);return t?f.findOne(t):null},getMultipleElementsFromSelector(n){const t=Pe(n);return t?f.find(t):[]}},be=(n,t="hide")=>{const e=`click.dismiss${n.EVENT_KEY}`,s=n.NAME;l.on(document,e,`[data-bs-dismiss="${s}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),it(this))return;const r=f.getElementFromSelector(this)||this.closest(`.${s}`);n.getOrCreateInstance(r)[t]()})},wr="alert",Or="bs.alert",zs=`.${Or}`,Cr=`close${zs}`,Nr=`closed${zs}`,Sr="fade",Dr="show";class Ae extends U{static get NAME(){return wr}close(){if(l.trigger(this._element,Cr).defaultPrevented)return;this._element.classList.remove(Dr);const e=this._element.classList.contains(Sr);this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),l.trigger(this._element,Nr),this.dispose()}static jQueryInterface(t){return this.each(function(){const e=Ae.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}be(Ae,"close");j(Ae);const Lr="button",$r="bs.button",Ir=`.${$r}`,Pr=".data-api",Rr="active",Hn='[data-bs-toggle="button"]',xr=`click${Ir}${Pr}`;class Te extends U{static get NAME(){return Lr}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(Rr))}static jQueryInterface(t){return this.each(function(){const e=Te.getOrCreateInstance(this);t==="toggle"&&e[t]()})}}l.on(document,xr,Hn,n=>{n.preventDefault();const t=n.target.closest(Hn);Te.getOrCreateInstance(t).toggle()});j(Te);const Mr="swipe",kt=".bs.swipe",kr=`touchstart${kt}`,Vr=`touchmove${kt}`,Hr=`touchend${kt}`,Wr=`pointerdown${kt}`,Br=`pointerup${kt}`,jr="touch",Fr="pen",Kr="pointer-event",Yr=40,Ur={endCallback:null,leftCallback:null,rightCallback:null},zr={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class _e extends Gt{constructor(t,e){super(),this._element=t,!(!t||!_e.isSupported())&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return Ur}static get DefaultType(){return zr}static get NAME(){return Mr}dispose(){l.off(this._element,kt)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),R(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=Yr)return;const e=t/this._deltaX;this._deltaX=0,e&&R(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(l.on(this._element,Wr,t=>this._start(t)),l.on(this._element,Br,t=>this._end(t)),this._element.classList.add(Kr)):(l.on(this._element,kr,t=>this._start(t)),l.on(this._element,Vr,t=>this._move(t)),l.on(this._element,Hr,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===Fr||t.pointerType===jr)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Gr="carousel",qr="bs.carousel",at=`.${qr}`,Gs=".data-api",Xr="ArrowLeft",Qr="ArrowRight",Zr=500,Bt="next",Tt="prev",Ot="left",de="right",Jr=`slide${at}`,Re=`slid${at}`,to=`keydown${at}`,eo=`mouseenter${at}`,no=`mouseleave${at}`,so=`dragstart${at}`,io=`load${at}${Gs}`,ro=`click${at}${Gs}`,qs="carousel",ie="active",oo="slide",ao="carousel-item-end",co="carousel-item-start",lo="carousel-item-next",uo="carousel-item-prev",Xs=".active",Qs=".carousel-item",ho=Xs+Qs,fo=".carousel-item img",po=".carousel-indicators",_o="[data-bs-slide], [data-bs-slide-to]",mo='[data-bs-ride="carousel"]',go={[Xr]:de,[Qr]:Ot},Eo={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},vo={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class qt extends U{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=f.findOne(po,this._element),this._addEventListeners(),this._config.ride===qs&&this.cycle()}static get Default(){return Eo}static get DefaultType(){return vo}static get NAME(){return Gr}next(){this._slide(Bt)}nextWhenVisible(){!document.hidden&&Mt(this._element)&&this.next()}prev(){this._slide(Tt)}pause(){this._isSliding&&ks(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){l.one(this._element,Re,()=>this.cycle());return}this.cycle()}}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding){l.one(this._element,Re,()=>this.to(t));return}const s=this._getItemIndex(this._getActive());if(s===t)return;const i=t>s?Bt:Tt;this._slide(i,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&l.on(this._element,to,t=>this._keydown(t)),this._config.pause==="hover"&&(l.on(this._element,eo,()=>this.pause()),l.on(this._element,no,()=>this._maybeEnableCycle())),this._config.touch&&_e.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of f.find(fo,this._element))l.on(s,so,i=>i.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(Ot)),rightCallback:()=>this._slide(this._directionToOrder(de)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),Zr+this._config.interval))}};this._swipeHelper=new _e(this._element,e)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=go[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=f.findOne(Xs,this._indicatorsElement);e.classList.remove(ie),e.removeAttribute("aria-current");const s=f.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);s&&(s.classList.add(ie),s.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const s=this._getActive(),i=t===Bt,r=e||dn(this._getItems(),s,i,this._config.wrap);if(r===s)return;const o=this._getItemIndex(r),a=h=>l.trigger(this._element,h,{relatedTarget:r,direction:this._orderToDirection(t),from:this._getItemIndex(s),to:o});if(a(Jr).defaultPrevented||!s||!r)return;const d=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=r;const u=i?co:ao,_=i?lo:uo;r.classList.add(_),zt(r),s.classList.add(u),r.classList.add(u);const m=()=>{r.classList.remove(u,_),r.classList.add(ie),s.classList.remove(ie,_,u),this._isSliding=!1,a(Re)};this._queueCallback(m,s,this._isAnimated()),d&&this.cycle()}_isAnimated(){return this._element.classList.contains(oo)}_getActive(){return f.findOne(ho,this._element)}_getItems(){return f.find(Qs,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return W()?t===Ot?Tt:Bt:t===Ot?Bt:Tt}_orderToDirection(t){return W()?t===Tt?Ot:de:t===Tt?de:Ot}static jQueryInterface(t){return this.each(function(){const e=qt.getOrCreateInstance(this,t);if(typeof t=="number"){e.to(t);return}if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}l.on(document,ro,_o,function(n){const t=f.getElementFromSelector(this);if(!t||!t.classList.contains(qs))return;n.preventDefault();const e=qt.getOrCreateInstance(t),s=this.getAttribute("data-bs-slide-to");if(s){e.to(s),e._maybeEnableCycle();return}if(Z.getDataAttribute(this,"slide")==="next"){e.next(),e._maybeEnableCycle();return}e.prev(),e._maybeEnableCycle()});l.on(window,io,()=>{const n=f.find(mo);for(const t of n)qt.getOrCreateInstance(t)});j(qt);const bo="collapse",Ao="bs.collapse",Xt=`.${Ao}`,To=".data-api",yo=`show${Xt}`,wo=`shown${Xt}`,Oo=`hide${Xt}`,Co=`hidden${Xt}`,No=`click${Xt}${To}`,xe="show",Nt="collapse",re="collapsing",So="collapsed",Do=`:scope .${Nt} .${Nt}`,Lo="collapse-horizontal",$o="width",Io="height",Po=".collapse.show, .collapse.collapsing",qe='[data-bs-toggle="collapse"]',Ro={parent:null,toggle:!0},xo={parent:"(null|element)",toggle:"boolean"};class Yt extends U{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const s=f.find(qe);for(const i of s){const r=f.getSelectorFromElement(i),o=f.find(r).filter(a=>a===this._element);r!==null&&o.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Ro}static get DefaultType(){return xo}static get NAME(){return bo}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(Po).filter(a=>a!==this._element).map(a=>Yt.getOrCreateInstance(a,{toggle:!1}))),t.length&&t[0]._isTransitioning||l.trigger(this._element,yo).defaultPrevented)return;for(const a of t)a.hide();const s=this._getDimension();this._element.classList.remove(Nt),this._element.classList.add(re),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(re),this._element.classList.add(Nt,xe),this._element.style[s]="",l.trigger(this._element,wo)},o=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(i,this._element,!0),this._element.style[s]=`${this._element[o]}px`}hide(){if(this._isTransitioning||!this._isShown()||l.trigger(this._element,Oo).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,zt(this._element),this._element.classList.add(re),this._element.classList.remove(Nt,xe);for(const i of this._triggerArray){const r=f.getElementFromSelector(i);r&&!this._isShown(r)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;const s=()=>{this._isTransitioning=!1,this._element.classList.remove(re),this._element.classList.add(Nt),l.trigger(this._element,Co)};this._element.style[e]="",this._queueCallback(s,this._element,!0)}_isShown(t=this._element){return t.classList.contains(xe)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=st(t.parent),t}_getDimension(){return this._element.classList.contains(Lo)?$o:Io}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(qe);for(const e of t){const s=f.getElementFromSelector(e);s&&this._addAriaAndCollapsedClass([e],this._isShown(s))}}_getFirstLevelChildren(t){const e=f.find(Do,this._config.parent);return f.find(t,this._config.parent).filter(s=>!e.includes(s))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const s of t)s.classList.toggle(So,!e),s.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return typeof t=="string"&&/show|hide/.test(t)&&(e.toggle=!1),this.each(function(){const s=Yt.getOrCreateInstance(this,e);if(typeof t=="string"){if(typeof s[t]=="undefined")throw new TypeError(`No method named "${t}"`);s[t]()}})}}l.on(document,No,qe,function(n){(n.target.tagName==="A"||n.delegateTarget&&n.delegateTarget.tagName==="A")&&n.preventDefault();for(const t of f.getMultipleElementsFromSelector(this))Yt.getOrCreateInstance(t,{toggle:!1}).toggle()});j(Yt);const Wn="dropdown",Mo="bs.dropdown",vt=`.${Mo}`,fn=".data-api",ko="Escape",Bn="Tab",Vo="ArrowUp",jn="ArrowDown",Ho=2,Wo=`hide${vt}`,Bo=`hidden${vt}`,jo=`show${vt}`,Fo=`shown${vt}`,Zs=`click${vt}${fn}`,Js=`keydown${vt}${fn}`,Ko=`keyup${vt}${fn}`,Ct="show",Yo="dropup",Uo="dropend",zo="dropstart",Go="dropup-center",qo="dropdown-center",pt='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Xo=`${pt}.${Ct}`,he=".dropdown-menu",Qo=".navbar",Zo=".navbar-nav",Jo=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",ta=W()?"top-end":"top-start",ea=W()?"top-start":"top-end",na=W()?"bottom-end":"bottom-start",sa=W()?"bottom-start":"bottom-end",ia=W()?"left-start":"right-start",ra=W()?"right-start":"left-start",oa="top",aa="bottom",ca={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},la={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class q extends U{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=f.next(this._element,he)[0]||f.prev(this._element,he)[0]||f.findOne(he,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return ca}static get DefaultType(){return la}static get NAME(){return Wn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(it(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!l.trigger(this._element,jo,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Zo))for(const s of[].concat(...document.body.children))l.on(s,"mouseover",pe);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Ct),this._element.classList.add(Ct),l.trigger(this._element,Fo,t)}}hide(){if(it(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!l.trigger(this._element,Wo,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))l.off(s,"mouseover",pe);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ct),this._element.classList.remove(Ct),this._element.setAttribute("aria-expanded","false"),Z.removeDataAttribute(this._menu,"popper"),l.trigger(this._element,Bo,t)}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!Q(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${Wn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof xs=="undefined")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:Q(this._config.reference)?t=st(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=un(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Ct)}_getPlacement(){const t=this._parent;if(t.classList.contains(Uo))return ia;if(t.classList.contains(zo))return ra;if(t.classList.contains(Go))return oa;if(t.classList.contains(qo))return aa;const e=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(Yo)?e?ea:ta:e?sa:na}_detectNavbar(){return this._element.closest(Qo)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(Z.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),L(L({},t),R(this._config.popperConfig,[t]))}_selectMenuItem({key:t,target:e}){const s=f.find(Jo,this._menu).filter(i=>Mt(i));s.length&&dn(s,e,t===jn,!s.includes(e)).focus()}static jQueryInterface(t){return this.each(function(){const e=q.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}static clearMenus(t){if(t.button===Ho||t.type==="keyup"&&t.key!==Bn)return;const e=f.find(Xo);for(const s of e){const i=q.getInstance(s);if(!i||i._config.autoClose===!1)continue;const r=t.composedPath(),o=r.includes(i._menu);if(r.includes(i._element)||i._config.autoClose==="inside"&&!o||i._config.autoClose==="outside"&&o||i._menu.contains(t.target)&&(t.type==="keyup"&&t.key===Bn||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const a={relatedTarget:i._element};t.type==="click"&&(a.clickEvent=t),i._completeHide(a)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),s=t.key===ko,i=[Vo,jn].includes(t.key);if(!i&&!s||e&&!s)return;t.preventDefault();const r=this.matches(pt)?this:f.prev(this,pt)[0]||f.next(this,pt)[0]||f.findOne(pt,t.delegateTarget.parentNode),o=q.getOrCreateInstance(r);if(i){t.stopPropagation(),o.show(),o._selectMenuItem(t);return}o._isShown()&&(t.stopPropagation(),o.hide(),r.focus())}}l.on(document,Js,pt,q.dataApiKeydownHandler);l.on(document,Js,he,q.dataApiKeydownHandler);l.on(document,Zs,q.clearMenus);l.on(document,Ko,q.clearMenus);l.on(document,Zs,pt,function(n){n.preventDefault(),q.getOrCreateInstance(this).toggle()});j(q);const ti="backdrop",ua="fade",Fn="show",Kn=`mousedown.bs.${ti}`,da={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ha={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class ei extends Gt{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return da}static get DefaultType(){return ha}static get NAME(){return ti}show(t){if(!this._config.isVisible){R(t);return}this._append();const e=this._getElement();this._config.isAnimated&&zt(e),e.classList.add(Fn),this._emulateAnimation(()=>{R(t)})}hide(t){if(!this._config.isVisible){R(t);return}this._getElement().classList.remove(Fn),this._emulateAnimation(()=>{this.dispose(),R(t)})}dispose(){this._isAppended&&(l.off(this._element,Kn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(ua),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=st(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),l.on(t,Kn,()=>{R(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){Ws(t,this._getElement(),this._config.isAnimated)}}const fa="focustrap",pa="bs.focustrap",me=`.${pa}`,_a=`focusin${me}`,ma=`keydown.tab${me}`,ga="Tab",Ea="forward",Yn="backward",va={autofocus:!0,trapElement:null},ba={autofocus:"boolean",trapElement:"element"};class ni extends Gt{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return va}static get DefaultType(){return ba}static get NAME(){return fa}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),l.off(document,me),l.on(document,_a,t=>this._handleFocusin(t)),l.on(document,ma,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,l.off(document,me))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const s=f.focusableChildren(e);s.length===0?e.focus():this._lastTabNavDirection===Yn?s[s.length-1].focus():s[0].focus()}_handleKeydown(t){t.key===ga&&(this._lastTabNavDirection=t.shiftKey?Yn:Ea)}}const Un=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",zn=".sticky-top",oe="padding-right",Gn="margin-right";class Xe{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,oe,e=>e+t),this._setElementAttributes(Un,oe,e=>e+t),this._setElementAttributes(zn,Gn,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,oe),this._resetElementAttributes(Un,oe),this._resetElementAttributes(zn,Gn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,s){const i=this.getWidth(),r=o=>{if(o!==this._element&&window.innerWidth>o.clientWidth+i)return;this._saveInitialAttribute(o,e);const a=window.getComputedStyle(o).getPropertyValue(e);o.style.setProperty(e,`${s(Number.parseFloat(a))}px`)};this._applyManipulationCallback(t,r)}_saveInitialAttribute(t,e){const s=t.style.getPropertyValue(e);s&&Z.setDataAttribute(t,e,s)}_resetElementAttributes(t,e){const s=i=>{const r=Z.getDataAttribute(i,e);if(r===null){i.style.removeProperty(e);return}Z.removeDataAttribute(i,e),i.style.setProperty(e,r)};this._applyManipulationCallback(t,s)}_applyManipulationCallback(t,e){if(Q(t)){e(t);return}for(const s of f.find(t,this._element))e(s)}}const Aa="modal",Ta="bs.modal",B=`.${Ta}`,ya=".data-api",wa="Escape",Oa=`hide${B}`,Ca=`hidePrevented${B}`,si=`hidden${B}`,ii=`show${B}`,Na=`shown${B}`,Sa=`resize${B}`,Da=`click.dismiss${B}`,La=`mousedown.dismiss${B}`,$a=`keydown.dismiss${B}`,Ia=`click${B}${ya}`,qn="modal-open",Pa="fade",Xn="show",Me="modal-static",Ra=".modal.show",xa=".modal-dialog",Ma=".modal-body",ka='[data-bs-toggle="modal"]',Va={backdrop:!0,focus:!0,keyboard:!0},Ha={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Pt extends U{constructor(t,e){super(t,e),this._dialog=f.findOne(xa,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Xe,this._addEventListeners()}static get Default(){return Va}static get DefaultType(){return Ha}static get NAME(){return Aa}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||l.trigger(this._element,ii,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(qn),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||l.trigger(this._element,Oa).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Xn),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){l.off(window,B),l.off(this._dialog,B),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new ei({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ni({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=f.findOne(Ma,this._dialog);e&&(e.scrollTop=0),zt(this._element),this._element.classList.add(Xn);const s=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,l.trigger(this._element,Na,{relatedTarget:t})};this._queueCallback(s,this._dialog,this._isAnimated())}_addEventListeners(){l.on(this._element,$a,t=>{if(t.key===wa){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),l.on(window,Sa,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),l.on(this._element,La,t=>{l.one(this._element,Da,e=>{if(!(this._element!==t.target||this._element!==e.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(qn),this._resetAdjustments(),this._scrollBar.reset(),l.trigger(this._element,si)})}_isAnimated(){return this._element.classList.contains(Pa)}_triggerBackdropTransition(){if(l.trigger(this._element,Ca).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;s==="hidden"||this._element.classList.contains(Me)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Me),this._queueCallback(()=>{this._element.classList.remove(Me),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),s=e>0;if(s&&!t){const i=W()?"paddingLeft":"paddingRight";this._element.style[i]=`${e}px`}if(!s&&t){const i=W()?"paddingRight":"paddingLeft";this._element.style[i]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const s=Pt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof s[t]=="undefined")throw new TypeError(`No method named "${t}"`);s[t](e)}})}}l.on(document,Ia,ka,function(n){const t=f.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&n.preventDefault(),l.one(t,ii,i=>{i.defaultPrevented||l.one(t,si,()=>{Mt(this)&&this.focus()})});const e=f.findOne(Ra);e&&Pt.getInstance(e).hide(),Pt.getOrCreateInstance(t).toggle(this)});be(Pt);j(Pt);const Wa="offcanvas",Ba="bs.offcanvas",tt=`.${Ba}`,ri=".data-api",ja=`load${tt}${ri}`,Fa="Escape",Qn="show",Zn="showing",Jn="hiding",Ka="offcanvas-backdrop",oi=".offcanvas.show",Ya=`show${tt}`,Ua=`shown${tt}`,za=`hide${tt}`,ts=`hidePrevented${tt}`,ai=`hidden${tt}`,Ga=`resize${tt}`,qa=`click${tt}${ri}`,Xa=`keydown.dismiss${tt}`,Qa='[data-bs-toggle="offcanvas"]',Za={backdrop:!0,keyboard:!0,scroll:!1},Ja={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class rt extends U{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Za}static get DefaultType(){return Ja}static get NAME(){return Wa}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||l.trigger(this._element,Ya,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Xe().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Zn);const s=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(Qn),this._element.classList.remove(Zn),l.trigger(this._element,Ua,{relatedTarget:t})};this._queueCallback(s,this._element,!0)}hide(){if(!this._isShown||l.trigger(this._element,za).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Jn),this._backdrop.hide();const e=()=>{this._element.classList.remove(Qn,Jn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Xe().reset(),l.trigger(this._element,ai)};this._queueCallback(e,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){l.trigger(this._element,ts);return}this.hide()},e=!!this._config.backdrop;return new ei({className:Ka,isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?t:null})}_initializeFocusTrap(){return new ni({trapElement:this._element})}_addEventListeners(){l.on(this._element,Xa,t=>{if(t.key===Fa){if(this._config.keyboard){this.hide();return}l.trigger(this._element,ts)}})}static jQueryInterface(t){return this.each(function(){const e=rt.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}l.on(document,qa,Qa,function(n){const t=f.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),it(this))return;l.one(t,ai,()=>{Mt(this)&&this.focus()});const e=f.findOne(oi);e&&e!==t&&rt.getInstance(e).hide(),rt.getOrCreateInstance(t).toggle(this)});l.on(window,ja,()=>{for(const n of f.find(oi))rt.getOrCreateInstance(n).show()});l.on(window,Ga,()=>{for(const n of f.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(n).position!=="fixed"&&rt.getOrCreateInstance(n).hide()});be(rt);j(rt);const tc=/^aria-[\w-]*$/i,ci={"*":["class","dir","id","lang","role",tc],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},ec=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),nc=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,sc=(n,t)=>{const e=n.nodeName.toLowerCase();return t.includes(e)?ec.has(e)?!!nc.test(n.nodeValue):!0:t.filter(s=>s instanceof RegExp).some(s=>s.test(e))};function ic(n,t,e){if(!n.length)return n;if(e&&typeof e=="function")return e(n);const i=new window.DOMParser().parseFromString(n,"text/html"),r=[].concat(...i.body.querySelectorAll("*"));for(const o of r){const a=o.nodeName.toLowerCase();if(!Object.keys(t).includes(a)){o.remove();continue}const c=[].concat(...o.attributes),d=[].concat(t["*"]||[],t[a]||[]);for(const u of c)sc(u,d)||o.removeAttribute(u.nodeName)}return i.body.innerHTML}const rc="TemplateFactory",oc={allowList:ci,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ac={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},cc={entry:"(string|element|function|null)",selector:"(string|element)"};class lc extends Gt{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return oc}static get DefaultType(){return ac}static get NAME(){return rc}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content=L(L({},this._config.content),t),this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[i,r]of Object.entries(this._config.content))this._setContent(t,r,i);const e=t.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&e.classList.add(...s.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,s]of Object.entries(t))super._typeCheckConfig({selector:e,entry:s},cc)}_setContent(t,e,s){const i=f.findOne(s,t);if(i){if(e=this._resolvePossibleFunction(e),!e){i.remove();return}if(Q(e)){this._putElementInTemplate(st(e),i);return}if(this._config.html){i.innerHTML=this._maybeSanitize(e);return}i.textContent=e}}_maybeSanitize(t){return this._config.sanitize?ic(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return R(t,[this])}_putElementInTemplate(t,e){if(this._config.html){e.innerHTML="",e.append(t);return}e.textContent=t.textContent}}const uc="tooltip",dc=new Set(["sanitize","allowList","sanitizeFn"]),ke="fade",hc="modal",ae="show",fc=".tooltip-inner",es=`.${hc}`,ns="hide.bs.modal",jt="hover",Ve="focus",pc="click",_c="manual",mc="hide",gc="hidden",Ec="show",vc="shown",bc="inserted",Ac="click",Tc="focusin",yc="focusout",wc="mouseenter",Oc="mouseleave",Cc={AUTO:"auto",TOP:"top",RIGHT:W()?"left":"right",BOTTOM:"bottom",LEFT:W()?"right":"left"},Nc={allowList:ci,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Sc={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Vt extends U{constructor(t,e){if(typeof xs=="undefined")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Nc}static get DefaultType(){return Sc}static get NAME(){return uc}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),l.off(this._element.closest(es),ns,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=l.trigger(this._element,this.constructor.eventName(Ec)),s=(Vs(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!s)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(i),l.trigger(this._element,this.constructor.eventName(bc))),this._popper=this._createPopper(i),i.classList.add(ae),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))l.on(a,"mouseover",pe);const o=()=>{l.trigger(this._element,this.constructor.eventName(vc)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(o,this.tip,this._isAnimated())}hide(){if(!this._isShown()||l.trigger(this._element,this.constructor.eventName(mc)).defaultPrevented)return;if(this._getTipElement().classList.remove(ae),"ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))l.off(i,"mouseover",pe);this._activeTrigger[pc]=!1,this._activeTrigger[Ve]=!1,this._activeTrigger[jt]=!1,this._isHovered=null;const s=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),l.trigger(this._element,this.constructor.eventName(gc)))};this._queueCallback(s,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(ke,ae),e.classList.add(`bs-${this.constructor.NAME}-auto`);const s=fr(this.constructor.NAME).toString();return e.setAttribute("id",s),this._isAnimated()&&e.classList.add(ke),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new lc(ne(L({},this._config),{content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)})),this._templateFactory}_getContentForTemplate(){return{[fc]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ke)}_isShown(){return this.tip&&this.tip.classList.contains(ae)}_createPopper(t){const e=R(this._config.placement,[this,t,this._element]),s=Cc[e.toUpperCase()];return un(this._element,t,this._getPopperConfig(s))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_resolvePossibleFunction(t){return R(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:s=>{this._getTipElement().setAttribute("data-popper-placement",s.state.placement)}}]};return L(L({},e),R(this._config.popperConfig,[e]))}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if(e==="click")l.on(this._element,this.constructor.eventName(Ac),this._config.selector,s=>{this._initializeOnDelegatedTarget(s).toggle()});else if(e!==_c){const s=e===jt?this.constructor.eventName(wc):this.constructor.eventName(Tc),i=e===jt?this.constructor.eventName(Oc):this.constructor.eventName(yc);l.on(this._element,s,this._config.selector,r=>{const o=this._initializeOnDelegatedTarget(r);o._activeTrigger[r.type==="focusin"?Ve:jt]=!0,o._enter()}),l.on(this._element,i,this._config.selector,r=>{const o=this._initializeOnDelegatedTarget(r);o._activeTrigger[r.type==="focusout"?Ve:jt]=o._element.contains(r.relatedTarget),o._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},l.on(this._element.closest(es),ns,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=Z.getDataAttributes(this._element);for(const s of Object.keys(e))dc.has(s)&&delete e[s];return t=L(L({},e),typeof t=="object"&&t?t:{}),t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:st(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,s]of Object.entries(this._config))this.constructor.Default[e]!==s&&(t[e]=s);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=Vt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}}j(Vt);const Dc="popover",Lc=".popover-header",$c=".popover-body",Ic=ne(L({},Vt.Default),{content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"}),Pc=ne(L({},Vt.DefaultType),{content:"(null|string|element|function)"});class pn extends Vt{static get Default(){return Ic}static get DefaultType(){return Pc}static get NAME(){return Dc}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Lc]:this._getTitle(),[$c]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=pn.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t]()}})}}j(pn);const Rc="scrollspy",xc="bs.scrollspy",_n=`.${xc}`,Mc=".data-api",kc=`activate${_n}`,ss=`click${_n}`,Vc=`load${_n}${Mc}`,Hc="dropdown-item",yt="active",Wc='[data-bs-spy="scroll"]',He="[href]",Bc=".nav, .list-group",is=".nav-link",jc=".nav-item",Fc=".list-group-item",Kc=`${is}, ${jc} > ${is}, ${Fc}`,Yc=".dropdown",Uc=".dropdown-toggle",zc={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Gc={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class ye extends U{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return zc}static get DefaultType(){return Gc}static get NAME(){return Rc}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=st(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(e=>Number.parseFloat(e))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(l.off(this._config.target,ss),l.on(this._config.target,ss,He,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const s=this._rootElement||window,i=e.offsetTop-this._element.offsetTop;if(s.scrollTo){s.scrollTo({top:i,behavior:"smooth"});return}s.scrollTop=i}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),t)}_observerCallback(t){const e=o=>this._targetLinks.get(`#${o.target.id}`),s=o=>{this._previousScrollData.visibleEntryTop=o.target.offsetTop,this._process(e(o))},i=(this._rootElement||document.documentElement).scrollTop,r=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const a=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(r&&a){if(s(o),!i)return;continue}!r&&!a&&s(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=f.find(He,this._config.target);for(const e of t){if(!e.hash||it(e))continue;const s=f.findOne(decodeURI(e.hash),this._element);Mt(s)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,s))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(yt),this._activateParents(t),l.trigger(this._element,kc,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(Hc)){f.findOne(Uc,t.closest(Yc)).classList.add(yt);return}for(const e of f.parents(t,Bc))for(const s of f.prev(e,Kc))s.classList.add(yt)}_clearActiveClass(t){t.classList.remove(yt);const e=f.find(`${He}.${yt}`,t);for(const s of e)s.classList.remove(yt)}static jQueryInterface(t){return this.each(function(){const e=ye.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}l.on(window,Vc,()=>{for(const n of f.find(Wc))ye.getOrCreateInstance(n)});j(ye);const qc="tab",Xc="bs.tab",bt=`.${Xc}`,Qc=`hide${bt}`,Zc=`hidden${bt}`,Jc=`show${bt}`,tl=`shown${bt}`,el=`click${bt}`,nl=`keydown${bt}`,sl=`load${bt}`,il="ArrowLeft",rs="ArrowRight",rl="ArrowUp",os="ArrowDown",We="Home",as="End",_t="active",cs="fade",Be="show",ol="dropdown",li=".dropdown-toggle",al=".dropdown-menu",je=`:not(${li})`,cl='.list-group, .nav, [role="tablist"]',ll=".nav-item, .list-group-item",ul=`.nav-link${je}, .list-group-item${je}, [role="tab"]${je}`,ui='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Fe=`${ul}, ${ui}`,dl=`.${_t}[data-bs-toggle="tab"], .${_t}[data-bs-toggle="pill"], .${_t}[data-bs-toggle="list"]`;class Rt extends U{constructor(t){super(t),this._parent=this._element.closest(cl),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),l.on(this._element,nl,e=>this._keydown(e)))}static get NAME(){return qc}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),s=e?l.trigger(e,Qc,{relatedTarget:t}):null;l.trigger(t,Jc,{relatedTarget:e}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(_t),this._activate(f.getElementFromSelector(t));const s=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(Be);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),l.trigger(t,tl,{relatedTarget:e})};this._queueCallback(s,t,t.classList.contains(cs))}_deactivate(t,e){if(!t)return;t.classList.remove(_t),t.blur(),this._deactivate(f.getElementFromSelector(t));const s=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(Be);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),l.trigger(t,Zc,{relatedTarget:e})};this._queueCallback(s,t,t.classList.contains(cs))}_keydown(t){if(![il,rs,rl,os,We,as].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter(i=>!it(i));let s;if([We,as].includes(t.key))s=e[t.key===We?0:e.length-1];else{const i=[rs,os].includes(t.key);s=dn(e,t.target,i,!0)}s&&(s.focus({preventScroll:!0}),Rt.getOrCreateInstance(s).show())}_getChildren(){return f.find(Fe,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const s of e)this._setInitialAttributesOnChild(s)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),s=this._getOuterElement(t);t.setAttribute("aria-selected",e),s!==t&&this._setAttributeIfNotExists(s,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=f.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const s=this._getOuterElement(t);if(!s.classList.contains(ol))return;const i=(r,o)=>{const a=f.findOne(r,s);a&&a.classList.toggle(o,e)};i(li,_t),i(al,Be),s.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,s){t.hasAttribute(e)||t.setAttribute(e,s)}_elemIsActive(t){return t.classList.contains(_t)}_getInnerElement(t){return t.matches(Fe)?t:f.findOne(Fe,t)}_getOuterElement(t){return t.closest(ll)||t}static jQueryInterface(t){return this.each(function(){const e=Rt.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}l.on(document,el,ui,function(n){["A","AREA"].includes(this.tagName)&&n.preventDefault(),!it(this)&&Rt.getOrCreateInstance(this).show()});l.on(window,sl,()=>{for(const n of f.find(dl))Rt.getOrCreateInstance(n)});j(Rt);const hl="toast",fl="bs.toast",ct=`.${fl}`,pl=`mouseover${ct}`,_l=`mouseout${ct}`,ml=`focusin${ct}`,gl=`focusout${ct}`,El=`hide${ct}`,vl=`hidden${ct}`,bl=`show${ct}`,Al=`shown${ct}`,Tl="fade",ls="hide",ce="show",le="showing",yl={animation:"boolean",autohide:"boolean",delay:"number"},wl={animation:!0,autohide:!0,delay:5e3};class we extends U{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return wl}static get DefaultType(){return yl}static get NAME(){return hl}show(){if(l.trigger(this._element,bl).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(Tl);const e=()=>{this._element.classList.remove(le),l.trigger(this._element,Al),this._maybeScheduleHide()};this._element.classList.remove(ls),zt(this._element),this._element.classList.add(ce,le),this._queueCallback(e,this._element,this._config.animation)}hide(){if(!this.isShown()||l.trigger(this._element,El).defaultPrevented)return;const e=()=>{this._element.classList.add(ls),this._element.classList.remove(le,ce),l.trigger(this._element,vl)};this._element.classList.add(le),this._queueCallback(e,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(ce),super.dispose()}isShown(){return this._element.classList.contains(ce)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=e;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=e;break}}if(e){this._clearTimeout();return}const s=t.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){l.on(this._element,pl,t=>this._onInteraction(t,!0)),l.on(this._element,_l,t=>this._onInteraction(t,!1)),l.on(this._element,ml,t=>this._onInteraction(t,!0)),l.on(this._element,gl,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const e=we.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]=="undefined")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}be(we);j(we);const us={explorer_index:()=>z(()=>Y(void 0,null,function*(){const{setupQueryList:n}=yield import("./query-list.5.3.js");return{setupQueryList:n}}),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(({setupQueryList:n})=>n()),query_detail:()=>z(()=>Y(void 0,null,function*(){const{ExplorerEditor:n}=yield import("./explorer.5.3.js");return{ExplorerEditor:n}}),__vite__mapDeps([5,2,6,3,1,4]),import.meta.url).then(({ExplorerEditor:n})=>new n(document.getElementById("queryIdGlobal").value)),query_create:()=>z(()=>Y(void 0,null,function*(){const{ExplorerEditor:n}=yield import("./explorer.5.3.js");return{ExplorerEditor:n}}),__vite__mapDeps([5,2,6,3,1,4]),import.meta.url).then(({ExplorerEditor:n})=>new n("new")),explorer_playground:()=>z(()=>Y(void 0,null,function*(){const{ExplorerEditor:n}=yield import("./explorer.5.3.js");return{ExplorerEditor:n}}),__vite__mapDeps([5,2,6,3,1,4]),import.meta.url).then(({ExplorerEditor:n})=>new n("new")),explorer_schema:()=>z(()=>Y(void 0,null,function*(){const{setupSchema:n}=yield import("./schema.5.3.js");return{setupSchema:n}}),__vite__mapDeps([7,1,2]),import.meta.url).then(({setupSchema:n})=>n()),explorer_upload_create:()=>z(()=>Y(void 0,null,function*(){const{setupUploads:n}=yield import("./uploads.5.3.js");return{setupUploads:n}}),__vite__mapDeps([8,3]),import.meta.url).then(({setupUploads:n})=>n()),explorer_connection_update:()=>z(()=>Y(void 0,null,function*(){const{setupUploads:n}=yield import("./uploads.5.3.js");return{setupUploads:n}}),__vite__mapDeps([8,3]),import.meta.url).then(({setupUploads:n})=>n()),explorer_connection_create:()=>z(()=>Y(void 0,null,function*(){const{setupUploads:n}=yield import("./uploads.5.3.js");return{setupUploads:n}}),__vite__mapDeps([8,3]),import.meta.url).then(({setupUploads:n})=>n()),table_description_create:()=>z(()=>Y(void 0,null,function*(){const{setupTableDescription:n}=yield import("./tableDescription.5.3.js");return{setupTableDescription:n}}),__vite__mapDeps([9,6,2]),import.meta.url).then(({setupTableDescription:n})=>n()),table_description_update:()=>z(()=>Y(void 0,null,function*(){const{setupTableDescription:n}=yield import("./tableDescription.5.3.js");return{setupTableDescription:n}}),__vite__mapDeps([9,6,2]),import.meta.url).then(({setupTableDescription:n})=>n())};document.addEventListener("DOMContentLoaded",function(){const n=document.getElementById("clientRoute").value;window.baseUrlPath=document.getElementById("baseUrlPath").value,us.hasOwnProperty(n)&&us[n]()});export{Yt as C,Pt as M,Vt as T,z as _};
