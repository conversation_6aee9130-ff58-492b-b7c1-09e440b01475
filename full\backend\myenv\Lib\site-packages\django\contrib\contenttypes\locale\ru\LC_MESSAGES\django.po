# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2012,2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-14 18:22+0000\n"
"Last-Translator: crazyzubr <<EMAIL>>\n"
"Language-Team: Russian (http://www.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Content Types"
msgstr "Типы содержимого"

msgid "python model class name"
msgstr "имя класса модели"

msgid "content type"
msgstr "тип содержимого"

msgid "content types"
msgstr "типы содержимого"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Тип содержимого %(ct_id)s не имеет связанной модели"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Объекта типа %(ct_id)s с идентификатором %(obj_id)s не существует"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Объекты типа \"%(ct_name)s\" не имеют метода get_absolute_url()"
