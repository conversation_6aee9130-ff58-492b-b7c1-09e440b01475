import { useState, ReactNode, useEffect } from "react";
import { apiCall, endpoints } from "../lib/api";
import { User, MeResponse } from "../types/auth";
import { AuthContext } from "./auth-context";

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check token and restore user session on mount
  useEffect(() => {
    async function restoreSession() {
      try {
        const token = localStorage.getItem("token");
        if (!token) {
          setIsLoading(false);
          return;
        }

        // Validate token by fetching fresh user data
        const meResponse = await apiCall<MeResponse>("GET", endpoints.auth.me);
        if (meResponse?.user) {
          setUser(meResponse.user);
          localStorage.setItem("user", JSON.stringify(meResponse.user));
        } else {
          throw new Error("Invalid user data");
        }
      } catch (error) {
        console.error("Failed to restore session:", error);
        // Clear invalid session data
        localStorage.removeItem("token");
        localStorage.removeItem("refresh_token");
        localStorage.removeItem("user");
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    }

    restoreSession();
  }, []);

  const login = async (email: string, password: string): Promise<User> => {
    try {
      console.log("Starting login...");
      // First get tokens
      const tokenResponse = await apiCall<{ access: string; refresh: string }>("POST", endpoints.auth.login, {
        username: email,
        password,
      });
      
      console.log("Token response:", tokenResponse);
      if (!tokenResponse.access || !tokenResponse.refresh) {
        throw new Error("Invalid token response");
      }

      // Store the tokens
      localStorage.setItem("token", tokenResponse.access);
      localStorage.setItem("refresh_token", tokenResponse.refresh);
      
      // Then get user data
      const meResponse = await apiCall<MeResponse>("GET", endpoints.auth.me);
      console.log("User response:", meResponse);
      
      if (!meResponse?.user || !meResponse.user.id) {
        throw new Error("Invalid user data");
      }

      // Store user data
      const userData = meResponse.user;
      localStorage.setItem("user", JSON.stringify(userData));
      setUser(userData);
      return userData;
    } catch (error) {
      console.error("Login failed:", error);
      // Clear any existing tokens on login failure
      localStorage.removeItem("token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("user");
      setUser(null);
      throw error;
    }
  };

  const logout = () => {
    // Clear all stored data
    localStorage.removeItem("token");
    localStorage.removeItem("refresh_token");
    localStorage.removeItem("user");
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}
