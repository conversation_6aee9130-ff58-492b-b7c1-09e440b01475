var c=(s,o,e)=>new Promise((n,a)=>{var i=r=>{try{l(e.next(r))}catch(d){a(d)}},t=r=>{try{l(e.throw(r))}catch(d){a(d)}},l=r=>r.done?n(r.value):Promise.resolve(r.value).then(i,t);l((e=e.apply(s,o)).next())});import{g as f}from"./csrf.5.3.js";function g(){return c(this,null,function*(){let s=this.dataset.id,o=this.dataset.url;try{let a=(yield(yield fetch(o,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":f()},body:JSON.stringify({})})).json()).is_favorite,i=`.query_favorite_toggle[data-id='${s}']`,t=document.querySelector(i);t&&(a?(t.classList.remove("bi-heart"),t.classList.add("bi-heart-fill")):(t.classList.remove("bi-heart-fill"),t.classList.add("bi-heart")))}catch(e){console.error("Error:",e),alert("error")}})}export{g as t};
