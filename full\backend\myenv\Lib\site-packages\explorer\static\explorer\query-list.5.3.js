import{L as m}from"./index.5.3.js";import{g as d}from"./csrf.5.3.js";import{t as u}from"./favorites.5.3.js";import{M as y}from"./main.5.3.js";import"./_commonjsHelpers.5.3.js";function h(){const s=document.querySelector(".search");s&&s.focus()}function f(s){document.querySelector(".search").value.trim()!==""&&document.querySelectorAll(".collapse").forEach(function(r){r.classList.add("show")})}function k(){document.querySelectorAll(".query_favorite_toggle").forEach(function(l){l.addEventListener("click",u)});let s={valueNames:["sort-name","sort-created","sort-created","sort-last-run","sort-run-count","sort-connection"],handlers:{updated:[h],searchComplete:[f]},searchDelay:250,searchColumns:["sort-name"]};new m("queries",s),E()}function E(){let s=new y("#emailCsvModal",{}),l=null,r=function(e){return/^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i.test(e)},c=()=>{const e=document.getElementById("email-success-msg"),t=document.getElementById("email-error-msg");e.style.display="block",t.style.display="none",setTimeout(()=>s.hide(),2e3)},a=e=>{const t=document.getElementById("email-success-msg"),n=document.getElementById("email-error-msg");n.innerHTML=e,t.style.display="none",n.style.display="block"},i=function(e){let t=document.querySelector("#emailCsvInput").value,n=`${window.baseUrlPath}${l}/email_csv?email=${t}`;r(t)?fetch(n,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":d()},body:JSON.stringify({email:t})}).then(o=>{if(!o.ok)throw new Error("Network response was not ok");return o.json()}).then(o=>{c()}).catch(o=>{a(o.message)}):a("Email is invalid")};document.querySelectorAll("#btnSubmitCsvEmail").forEach(function(e){e.addEventListener("click",i)}),document.querySelectorAll(".email-csv").forEach(e=>{e.addEventListener("click",function(t){t.preventDefault(),l=this.getAttribute("data-query-id"),s.show()})}),document.getElementById("emailCsvModal").addEventListener("hidden.bs.modal",e=>{document.getElementById("email-success-msg").style.display="none",document.getElementById("email-error-msg").style.display="none"})}export{k as setupQueryList};
